# متطلبات Python to EXE Converter Pro v2.0
# Python to EXE Converter Pro v2.0 Requirements

# المكتبة الأساسية لتحويل Python إلى EXE
# Core library for converting Python to EXE
PyInstaller>=5.0.0

# مكتبة معالجة الصور (اختيارية لدعم الأيقونات)
# Image processing library (optional for icon support)
Pillow>=9.0.0

# مكتبات Python الأساسية (مدمجة مع Python)
# Core Python libraries (built-in with Python)
# tkinter - للواجهة الرسومية (مدمجة)
# threading - للعمليات المتوازية (مدمجة)
# subprocess - لتشغيل الأوامر (مدمجة)
# json - لحفظ الإعدادات (مدمجة)
# os, sys - للتعامل مع النظام (مدمجة)
# datetime - للتواريخ والأوقات (مدمجة)

# ملاحظات التثبيت:
# Installation Notes:
# 1. تأكد من تثبيت Python 3.7 أو أحدث
#    Make sure Python 3.7+ is installed
# 2. استخدم الأمر: pip install -r requirements.txt
#    Use command: pip install -r requirements.txt
# 3. في حالة مشاكل التثبيت، جرب: pip install --upgrade pip
#    If installation issues, try: pip install --upgrade pip

# مكتبات إضافية اختيارية:
# Optional additional libraries:

# لضغط UPX (يتطلب تحميل UPX منفصل)
# For UPX compression (requires separate UPX download)
# upx - يجب تحميله من: https://upx.github.io/

# لدعم أفضل للأيقونات
# For better icon support
# imageio>=2.0.0

# لتحسين الأداء
# For performance improvements
# psutil>=5.0.0
