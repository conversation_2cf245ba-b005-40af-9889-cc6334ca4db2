#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Logo Design Studio - استوديو تصميم اللوجوهات الاحترافي
واجهة شاملة لتصميم اللوجوهات بالذكاء الاصطناعي والأدوات الاحترافية
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance, ImageOps, ImageTk
from PIL.ImageTk import PhotoImage
import os
import sys
import json
import threading
import time
import math
import random
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from ai_logo_designer_studio import AILogoGenerator, SmartCodeAnalyzer, LogoDesign, AIPrompt

class LogoDesignStudio:
    """استوديو تصميم اللوجوهات الاحترافي"""
    
    def __init__(self, parent, analysis=None, callback=None):
        self.parent = parent
        self.analysis = analysis
        self.callback = callback
        
        # إعدادات الاستوديو
        self.canvas_size = 1024
        self.zoom_level = 0.5  # بدء بزوم 50% لرؤية أفضل
        self.grid_size = 32
        self.show_grid = True
        
        # الطبقات والأدوات
        self.layers = []
        self.current_layer = None
        self.current_tool = "brush"
        self.current_color = "#4169E1"
        self.brush_size = 20
        
        # نظام التراجع
        self.history = []
        self.history_index = -1
        self.max_history = 30
        
        # الذكاء الاصطناعي
        self.ai_generator = AILogoGenerator(callback=self.ai_callback)
        self.analyzer = SmartCodeAnalyzer()
        self.ai_suggestions = []
        self.generated_logos = []
        
        # ألوان الواجهة العصرية
        self.colors = {
            'bg_primary': '#0f0f23',
            'bg_secondary': '#1a1a2e',
            'bg_tertiary': '#16213e',
            'accent_blue': '#6366f1',
            'accent_purple': '#8b5cf6',
            'accent_cyan': '#06b6d4',
            'accent_pink': '#ec4899',
            'accent_green': '#10b981',
            'text_primary': '#ffffff',
            'text_secondary': '#a1a1aa',
            'border': '#374151',
            'success': '#10b981',
            'warning': '#f59e0b',
            'error': '#ef4444'
        }
        
        self.setup_studio()
    
    def setup_studio(self):
        """إعداد واجهة الاستوديو الاحترافي"""
        # النافذة الرئيسية
        self.studio_window = tk.Toplevel(self.parent)
        self.studio_window.title("🎨 Logo Design Studio - استوديو تصميم اللوجوهات الاحترافي")
        self.studio_window.geometry("1600x1000")
        self.studio_window.configure(bg=self.colors['bg_primary'])
        
        # تطبيق نمط عصري
        self.studio_window.attributes('-alpha', 0.98)
        
        # شريط القوائم العصري
        self.create_modern_menu_bar()
        
        # شريط الأدوات الرئيسي
        self.create_main_toolbar()
        
        # المنطقة الرئيسية
        main_frame = tk.Frame(self.studio_window, bg=self.colors['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # لوحة الذكاء الاصطناعي (أعلى)
        self.create_ai_panel(main_frame)
        
        # المنطقة الوسطى
        middle_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
        middle_frame.pack(fill='both', expand=True, pady=10)
        
        # لوحة الأدوات (يسار)
        self.create_tools_panel(middle_frame)
        
        # منطقة الرسم (وسط)
        self.create_canvas_area(middle_frame)
        
        # لوحة الخصائص (يمين)
        self.create_properties_panel(middle_frame)
        
        # شريط الحالة العصري
        self.create_modern_status_bar()
        
        # إنشاء طبقة افتراضية
        self.create_new_layer("Background")
        
        # تحميل اقتراحات الذكاء الاصطناعي
        if self.analysis:
            self.load_ai_suggestions()
    
    def create_modern_menu_bar(self):
        """إنشاء شريط قوائم عصري"""
        # إطار شريط القوائم
        menu_frame = tk.Frame(self.studio_window, bg=self.colors['bg_secondary'], height=40)
        menu_frame.pack(fill='x')
        menu_frame.pack_propagate(False)
        
        # قوائم عصرية
        menus = [
            ("📁 ملف", [
                ("🆕 مشروع جديد", self.new_project),
                ("📂 فتح مشروع", self.open_project),
                ("💾 حفظ مشروع", self.save_project),
                ("📤 تصدير لوجو", self.export_logo),
                ("🖼️ تصدير صورة", self.export_image)
            ]),
            ("✏️ تحرير", [
                ("↶ تراجع", self.undo),
                ("↷ إعادة", self.redo),
                ("📋 نسخ طبقة", self.copy_layer),
                ("📄 لصق طبقة", self.paste_layer),
                ("🗑️ حذف طبقة", self.delete_layer)
            ]),
            ("🗂️ طبقات", [
                ("➕ طبقة جديدة", lambda: self.create_new_layer()),
                ("📋 تكرار طبقة", self.duplicate_layer),
                ("🔗 دمج لأسفل", self.merge_down),
                ("📄 تسطيح الصورة", self.flatten_image)
            ]),
            ("🎨 تأثيرات", [
                ("🌫️ ضبابية", lambda: self.apply_filter("blur")),
                ("⚡ حدة", lambda: self.apply_filter("sharpen")),
                ("🎭 نقش", lambda: self.apply_filter("emboss")),
                ("🔍 كشف الحواف", lambda: self.apply_filter("edge"))
            ]),
            ("🧠 ذكاء اصطناعي", [
                ("🎨 توليد لوجوهات", self.generate_ai_logos),
                ("✨ تحسين الصورة", self.enhance_with_ai),
                ("🎯 إزالة الخلفية", self.remove_background_ai),
                ("🔄 إعادة توليد", self.regenerate_suggestions)
            ])
        ]
        
        for menu_name, menu_items in menus:
            menu_btn = tk.Button(
                menu_frame,
                text=menu_name,
                font=('Segoe UI', 10, 'bold'),
                fg=self.colors['text_primary'],
                bg=self.colors['bg_secondary'],
                relief='flat',
                padx=15,
                pady=8,
                command=lambda items=menu_items: self.show_context_menu(items)
            )
            menu_btn.pack(side='left', padx=2)
            
            # تأثير hover
            menu_btn.bind('<Enter>', lambda e, btn=menu_btn: btn.configure(bg=self.colors['accent_blue']))
            menu_btn.bind('<Leave>', lambda e, btn=menu_btn: btn.configure(bg=self.colors['bg_secondary']))
    
    def create_main_toolbar(self):
        """إنشاء شريط الأدوات الرئيسي العصري"""
        toolbar = tk.Frame(self.studio_window, bg=self.colors['bg_tertiary'], height=60)
        toolbar.pack(fill='x', padx=10, pady=5)
        toolbar.pack_propagate(False)
        
        # مجموعة أدوات الملف
        file_group = tk.Frame(toolbar, bg=self.colors['bg_tertiary'])
        file_group.pack(side='left', padx=15, pady=10)
        
        file_buttons = [
            ("🆕", "مشروع جديد", self.new_project, self.colors['accent_green']),
            ("📂", "فتح مشروع", self.open_project, self.colors['accent_blue']),
            ("💾", "حفظ مشروع", self.save_project, self.colors['accent_purple']),
            ("📤", "تصدير", self.export_logo, self.colors['accent_cyan'])
        ]
        
        for icon, tooltip, command, color in file_buttons:
            btn = self.create_modern_button(file_group, icon, command, color, tooltip)
            btn.pack(side='left', padx=3)
        
        # فاصل عصري
        separator = tk.Frame(toolbar, bg=self.colors['border'], width=2)
        separator.pack(side='left', fill='y', padx=15, pady=10)
        
        # مجموعة أدوات التحرير
        edit_group = tk.Frame(toolbar, bg=self.colors['bg_tertiary'])
        edit_group.pack(side='left', padx=15, pady=10)
        
        edit_buttons = [
            ("↶", "تراجع", self.undo, self.colors['warning']),
            ("↷", "إعادة", self.redo, self.colors['warning']),
            ("📋", "نسخ", self.copy_layer, self.colors['accent_blue']),
            ("📄", "لصق", self.paste_layer, self.colors['accent_blue'])
        ]
        
        for icon, tooltip, command, color in edit_buttons:
            btn = self.create_modern_button(edit_group, icon, command, color, tooltip)
            btn.pack(side='left', padx=3)
        
        # فاصل عصري
        separator2 = tk.Frame(toolbar, bg=self.colors['border'], width=2)
        separator2.pack(side='left', fill='y', padx=15, pady=10)
        
        # مجموعة الذكاء الاصطناعي
        ai_group = tk.Frame(toolbar, bg=self.colors['bg_tertiary'])
        ai_group.pack(side='left', padx=15, pady=10)
        
        ai_buttons = [
            ("🧠", "توليد بالذكاء الاصطناعي", self.generate_ai_logos, self.colors['accent_pink']),
            ("✨", "تحسين ذكي", self.enhance_with_ai, self.colors['accent_purple']),
            ("🎯", "إزالة خلفية", self.remove_background_ai, self.colors['accent_cyan'])
        ]
        
        for icon, tooltip, command, color in ai_buttons:
            btn = self.create_modern_button(ai_group, icon, command, color, tooltip)
            btn.pack(side='left', padx=3)
        
        # معلومات الزوم (يمين)
        zoom_frame = tk.Frame(toolbar, bg=self.colors['bg_tertiary'])
        zoom_frame.pack(side='right', padx=15, pady=10)
        
        self.zoom_label = tk.Label(
            zoom_frame,
            text=f"🔍 {int(self.zoom_level * 100)}%",
            font=('Segoe UI', 11, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_tertiary']
        )
        self.zoom_label.pack(side='right')
    
    def create_modern_button(self, parent, icon, command, color, tooltip=""):
        """إنشاء زر عصري"""
        btn = tk.Button(
            parent,
            text=icon,
            font=('Segoe UI', 14, 'bold'),
            fg='white',
            bg=color,
            relief='flat',
            width=3,
            height=1,
            command=command,
            cursor='hand2'
        )
        
        # تأثيرات hover عصرية
        def on_enter(e):
            btn.configure(bg=self.lighten_color(color, 20))
        
        def on_leave(e):
            btn.configure(bg=color)
        
        btn.bind('<Enter>', on_enter)
        btn.bind('<Leave>', on_leave)
        
        # إضافة tooltip
        if tooltip:
            self.create_tooltip(btn, tooltip)
        
        return btn
    
    def create_ai_panel(self, parent):
        """إنشاء لوحة الذكاء الاصطناعي العصرية"""
        ai_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], relief='solid', bd=1)
        ai_frame.pack(fill='x', pady=(0, 10))
        
        # عنوان اللوحة
        title_frame = tk.Frame(ai_frame, bg=self.colors['bg_secondary'])
        title_frame.pack(fill='x', padx=15, pady=10)
        
        title_label = tk.Label(
            title_frame,
            text="🧠 مولد اللوجوهات بالذكاء الاصطناعي",
            font=('Segoe UI', 14, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        title_label.pack(side='left')
        
        # أزرار التحكم
        controls_frame = tk.Frame(title_frame, bg=self.colors['bg_secondary'])
        controls_frame.pack(side='right')
        
        generate_btn = self.create_modern_button(
            controls_frame, "🎨", self.generate_ai_logos, 
            self.colors['accent_pink'], "توليد لوجوهات جديدة"
        )
        generate_btn.pack(side='left', padx=5)
        
        # المحتوى الرئيسي
        content_frame = tk.Frame(ai_frame, bg=self.colors['bg_secondary'])
        content_frame.pack(fill='both', expand=True, padx=15, pady=(0, 15))
        
        # منطقة الإدخال
        input_frame = tk.Frame(content_frame, bg=self.colors['bg_secondary'])
        input_frame.pack(fill='x', pady=(0, 10))
        
        # تحليل التطبيق
        analysis_frame = tk.LabelFrame(
            input_frame,
            text="📊 تحليل التطبيق",
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        analysis_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        self.analysis_text = tk.Text(
            analysis_frame,
            height=4,
            font=('Segoe UI', 9),
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            relief='flat',
            wrap='word'
        )
        self.analysis_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # إدخال مخصص
        custom_frame = tk.LabelFrame(
            input_frame,
            text="✏️ وصف مخصص",
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        custom_frame.pack(side='right', fill='both', expand=True)
        
        self.custom_prompt_text = tk.Text(
            custom_frame,
            height=4,
            font=('Segoe UI', 9),
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            relief='flat',
            wrap='word'
        )
        self.custom_prompt_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # إدراج نص تلقائي
        self.custom_prompt_text.insert('1.0', "اكتب وصف اللوجو المطلوب هنا...\nمثال: لوجو تطبيق تعليمي للأطفال بألوان زاهية ومرحة")
        
        # منطقة النتائج
        results_frame = tk.Frame(content_frame, bg=self.colors['bg_secondary'])
        results_frame.pack(fill='both', expand=True)
        
        # قائمة الاقتراحات
        suggestions_frame = tk.LabelFrame(
            results_frame,
            text="💡 اقتراحات الذكاء الاصطناعي",
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        suggestions_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        # إطار التمرير للاقتراحات
        suggestions_scroll_frame = tk.Frame(suggestions_frame, bg=self.colors['bg_secondary'])
        suggestions_scroll_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Canvas للاقتراحات مع شريط تمرير
        self.suggestions_canvas = tk.Canvas(
            suggestions_scroll_frame,
            bg=self.colors['bg_tertiary'],
            relief='flat',
            highlightthickness=0
        )
        suggestions_scrollbar = tk.Scrollbar(
            suggestions_scroll_frame,
            orient='vertical',
            command=self.suggestions_canvas.yview
        )
        
        self.suggestions_canvas.configure(yscrollcommand=suggestions_scrollbar.set)
        
        self.suggestions_canvas.pack(side='left', fill='both', expand=True)
        suggestions_scrollbar.pack(side='right', fill='y')
        
        # إطار داخلي للاقتراحات
        self.suggestions_inner_frame = tk.Frame(self.suggestions_canvas, bg=self.colors['bg_tertiary'])
        self.suggestions_canvas.create_window((0, 0), window=self.suggestions_inner_frame, anchor='nw')
        
        # معاينة مفصلة
        preview_frame = tk.LabelFrame(
            results_frame,
            text="👁️ معاينة مفصلة",
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        preview_frame.pack(side='right', fill='both', expand=True)
        
        # منطقة المعاينة
        self.preview_canvas = tk.Canvas(
            preview_frame,
            width=300,
            height=300,
            bg='white',
            relief='solid',
            bd=1
        )
        self.preview_canvas.pack(padx=10, pady=10)
        
        # معلومات المعاينة
        self.preview_info = tk.Label(
            preview_frame,
            text="اختر اقتراح لمعاينته",
            font=('Segoe UI', 9),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_secondary'],
            wraplength=280,
            justify='center'
        )
        self.preview_info.pack(pady=5)
        
        # أزرار المعاينة
        preview_buttons = tk.Frame(preview_frame, bg=self.colors['bg_secondary'])
        preview_buttons.pack(pady=10)
        
        apply_btn = self.create_modern_button(
            preview_buttons, "✅", self.apply_selected_suggestion,
            self.colors['success'], "تطبيق الاقتراح"
        )
        apply_btn.pack(side='left', padx=5)
        
        edit_btn = self.create_modern_button(
            preview_buttons, "✏️", self.edit_selected_suggestion,
            self.colors['accent_blue'], "تحرير الاقتراح"
        )
        edit_btn.pack(side='left', padx=5)
        
        # عرض التحليل الحالي
        self.display_current_analysis()

    def create_tools_panel(self, parent):
        """إنشاء لوحة الأدوات العصرية"""
        tools_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], width=250, relief='solid', bd=1)
        tools_frame.pack(side='left', fill='y', padx=(0, 10))
        tools_frame.pack_propagate(False)

        # عنوان الأدوات
        title_label = tk.Label(
            tools_frame,
            text="🛠️ صندوق الأدوات الاحترافي",
            font=('Segoe UI', 12, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        title_label.pack(pady=15)

        # أدوات الرسم
        drawing_frame = tk.LabelFrame(
            tools_frame,
            text="🎨 أدوات الرسم",
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        drawing_frame.pack(fill='x', padx=10, pady=5)

        drawing_tools = [
            ("🖌️", "brush", "فرشاة احترافية"),
            ("✏️", "pencil", "قلم رصاص دقيق"),
            ("🖍️", "eraser", "ممحاة ذكية"),
            ("🪣", "bucket", "دلو الطلاء"),
            ("💧", "dropper", "قطارة الألوان")
        ]

        self.tool_buttons = {}
        tools_grid = tk.Frame(drawing_frame, bg=self.colors['bg_secondary'])
        tools_grid.pack(padx=5, pady=5)

        for i, (icon, tool_id, tooltip) in enumerate(drawing_tools):
            row, col = i // 3, i % 3
            btn = tk.Button(
                tools_grid,
                text=icon,
                font=('Segoe UI', 16),
                command=lambda t=tool_id: self.select_tool(t),
                bg=self.colors['bg_tertiary'],
                fg=self.colors['text_primary'],
                relief='flat',
                width=4,
                height=2,
                cursor='hand2'
            )
            btn.grid(row=row, column=col, padx=2, pady=2)
            self.tool_buttons[tool_id] = btn
            self.create_tooltip(btn, tooltip)

        # أدوات الأشكال
        shapes_frame = tk.LabelFrame(
            tools_frame,
            text="📐 الأشكال الهندسية",
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        shapes_frame.pack(fill='x', padx=10, pady=5)

        shape_tools = [
            ("📏", "line", "خط مستقيم"),
            ("⬜", "rectangle", "مستطيل"),
            ("⭕", "circle", "دائرة"),
            ("🔺", "triangle", "مثلث"),
            ("⭐", "star", "نجمة"),
            ("💎", "diamond", "معين")
        ]

        shapes_grid = tk.Frame(shapes_frame, bg=self.colors['bg_secondary'])
        shapes_grid.pack(padx=5, pady=5)

        for i, (icon, tool_id, tooltip) in enumerate(shape_tools):
            row, col = i // 3, i % 3
            btn = tk.Button(
                shapes_grid,
                text=icon,
                font=('Segoe UI', 16),
                command=lambda t=tool_id: self.select_tool(t),
                bg=self.colors['bg_tertiary'],
                fg=self.colors['text_primary'],
                relief='flat',
                width=4,
                height=2,
                cursor='hand2'
            )
            btn.grid(row=row, column=col, padx=2, pady=2)
            self.tool_buttons[tool_id] = btn
            self.create_tooltip(btn, tooltip)

        # إعدادات الأداة
        settings_frame = tk.LabelFrame(
            tools_frame,
            text="⚙️ إعدادات الأداة",
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        settings_frame.pack(fill='x', padx=10, pady=5)

        # حجم الفرشاة
        size_frame = tk.Frame(settings_frame, bg=self.colors['bg_secondary'])
        size_frame.pack(fill='x', padx=5, pady=5)

        tk.Label(
            size_frame,
            text="📏 حجم الفرشاة:",
            font=('Segoe UI', 9),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        ).pack(anchor='w')

        self.brush_size_var = tk.IntVar(value=self.brush_size)
        brush_scale = tk.Scale(
            size_frame,
            from_=1,
            to=100,
            orient='horizontal',
            variable=self.brush_size_var,
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            troughcolor=self.colors['bg_tertiary'],
            activebackground=self.colors['accent_blue'],
            command=self.update_brush_size
        )
        brush_scale.pack(fill='x', pady=2)

        # الشفافية
        opacity_frame = tk.Frame(settings_frame, bg=self.colors['bg_secondary'])
        opacity_frame.pack(fill='x', padx=5, pady=5)

        tk.Label(
            opacity_frame,
            text="🌫️ الشفافية:",
            font=('Segoe UI', 9),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        ).pack(anchor='w')

        self.opacity_var = tk.IntVar(value=100)
        opacity_scale = tk.Scale(
            opacity_frame,
            from_=1,
            to=100,
            orient='horizontal',
            variable=self.opacity_var,
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            troughcolor=self.colors['bg_tertiary'],
            activebackground=self.colors['accent_purple']
        )
        opacity_scale.pack(fill='x', pady=2)

        # اختيار اللون العصري
        color_frame = tk.Frame(settings_frame, bg=self.colors['bg_secondary'])
        color_frame.pack(fill='x', padx=5, pady=10)

        tk.Label(
            color_frame,
            text="🎨 اللون الحالي:",
            font=('Segoe UI', 9),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        ).pack(anchor='w')

        color_display_frame = tk.Frame(color_frame, bg=self.colors['bg_secondary'])
        color_display_frame.pack(fill='x', pady=5)

        self.color_display = tk.Label(
            color_display_frame,
            bg=self.current_color,
            width=8,
            height=3,
            relief='solid',
            bd=2,
            cursor='hand2'
        )
        self.color_display.pack(side='left')
        self.color_display.bind('<Button-1>', self.choose_color)

        # ألوان سريعة
        quick_colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF', '#000000', '#FFFFFF']
        quick_frame = tk.Frame(color_display_frame, bg=self.colors['bg_secondary'])
        quick_frame.pack(side='right', padx=10)

        for i, color in enumerate(quick_colors):
            row, col = i // 4, i % 4
            color_btn = tk.Label(
                quick_frame,
                bg=color,
                width=2,
                height=1,
                relief='solid',
                bd=1,
                cursor='hand2'
            )
            color_btn.grid(row=row, column=col, padx=1, pady=1)
            color_btn.bind('<Button-1>', lambda e, c=color: self.set_color(c))

        # تحديد الأداة الافتراضية
        self.select_tool("brush")

    def create_canvas_area(self, parent):
        """إنشاء منطقة الرسم العصرية"""
        canvas_frame = tk.Frame(parent, bg=self.colors['bg_primary'])
        canvas_frame.pack(side='left', fill='both', expand=True)

        # شريط أدوات الرسم
        canvas_toolbar = tk.Frame(canvas_frame, bg=self.colors['bg_tertiary'], height=50)
        canvas_toolbar.pack(fill='x', pady=(0, 10))
        canvas_toolbar.pack_propagate(False)

        # أزرار الزوم العصرية
        zoom_frame = tk.Frame(canvas_toolbar, bg=self.colors['bg_tertiary'])
        zoom_frame.pack(side='left', padx=15, pady=8)

        zoom_buttons = [
            ("🔍+", "تكبير", self.zoom_in, self.colors['accent_green']),
            ("🔍-", "تصغير", self.zoom_out, self.colors['accent_green']),
            ("🎯", "ملائمة", self.zoom_fit, self.colors['accent_blue']),
            ("📐", "حجم فعلي", self.zoom_actual, self.colors['accent_purple'])
        ]

        for icon, tooltip, command, color in zoom_buttons:
            btn = self.create_modern_button(zoom_frame, icon, command, color, tooltip)
            btn.pack(side='left', padx=2)

        # إعدادات الشبكة
        grid_frame = tk.Frame(canvas_toolbar, bg=self.colors['bg_tertiary'])
        grid_frame.pack(side='left', padx=20, pady=8)

        self.grid_var = tk.BooleanVar(value=self.show_grid)
        grid_check = tk.Checkbutton(
            grid_frame,
            text="📐 إظهار الشبكة",
            variable=self.grid_var,
            command=self.toggle_grid,
            font=('Segoe UI', 9),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_tertiary'],
            selectcolor=self.colors['accent_blue'],
            activebackground=self.colors['bg_tertiary'],
            activeforeground=self.colors['text_primary']
        )
        grid_check.pack()

        # معلومات إضافية
        info_frame = tk.Frame(canvas_toolbar, bg=self.colors['bg_tertiary'])
        info_frame.pack(side='right', padx=15, pady=8)

        self.canvas_info = tk.Label(
            info_frame,
            text=f"📐 {self.canvas_size}×{self.canvas_size}px",
            font=('Segoe UI', 9),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_tertiary']
        )
        self.canvas_info.pack()

        # منطقة الرسم مع إطار عصري
        canvas_container = tk.Frame(canvas_frame, bg=self.colors['bg_primary'], relief='solid', bd=2)
        canvas_container.pack(fill='both', expand=True)

        # Canvas للرسم
        display_size = int(self.canvas_size * self.zoom_level)
        self.canvas = tk.Canvas(
            canvas_container,
            bg='white',
            width=display_size,
            height=display_size,
            scrollregion=(0, 0, display_size, display_size),
            relief='flat',
            highlightthickness=0
        )

        # أشرطة التمرير العصرية
        h_scrollbar = tk.Scrollbar(canvas_container, orient='horizontal', command=self.canvas.xview)
        v_scrollbar = tk.Scrollbar(canvas_container, orient='vertical', command=self.canvas.yview)

        self.canvas.configure(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)

        # تخطيط العناصر
        self.canvas.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')

        # ربط أحداث الماوس
        self.canvas.bind('<Button-1>', self.on_canvas_click)
        self.canvas.bind('<B1-Motion>', self.on_canvas_drag)
        self.canvas.bind('<ButtonRelease-1>', self.on_canvas_release)
        self.canvas.bind('<MouseWheel>', self.on_mouse_wheel)
        self.canvas.bind('<Motion>', self.on_mouse_move)

        # رسم الشبكة
        self.draw_grid()

    def create_properties_panel(self, parent):
        """إنشاء لوحة الخصائص العصرية"""
        props_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], width=300, relief='solid', bd=1)
        props_frame.pack(side='right', fill='y', padx=(10, 0))
        props_frame.pack_propagate(False)

        # تبويبات عصرية
        notebook_frame = tk.Frame(props_frame, bg=self.colors['bg_secondary'])
        notebook_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # إنشاء تبويبات مخصصة
        self.create_custom_tabs(notebook_frame)

    def create_custom_tabs(self, parent):
        """إنشاء تبويبات مخصصة عصرية"""
        # شريط التبويبات
        tabs_bar = tk.Frame(parent, bg=self.colors['bg_secondary'])
        tabs_bar.pack(fill='x', pady=(0, 10))

        # محتوى التبويبات
        self.tabs_content = tk.Frame(parent, bg=self.colors['bg_secondary'])
        self.tabs_content.pack(fill='both', expand=True)

        # التبويبات
        self.tabs = {
            'layers': {'name': '🗂️ الطبقات', 'frame': None, 'button': None},
            'properties': {'name': '⚙️ الخصائص', 'frame': None, 'button': None},
            'history': {'name': '📜 التاريخ', 'frame': None, 'button': None},
            'effects': {'name': '🎨 التأثيرات', 'frame': None, 'button': None}
        }

        self.current_tab = 'layers'

        # إنشاء أزرار التبويبات
        for tab_id, tab_info in self.tabs.items():
            btn = tk.Button(
                tabs_bar,
                text=tab_info['name'],
                font=('Segoe UI', 9, 'bold'),
                fg=self.colors['text_primary'],
                bg=self.colors['accent_blue'] if tab_id == self.current_tab else self.colors['bg_tertiary'],
                relief='flat',
                padx=10,
                pady=5,
                command=lambda t=tab_id: self.switch_tab(t)
            )
            btn.pack(side='left', padx=1)
            tab_info['button'] = btn

        # إنشاء محتوى التبويبات
        for tab_id in self.tabs.keys():
            frame = tk.Frame(self.tabs_content, bg=self.colors['bg_secondary'])
            self.tabs[tab_id]['frame'] = frame

            if tab_id == 'layers':
                self.create_layers_tab(frame)
            elif tab_id == 'properties':
                self.create_properties_tab(frame)
            elif tab_id == 'history':
                self.create_history_tab(frame)
            elif tab_id == 'effects':
                self.create_effects_tab(frame)

        # عرض التبويب الحالي
        self.switch_tab(self.current_tab)

    def create_modern_status_bar(self):
        """إنشاء شريط حالة عصري"""
        status_frame = tk.Frame(self.studio_window, bg=self.colors['bg_tertiary'], height=30)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)

        # رسالة الحالة
        self.status_label = tk.Label(
            status_frame,
            text="🎨 جاهز للتصميم",
            font=('Segoe UI', 9),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_tertiary']
        )
        self.status_label.pack(side='left', padx=15, pady=5)

        # معلومات الماوس
        self.mouse_pos_label = tk.Label(
            status_frame,
            text="",
            font=('Segoe UI', 9),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_tertiary']
        )
        self.mouse_pos_label.pack(side='right', padx=15, pady=5)

        # معلومات الطبقة الحالية
        self.layer_info_label = tk.Label(
            status_frame,
            text="",
            font=('Segoe UI', 9),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_tertiary']
        )
        self.layer_info_label.pack(side='right', padx=15, pady=5)

    # ==================== وظائف مساعدة ====================

    def lighten_color(self, color, amount):
        """تفتيح لون"""
        try:
            # تحويل من hex إلى RGB
            color = color.lstrip('#')
            rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))

            # تفتيح اللون
            lightened = tuple(min(255, c + amount) for c in rgb)

            # تحويل إلى hex
            return f"#{lightened[0]:02x}{lightened[1]:02x}{lightened[2]:02x}"
        except:
            return color

    def create_tooltip(self, widget, text):
        """إنشاء tooltip للعنصر"""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")

            label = tk.Label(
                tooltip,
                text=text,
                font=('Segoe UI', 8),
                bg='#2d3748',
                fg='white',
                relief='solid',
                bd=1,
                padx=5,
                pady=3
            )
            label.pack()

            widget.tooltip = tooltip

        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip

        widget.bind('<Enter>', on_enter)
        widget.bind('<Leave>', on_leave)

    def show_context_menu(self, items):
        """عرض قائمة سياق"""
        menu = tk.Menu(self.studio_window, tearoff=0)
        menu.configure(
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            activebackground=self.colors['accent_blue'],
            activeforeground='white'
        )

        for item_text, command in items:
            if item_text == "---":
                menu.add_separator()
            else:
                menu.add_command(label=item_text, command=command)

        try:
            menu.tk_popup(self.studio_window.winfo_pointerx(), self.studio_window.winfo_pointery())
        finally:
            menu.grab_release()

    def display_current_analysis(self):
        """عرض التحليل الحالي"""
        if self.analysis:
            analysis_text = f"""🎯 نوع التطبيق: {self.analysis.app_type}
📝 الوصف: {self.analysis.description}
🏷️ الفئة: {self.analysis.category}
🎨 النمط المقترح: {self.analysis.style_suggestion}
🎭 المزاج: {self.analysis.mood}
📊 مستوى الثقة: {int(self.analysis.confidence * 100)}%

🔑 الكلمات المفتاحية:
{', '.join(self.analysis.keywords[:10])}

🎨 الألوان المقترحة:
{', '.join(self.analysis.color_palette)}"""

            self.analysis_text.delete('1.0', tk.END)
            self.analysis_text.insert('1.0', analysis_text)
        else:
            self.analysis_text.delete('1.0', tk.END)
            self.analysis_text.insert('1.0', "لا يوجد تحليل متاح\nيرجى اختيار ملف للتحليل")

    def ai_callback(self, message):
        """استقبال رسائل من الذكاء الاصطناعي"""
        self.update_status(message)

    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_label.configure(text=message)
        self.studio_window.update_idletasks()

    def update_mouse_position(self, x, y):
        """تحديث موقع الماوس"""
        self.mouse_pos_label.configure(text=f"📍 X: {int(x)}, Y: {int(y)}")

    def update_layer_info(self):
        """تحديث معلومات الطبقة"""
        if self.current_layer:
            info = f"🗂️ {self.current_layer.name} | 🌫️ {int(self.current_layer.opacity * 100)}%"
            self.layer_info_label.configure(text=info)
        else:
            self.layer_info_label.configure(text="")

    # ==================== وظائف الأدوات ====================

    def select_tool(self, tool_name):
        """تحديد أداة"""
        # إعادة تعيين ألوان الأزرار
        for btn in self.tool_buttons.values():
            btn.configure(bg=self.colors['bg_tertiary'])

        # تمييز الأداة المحددة
        if tool_name in self.tool_buttons:
            self.tool_buttons[tool_name].configure(bg=self.colors['accent_blue'])

        self.current_tool = tool_name
        self.update_status(f"🛠️ تم تحديد أداة: {tool_name}")

    def choose_color(self, event=None):
        """اختيار لون"""
        color = colorchooser.askcolor(title="اختر لون", color=self.current_color)[1]
        if color:
            self.set_color(color)

    def set_color(self, color):
        """تعيين لون"""
        self.current_color = color
        self.color_display.configure(bg=color)
        self.update_status(f"🎨 تم تحديد اللون: {color}")

    def update_brush_size(self, value):
        """تحديث حجم الفرشاة"""
        self.brush_size = int(value)
        self.update_status(f"📏 حجم الفرشاة: {self.brush_size}px")

    # ==================== وظائف الرسم ====================

    def on_canvas_click(self, event):
        """النقر على اللوحة"""
        x, y = self.canvas.canvasx(event.x), self.canvas.canvasy(event.y)

        # تحويل إلى إحداثيات الصورة الفعلية
        actual_x = x / self.zoom_level
        actual_y = y / self.zoom_level

        if self.current_tool == "brush":
            self.start_brush_stroke(actual_x, actual_y)
        elif self.current_tool == "pencil":
            self.start_pencil_stroke(actual_x, actual_y)
        elif self.current_tool == "eraser":
            self.start_eraser_stroke(actual_x, actual_y)
        elif self.current_tool == "bucket":
            self.bucket_fill(actual_x, actual_y)
        elif self.current_tool == "dropper":
            self.pick_color(actual_x, actual_y)
        elif self.current_tool in ["line", "rectangle", "circle", "triangle", "star", "diamond"]:
            self.start_shape_draw(actual_x, actual_y)

        self.last_x, self.last_y = actual_x, actual_y

    def on_canvas_drag(self, event):
        """سحب على اللوحة"""
        x, y = self.canvas.canvasx(event.x), self.canvas.canvasy(event.y)
        actual_x = x / self.zoom_level
        actual_y = y / self.zoom_level

        if self.current_tool in ["brush", "pencil", "eraser"]:
            self.continue_stroke(actual_x, actual_y)
        elif self.current_tool in ["line", "rectangle", "circle", "triangle", "star", "diamond"]:
            self.continue_shape_draw(actual_x, actual_y)

        self.last_x, self.last_y = actual_x, actual_y
        self.update_mouse_position(actual_x, actual_y)

    def on_canvas_release(self, event):
        """تحرير الماوس"""
        if self.current_tool in ["brush", "pencil", "eraser"]:
            self.end_stroke()
        elif self.current_tool in ["line", "rectangle", "circle", "triangle", "star", "diamond"]:
            self.end_shape_draw()

    def on_mouse_move(self, event):
        """حركة الماوس"""
        x, y = self.canvas.canvasx(event.x), self.canvas.canvasy(event.y)
        actual_x = x / self.zoom_level
        actual_y = y / self.zoom_level
        self.update_mouse_position(actual_x, actual_y)

    def on_mouse_wheel(self, event):
        """عجلة الماوس للزوم"""
        if event.state & 0x4:  # Ctrl مضغوط
            if event.delta > 0:
                self.zoom_in()
            else:
                self.zoom_out()

    # ==================== وظائف الزوم ====================

    def zoom_in(self):
        """تكبير"""
        self.zoom_level = min(self.zoom_level * 1.5, 5.0)
        self.update_zoom()

    def zoom_out(self):
        """تصغير"""
        self.zoom_level = max(self.zoom_level / 1.5, 0.1)
        self.update_zoom()

    def zoom_fit(self):
        """ملائمة الحجم"""
        # حساب الزوم المناسب للشاشة
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        if canvas_width > 0 and canvas_height > 0:
            zoom_x = canvas_width / self.canvas_size
            zoom_y = canvas_height / self.canvas_size
            self.zoom_level = min(zoom_x, zoom_y) * 0.9  # 90% من الحجم المتاح
        else:
            self.zoom_level = 0.5

        self.update_zoom()

    def zoom_actual(self):
        """الحجم الفعلي"""
        self.zoom_level = 1.0
        self.update_zoom()

    def update_zoom(self):
        """تحديث الزوم"""
        self.zoom_label.configure(text=f"🔍 {int(self.zoom_level * 100)}%")

        # تحديث حجم Canvas
        display_size = int(self.canvas_size * self.zoom_level)
        self.canvas.configure(
            width=display_size,
            height=display_size,
            scrollregion=(0, 0, display_size, display_size)
        )

        # إعادة رسم المحتوى
        self.update_canvas_display()
        self.draw_grid()

    def toggle_grid(self):
        """تبديل عرض الشبكة"""
        self.show_grid = self.grid_var.get()
        if self.show_grid:
            self.draw_grid()
        else:
            self.canvas.delete("grid")

    def draw_grid(self):
        """رسم الشبكة"""
        if self.show_grid:
            self.canvas.delete("grid")

            grid_size = int(self.grid_size * self.zoom_level)
            display_size = int(self.canvas_size * self.zoom_level)

            # خطوط عمودية
            for x in range(0, display_size, grid_size):
                self.canvas.create_line(
                    x, 0, x, display_size,
                    fill='#e2e8f0',
                    width=1,
                    tags="grid"
                )

            # خطوط أفقية
            for y in range(0, display_size, grid_size):
                self.canvas.create_line(
                    0, y, display_size, y,
                    fill='#e2e8f0',
                    width=1,
                    tags="grid"
                )
