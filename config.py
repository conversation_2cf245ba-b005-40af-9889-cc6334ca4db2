#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف التكوين والإعدادات لـ Python to EXE Converter Pro v2.0
Configuration and settings file for Python to EXE Converter Pro v2.0
"""

import os
import sys
from pathlib import Path

class Config:
    """كلاس إعدادات التطبيق"""
    
    # معلومات التطبيق
    APP_NAME = "Python to EXE Converter Pro"
    APP_VERSION = "2.0"
    APP_DESCRIPTION = "واجهة رسومية احترافية بتصميم زجاجي لتحويل ملفات Python إلى تطبيقات تنفيذية"
    APP_AUTHOR = "Python to EXE Converter Pro Team"
    
    # مسارات الملفات
    BASE_DIR = Path(__file__).parent
    SETTINGS_FILE = BASE_DIR / "settings.json"
    LOG_FILE = BASE_DIR / "conversion_log.txt"
    TEMP_DIR = BASE_DIR / "temp"
    
    # إعدادات النافذة الافتراضية
    DEFAULT_WINDOW_SIZE = "1200x700"
    MIN_WINDOW_SIZE = (1000, 600)
    
    # إعدادات PyInstaller الافتراضية
    DEFAULT_PYINSTALLER_OPTIONS = {
        'onefile': True,
        'noconsole': False,
        'debug': False,
        'optimize': False,
        'upx': False,
        'clean': True
    }
    
    # مسارات البحث عن الملفات الرئيسية
    MAIN_FILE_NAMES = [
        'main.py',
        '__main__.py',
        'app.py',
        'run.py',
        'start.py'
    ]
    
    # أنواع الملفات المدعومة
    SUPPORTED_PYTHON_FILES = [
        ("Python files", "*.py"),
        ("All files", "*.*")
    ]
    
    SUPPORTED_ICON_FILES = [
        ("Icon files", "*.ico"),
        ("PNG files", "*.png"),
        ("JPG files", "*.jpg"),
        ("JPEG files", "*.jpeg"),
        ("BMP files", "*.bmp"),
        ("All files", "*.*")
    ]
    
    # رسائل الحالة
    STATUS_MESSAGES = {
        'ready': "✅ جاهز للاستخدام",
        'file_selected': "✅ تم اختيار الملف المصدر",
        'folder_selected': "✅ تم اختيار المجلد المصدر",
        'output_selected': "✅ تم اختيار مجلد الحفظ",
        'icon_selected': "✅ تم اختيار الأيقونة",
        'checking': "🔍 جاري فحص المتطلبات...",
        'converting': "🚀 بدء عملية التحويل...",
        'success': "✅ تم التحويل بنجاح",
        'error': "❌ حدث خطأ",
        'cancelled': "⏹️ تم إلغاء العملية"
    }
    
    # رسائل السجل
    LOG_MESSAGES = {
        'welcome': "🚀 مرحباً بك في Python to EXE Converter Pro v2.0",
        'ready': "📝 جاهز لبدء التحويل...",
        'file_selected': "📄 تم اختيار الملف: {}",
        'folder_selected': "📁 تم اختيار المجلد: {}",
        'main_file_found': "📄 الملف الرئيسي: {}",
        'no_main_file': "⚠️ لم يتم العثور على ملف رئيسي، يرجى تحديد الملف يدوياً",
        'output_selected': "💾 مجلد الحفظ: {}",
        'icon_selected': "🎨 تم اختيار الأيقونة: {}",
        'conversion_start': "🚀 بدء عملية التحويل...",
        'conversion_success': "✅ تم التحويل بنجاح!",
        'conversion_error': "❌ فشل التحويل - رمز الخطأ: {}",
        'files_found': "📁 تم العثور على {} ملف محول",
        'no_files_found': "⚠️ لم يتم العثور على ملفات محولة في المسارات المتوقعة"
    }
    
    # إعدادات التقدم
    PROGRESS_STEPS = {
        'prepare': (10, "📋 بناء الأمر..."),
        'start': (20, "⚙️ تشغيل PyInstaller..."),
        'analyze': (30, "🔍 تحليل التبعيات..."),
        'collect': (50, "📦 جمع الملفات..."),
        'build': (70, "🔨 بناء التطبيق..."),
        'optimize': (85, "⚡ تحسين الملف..."),
        'complete': (100, "✅ تم التحويل بنجاح!")
    }
    
    # أوامر PyInstaller
    PYINSTALLER_COMMANDS = {
        'base': 'pyinstaller',
        'onefile': '--onefile',
        'noconsole': '--noconsole',
        'debug': '--debug=all',
        'optimize': '--optimize=2',
        'icon': '--icon={}',
        'distpath': '--distpath={}',
        'upx': '--upx-dir=upx',
        'clean': '--clean',
        'noconfirm': '--noconfirm'
    }
    
    # متطلبات النظام
    SYSTEM_REQUIREMENTS = {
        'python_min_version': (3, 7),
        'required_packages': [
            'PyInstaller>=5.0.0'
        ],
        'optional_packages': [
            'Pillow>=9.0.0'
        ],
        'min_disk_space_gb': 1,
        'recommended_ram_gb': 4
    }
    
    # إعدادات الواجهة
    UI_SETTINGS = {
        'font_family': 'Segoe UI',
        'font_size': 10,
        'title_font_size': 16,
        'button_width': 20,
        'button_height': 2,
        'entry_width': 40,
        'text_width': 60,
        'text_height': 15,
        'padding': 10
    }
    
    @classmethod
    def get_temp_dir(cls):
        """إنشاء وإرجاع مجلد مؤقت"""
        cls.TEMP_DIR.mkdir(exist_ok=True)
        return cls.TEMP_DIR
    
    @classmethod
    def get_python_version(cls):
        """الحصول على إصدار Python"""
        return f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    
    @classmethod
    def check_python_version(cls):
        """التحقق من إصدار Python"""
        current = sys.version_info[:2]
        required = cls.SYSTEM_REQUIREMENTS['python_min_version']
        return current >= required
    
    @classmethod
    def get_output_paths(cls, source_path, output_dir=None):
        """الحصول على مسارات الملفات المحولة المتوقعة"""
        if not source_path:
            return []
        
        source_name = Path(source_path).stem
        output_dir = output_dir or "dist"
        
        possible_paths = [
            Path(output_dir) / f"{source_name}.exe",
            Path(output_dir) / source_name / f"{source_name}.exe",
            Path("dist") / f"{source_name}.exe",
            Path("dist") / source_name / f"{source_name}.exe"
        ]
        
        return [str(path) for path in possible_paths if path.exists()]
    
    @classmethod
    def validate_source_file(cls, file_path):
        """التحقق من صحة الملف المصدر"""
        if not file_path or not os.path.exists(file_path):
            return False, "الملف غير موجود"
        
        if not file_path.endswith('.py'):
            return False, "الملف ليس ملف Python"
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            compile(code, file_path, 'exec')
            return True, "الملف صحيح"
        except SyntaxError as e:
            return False, f"خطأ في بناء الجملة: {e}"
        except Exception as e:
            return False, f"خطأ في قراءة الملف: {e}"
    
    @classmethod
    def get_file_size_mb(cls, file_path):
        """الحصول على حجم الملف بالميجابايت"""
        try:
            size_bytes = os.path.getsize(file_path)
            return size_bytes / (1024 * 1024)
        except:
            return 0
    
    @classmethod
    def format_file_size(cls, size_mb):
        """تنسيق حجم الملف"""
        if size_mb < 1:
            return f"{size_mb * 1024:.1f} كيلوبايت"
        elif size_mb < 1024:
            return f"{size_mb:.1f} ميجابايت"
        else:
            return f"{size_mb / 1024:.1f} جيجابايت"
