#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد الأيقونات العصرية المتقدم - Advanced Modern Icon Generator
توليد أيقونات عصرية بالذكاء الاصطناعي مع تحويل الصور
"""

import os
import sys
import json
import requests
import base64
import io
import threading
import time
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance, ImageOps
from PIL.ImageTk import PhotoImage
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
try:
    import numpy as np
    HAS_NUMPY = True
except ImportError:
    HAS_NUMPY = False
    np = None
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import random
import math

@dataclass
class ModernIconStyle:
    """نمط الأيقونة العصرية"""
    name: str
    description: str
    colors: List[str]
    gradient_type: str  # linear, radial, conic
    effects: List[str]  # shadow, glow, blur, etc.
    shape_style: str    # rounded, sharp, organic
    texture: str        # flat, glass, metal, plastic

class AdvancedIconGenerator:
    """مولد الأيقونات العصرية المتقدم"""
    
    def __init__(self, callback=None):
        self.callback = callback
        self.generated_icons = []
        self.modern_styles = self._init_modern_styles()
        
        # إعدادات الذكاء الاصطناعي
        self.ai_apis = {
            'stability': {
                'url': 'https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image',
                'key': 'your-stability-key-here'
            },
            'openai': {
                'url': 'https://api.openai.com/v1/images/generations',
                'key': 'your-openai-key-here'
            }
        }
    
    def _init_modern_styles(self):
        """تهيئة الأنماط العصرية"""
        return {
            'glassmorphism': ModernIconStyle(
                name="Glassmorphism",
                description="تأثير زجاجي عصري مع شفافية",
                colors=['#ffffff40', '#ffffff20', '#6366f1', '#8b5cf6'],
                gradient_type="radial",
                effects=['blur', 'glow', 'shadow'],
                shape_style="rounded",
                texture="glass"
            ),
            'neumorphism': ModernIconStyle(
                name="Neumorphism",
                description="تصميم ناعم ثلاثي الأبعاد",
                colors=['#e0e5ec', '#ffffff', '#a3b1c6', '#d1d9e6'],
                gradient_type="linear",
                effects=['inner_shadow', 'outer_shadow'],
                shape_style="rounded",
                texture="soft"
            ),
            'gradient_modern': ModernIconStyle(
                name="Modern Gradient",
                description="تدرجات لونية عصرية زاهية",
                colors=['#667eea', '#764ba2', '#f093fb', '#f5576c'],
                gradient_type="linear",
                effects=['glow', 'shadow'],
                shape_style="rounded",
                texture="flat"
            ),
            'neon_cyber': ModernIconStyle(
                name="Neon Cyber",
                description="نيون سايبر مستقبلي",
                colors=['#00f5ff', '#ff00ff', '#ffff00', '#ff4500'],
                gradient_type="radial",
                effects=['neon_glow', 'electric'],
                shape_style="sharp",
                texture="metal"
            ),
            'minimal_flat': ModernIconStyle(
                name="Minimal Flat",
                description="تصميم مسطح بسيط وأنيق",
                colors=['#3b82f6', '#ef4444', '#10b981', '#f59e0b'],
                gradient_type="none",
                effects=['subtle_shadow'],
                shape_style="rounded",
                texture="flat"
            ),
            'holographic': ModernIconStyle(
                name="Holographic",
                description="تأثير هولوجرافي متقدم",
                colors=['#ff006e', '#8338ec', '#3a86ff', '#06ffa5'],
                gradient_type="conic",
                effects=['rainbow', 'shimmer', 'glow'],
                shape_style="organic",
                texture="hologram"
            )
        }
    
    def log(self, message):
        """إرسال رسالة للواجهة"""
        if self.callback:
            self.callback(message)
        else:
            print(message)
    
    def generate_modern_icons(self, analysis, count=6):
        """توليد أيقونات عصرية متعددة"""
        self.log("🎨 بدء توليد الأيقونات العصرية...")
        
        icons = []
        styles = list(self.modern_styles.keys())
        
        for i in range(count):
            style_name = styles[i % len(styles)]
            style = self.modern_styles[style_name]
            
            # توليد أيقونة بناءً على التحليل والنمط
            icon_data = self._generate_single_modern_icon(analysis, style, i)
            if icon_data:
                icons.append(icon_data)
        
        self.generated_icons = icons
        self.log(f"✨ تم توليد {len(icons)} أيقونة عصرية")
        
        return icons
    
    def _generate_single_modern_icon(self, analysis, style, index):
        """توليد أيقونة واحدة عصرية"""
        try:
            # إنشاء صورة 512x512 للجودة العالية
            size = 512
            image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
            draw = ImageDraw.Draw(image)
            
            # تطبيق النمط العصري
            if style.name == "Glassmorphism":
                image = self._create_glassmorphism_icon(analysis, style, size)
            elif style.name == "Neumorphism":
                image = self._create_neumorphism_icon(analysis, style, size)
            elif style.name == "Modern Gradient":
                image = self._create_gradient_icon(analysis, style, size)
            elif style.name == "Neon Cyber":
                image = self._create_neon_icon(analysis, style, size)
            elif style.name == "Minimal Flat":
                image = self._create_flat_icon(analysis, style, size)
            elif style.name == "Holographic":
                image = self._create_holographic_icon(analysis, style, size)
            
            # إنشاء بيانات الأيقونة
            icon_data = {
                'name': f"{style.name} {analysis.app_type}",
                'description': f"أيقونة {style.description} لتطبيق {analysis.app_type}",
                'style': style.name,
                'image': image,
                'colors': style.colors,
                'confidence': 0.9 - (index * 0.1),
                'modern': True
            }
            
            return icon_data
            
        except Exception as e:
            self.log(f"خطأ في توليد الأيقونة: {e}")
            return None
    
    def _create_glassmorphism_icon(self, analysis, style, size):
        """إنشاء أيقونة Glassmorphism"""
        image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)
        
        # خلفية زجاجية
        center = size // 2
        radius = size // 3
        
        # تدرج زجاجي
        for i in range(radius):
            alpha = int(80 - (i * 60 / radius))
            color = (*self._hex_to_rgb(style.colors[2]), alpha)
            draw.ellipse([center-radius+i, center-radius+i, 
                         center+radius-i, center+radius-i], 
                        fill=color)
        
        # إضافة رمز بناءً على نوع التطبيق
        self._add_app_symbol(draw, analysis, center, radius//2, style.colors[0])
        
        # تأثير التوهج
        image = self._add_glow_effect(image, style.colors[2])
        
        return image
    
    def _create_neumorphism_icon(self, analysis, style, size):
        """إنشاء أيقونة Neumorphism"""
        image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)
        
        # خلفية ناعمة
        bg_color = self._hex_to_rgb(style.colors[0])
        image.paste(bg_color, [0, 0, size, size])
        
        # شكل ناعم مرتفع
        center = size // 2
        radius = size // 3
        
        # ظل خارجي
        shadow_offset = 10
        shadow_color = self._hex_to_rgb(style.colors[2])
        draw.ellipse([center-radius+shadow_offset, center-radius+shadow_offset,
                     center+radius+shadow_offset, center+radius+shadow_offset],
                    fill=shadow_color)
        
        # الشكل الرئيسي
        main_color = self._hex_to_rgb(style.colors[1])
        draw.ellipse([center-radius, center-radius, center+radius, center+radius],
                    fill=main_color)
        
        # ظل داخلي (محاكاة)
        inner_radius = radius - 15
        inner_color = self._hex_to_rgb(style.colors[3])
        draw.ellipse([center-inner_radius, center-inner_radius,
                     center+inner_radius, center+inner_radius],
                    fill=inner_color)
        
        # رمز التطبيق
        self._add_app_symbol(draw, analysis, center, radius//3, style.colors[2])
        
        return image
    
    def _create_gradient_icon(self, analysis, style, size):
        """إنشاء أيقونة بتدرج عصري"""
        image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        
        # إنشاء تدرج لوني
        gradient = self._create_gradient(size, style.colors[:2], 'linear')
        
        # قناع دائري
        mask = Image.new('L', (size, size), 0)
        mask_draw = ImageDraw.Draw(mask)
        center = size // 2
        radius = size // 3
        mask_draw.ellipse([center-radius, center-radius, center+radius, center+radius], fill=255)
        
        # تطبيق القناع
        image.paste(gradient, (0, 0))
        image.putalpha(mask)
        
        # إضافة رمز
        draw = ImageDraw.Draw(image)
        self._add_app_symbol(draw, analysis, center, radius//2, '#ffffff')
        
        # تأثير التوهج
        image = self._add_glow_effect(image, style.colors[1])
        
        return image
    
    def _create_neon_icon(self, analysis, style, size):
        """إنشاء أيقونة نيون سايبر"""
        image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)
        
        center = size // 2
        radius = size // 3
        
        # خلفية داكنة
        draw.ellipse([center-radius, center-radius, center+radius, center+radius],
                    fill=(20, 20, 40, 200))
        
        # حلقات نيون
        for i, color in enumerate(style.colors[:3]):
            ring_radius = radius - (i * 15)
            if ring_radius > 0:
                # حلقة نيون
                for thickness in range(5):
                    alpha = 255 - (thickness * 40)
                    neon_color = (*self._hex_to_rgb(color), alpha)
                    draw.ellipse([center-ring_radius-thickness, center-ring_radius-thickness,
                                 center+ring_radius+thickness, center+ring_radius+thickness],
                                outline=neon_color, width=2)
        
        # رمز نيون
        self._add_app_symbol(draw, analysis, center, radius//3, style.colors[0])
        
        # تأثير كهربائي
        image = self._add_electric_effect(image, style.colors[0])
        
        return image
    
    def _create_flat_icon(self, analysis, style, size):
        """إنشاء أيقونة مسطحة عصرية"""
        image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)
        
        center = size // 2
        radius = size // 3
        
        # شكل مسطح بسيط
        main_color = self._hex_to_rgb(style.colors[0])
        draw.ellipse([center-radius, center-radius, center+radius, center+radius],
                    fill=main_color)
        
        # ظل خفيف
        shadow_color = (*main_color, 100)
        draw.ellipse([center-radius+5, center-radius+5, center+radius+5, center+radius+5],
                    fill=shadow_color)
        
        # الشكل الرئيسي مرة أخرى
        draw.ellipse([center-radius, center-radius, center+radius, center+radius],
                    fill=main_color)
        
        # رمز بسيط
        self._add_app_symbol(draw, analysis, center, radius//2, '#ffffff')
        
        return image
    
    def _create_holographic_icon(self, analysis, style, size):
        """إنشاء أيقونة هولوجرافية"""
        image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        
        # تدرج هولوجرافي متعدد الألوان
        gradient = self._create_rainbow_gradient(size, style.colors)
        
        # قناع دائري
        mask = Image.new('L', (size, size), 0)
        mask_draw = ImageDraw.Draw(mask)
        center = size // 2
        radius = size // 3
        mask_draw.ellipse([center-radius, center-radius, center+radius, center+radius], fill=255)
        
        # تطبيق القناع
        image.paste(gradient, (0, 0))
        image.putalpha(mask)
        
        # تأثير التشويش الهولوجرافي
        image = self._add_holographic_effect(image)
        
        # رمز
        draw = ImageDraw.Draw(image)
        self._add_app_symbol(draw, analysis, center, radius//2, '#ffffff')
        
        return image
    
    def _add_app_symbol(self, draw, analysis, x, y, color):
        """إضافة رمز التطبيق"""
        color_rgb = self._hex_to_rgb(color) if isinstance(color, str) else color
        
        if analysis.app_type == 'GUI':
            # نافذة
            draw.rectangle([x-20, y-15, x+20, y+15], outline=color_rgb, width=3)
            draw.rectangle([x-18, y-13, x+18, y-8], fill=color_rgb)
        elif analysis.app_type == 'Web':
            # كرة أرضية
            draw.ellipse([x-20, y-20, x+20, y+20], outline=color_rgb, width=3)
            draw.arc([x-15, y-15, x+15, y+15], 0, 180, fill=color_rgb, width=2)
            draw.line([x, y-20, x, y+20], fill=color_rgb, width=2)
        elif analysis.app_type == 'Game':
            # يد تحكم
            draw.ellipse([x-25, y-10, x+25, y+10], outline=color_rgb, width=3)
            draw.ellipse([x-15, y-15, x-10, y-10], fill=color_rgb)
            draw.ellipse([x+10, y-15, x+15, y-10], fill=color_rgb)
        elif analysis.app_type == 'Data':
            # مخطط بياني
            draw.rectangle([x-15, y+5, x-10, y+15], fill=color_rgb)
            draw.rectangle([x-5, y-5, x, y+15], fill=color_rgb)
            draw.rectangle([x+5, y-15, x+10, y+15], fill=color_rgb)
        else:
            # رمز افتراضي
            draw.ellipse([x-15, y-15, x+15, y+15], fill=color_rgb)
    
    def _hex_to_rgb(self, hex_color):
        """تحويل من hex إلى RGB"""
        hex_color = hex_color.lstrip('#')
        if len(hex_color) == 8:  # مع alpha
            return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4, 6))
        else:  # بدون alpha
            return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def _create_gradient(self, size, colors, direction='linear'):
        """إنشاء تدرج لوني"""
        image = Image.new('RGBA', (size, size))
        draw = ImageDraw.Draw(image)
        
        color1 = self._hex_to_rgb(colors[0])
        color2 = self._hex_to_rgb(colors[1])
        
        if direction == 'linear':
            for y in range(size):
                ratio = y / size
                r = int(color1[0] + (color2[0] - color1[0]) * ratio)
                g = int(color1[1] + (color2[1] - color1[1]) * ratio)
                b = int(color1[2] + (color2[2] - color1[2]) * ratio)
                draw.line([(0, y), (size, y)], fill=(r, g, b))
        
        return image
    
    def _create_rainbow_gradient(self, size, colors):
        """إنشاء تدرج قوس قزح"""
        image = Image.new('RGBA', (size, size))
        draw = ImageDraw.Draw(image)
        
        for y in range(size):
            for x in range(size):
                # حساب الزاوية للتدرج الدائري
                angle = math.atan2(y - size//2, x - size//2)
                normalized_angle = (angle + math.pi) / (2 * math.pi)
                
                # اختيار اللون بناءً على الزاوية
                color_index = int(normalized_angle * len(colors))
                color_index = min(color_index, len(colors) - 1)
                
                color = self._hex_to_rgb(colors[color_index])
                draw.point((x, y), fill=color)
        
        return image
    
    def _add_glow_effect(self, image, color):
        """إضافة تأثير التوهج"""
        # إنشاء قناع للتوهج
        glow = image.copy()
        glow = glow.filter(ImageFilter.GaussianBlur(radius=10))
        
        # دمج التوهج مع الصورة الأصلية
        result = Image.alpha_composite(glow, image)
        return result
    
    def _add_electric_effect(self, image, color):
        """إضافة تأثير كهربائي"""
        # محاكاة بسيطة للتأثير الكهربائي
        enhanced = ImageEnhance.Brightness(image).enhance(1.3)
        enhanced = ImageEnhance.Contrast(enhanced).enhance(1.2)
        return enhanced
    
    def _add_holographic_effect(self, image):
        """إضافة تأثير هولوجرافي"""
        # تأثير تشويش خفيف
        enhanced = ImageEnhance.Color(image).enhance(1.5)
        enhanced = ImageEnhance.Brightness(enhanced).enhance(1.1)
        return enhanced
