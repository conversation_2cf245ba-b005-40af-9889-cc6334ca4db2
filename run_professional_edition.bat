@echo off
chcp 65001 >nul
title Python to EXE Converter Pro v2.0 - Professional AI Icon Designer Edition

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║    🎨 Python to EXE Converter Pro v2.0 - Professional Edition 🎨           ║
echo ║                                                                              ║
echo ║    ✨ محرر أيقونات احترافي مع ذكاء اصطناعي متقدم ✨                        ║
echo ║                                                                              ║
echo ║    🧠 AI Image Generation    🎨 Professional Icon Editor                    ║
echo ║    🖼️ Smart Image Converter   ⚡ Advanced Filters                          ║
echo ║    📐 Multi-Layer Support    🎯 Smart Background Removal                    ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🚀 تشغيل النسخة الاحترافية...
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت أو غير موجود في PATH
    echo 📥 يرجى تثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python

REM التحقق من المكتبات المطلوبة
echo 🔍 فحص المكتبات المطلوبة...

python -c "import tkinter" >nul 2>&1
if errorlevel 1 (
    echo ❌ مكتبة tkinter غير متوفرة
    pause
    exit /b 1
)

python -c "import PIL" >nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت مكتبة Pillow...
    pip install Pillow
)

python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت مكتبة requests...
    pip install requests
)

echo ✅ جميع المكتبات جاهزة

echo.
echo 🎨 بدء تشغيل المحرر الاحترافي...
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                           🎨 المميزات الجديدة 🎨                           ║
echo ║                                                                              ║
echo ║  🧠 ذكاء اصطناعي متقدم:                                                    ║
echo ║     • توليد صور مخصصة بناءً على تحليل الكود                               ║
echo ║     • اقتراحات ذكية للأيقونات والصور                                      ║
echo ║     • تحسين الصور تلقائياً                                                 ║
echo ║     • إزالة الخلفية بالذكاء الاصطناعي                                      ║
echo ║                                                                              ║
echo ║  🎨 محرر احترافي متقدم:                                                     ║
echo ║     • أدوات رسم متطورة (فرشاة، قلم، ممحاة، أشكال)                         ║
echo ║     • نظام طبقات متقدم مع تأثيرات                                          ║
echo ║     • فلاتر وتأثيرات بصرية احترافية                                       ║
echo ║     • أدوات تحديد وقص متقدمة                                               ║
echo ║     • نظام تراجع/إعادة شامل                                                ║
echo ║     • حفظ وتحميل المشاريع                                                  ║
echo ║                                                                              ║
echo ║  🖼️ محول صور ذكي:                                                          ║
echo ║     • سحب وإفلات مباشر                                                     ║
echo ║     • تحسين تلقائي للصور                                                   ║
echo ║     • تأثيرات عصرية متقدمة                                                 ║
echo ║     • معاينة مباشرة بأحجام متعددة                                          ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM تشغيل التطبيق
python modern_pyinstaller_gui.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل التطبيق
    echo 🔧 تحقق من:
    echo    • تثبيت جميع المكتبات المطلوبة
    echo    • صحة ملفات التطبيق
    echo    • إصدار Python المتوافق
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ تم إغلاق التطبيق بنجاح
echo 👋 شكراً لاستخدام Python to EXE Converter Pro!
echo.
pause
