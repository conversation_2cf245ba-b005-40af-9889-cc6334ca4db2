#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI Logo Designer Studio - استوديو تصميم اللوجوهات بالذكاء الاصطناعي
نظام متكامل لتصميم اللوجوهات والأيقونات بالذكاء الاصطناعي
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance, ImageOps
from PIL.ImageTk import PhotoImage
import os
import sys
import json
import requests
import base64
import io
import threading
import time
import math
import random
import hashlib
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import subprocess

@dataclass
class AIPrompt:
    """وصف للذكاء الاصطناعي"""
    text: str
    style: str
    colors: List[str]
    mood: str
    category: str
    keywords: List[str]
    quality: str = "high"
    size: str = "1024x1024"

@dataclass
class LogoDesign:
    """تصميم لوجو"""
    name: str
    image: Image.Image
    prompt: AIPrompt
    confidence: float
    source: str
    metadata: Dict

class SmartCodeAnalyzer:
    """محلل الكود الذكي المتقدم"""
    
    def __init__(self):
        self.analysis_cache = {}
        
        # قاعدة بيانات أنواع التطبيقات المتقدمة
        self.app_patterns = {
            'GUI': {
                'keywords': ['tkinter', 'pyqt', 'pyside', 'kivy', 'wxpython', 'gui', 'window', 'button', 'label'],
                'description': 'تطبيق واجهة رسومية',
                'style': 'modern interface design',
                'colors': ['#4169E1', '#32CD32', '#FF6347'],
                'mood': 'professional, user-friendly'
            },
            'Web': {
                'keywords': ['flask', 'django', 'fastapi', 'streamlit', 'web', 'html', 'css', 'javascript'],
                'description': 'تطبيق ويب',
                'style': 'web application design',
                'colors': ['#FF4500', '#1E90FF', '#32CD32'],
                'mood': 'modern, accessible'
            },
            'Game': {
                'keywords': ['pygame', 'arcade', 'game', 'player', 'score', 'level', 'sprite'],
                'description': 'لعبة',
                'style': 'gaming design',
                'colors': ['#8A2BE2', '#FF1493', '#00CED1'],
                'mood': 'fun, exciting, energetic'
            },
            'Data Science': {
                'keywords': ['pandas', 'numpy', 'matplotlib', 'seaborn', 'sklearn', 'tensorflow', 'pytorch'],
                'description': 'تطبيق علوم البيانات',
                'style': 'data visualization design',
                'colors': ['#2E8B57', '#4682B4', '#FF8C00'],
                'mood': 'analytical, professional'
            },
            'AI/ML': {
                'keywords': ['machine learning', 'neural network', 'ai', 'deep learning', 'model', 'training'],
                'description': 'تطبيق ذكاء اصطناعي',
                'style': 'futuristic AI design',
                'colors': ['#6366f1', '#8b5cf6', '#06b6d4'],
                'mood': 'innovative, high-tech'
            },
            'Finance': {
                'keywords': ['bank', 'money', 'finance', 'trading', 'investment', 'crypto', 'payment'],
                'description': 'تطبيق مالي',
                'style': 'financial corporate design',
                'colors': ['#2E8B57', '#FFD700', '#1E90FF'],
                'mood': 'trustworthy, professional'
            },
            'Education': {
                'keywords': ['learn', 'education', 'student', 'course', 'quiz', 'study', 'school'],
                'description': 'تطبيق تعليمي',
                'style': 'educational friendly design',
                'colors': ['#4169E1', '#FF6347', '#32CD32'],
                'mood': 'friendly, approachable'
            },
            'Health': {
                'keywords': ['health', 'medical', 'doctor', 'patient', 'medicine', 'fitness', 'wellness'],
                'description': 'تطبيق صحي',
                'style': 'medical clean design',
                'colors': ['#DC143C', '#00FA9A', '#4682B4'],
                'mood': 'clean, trustworthy'
            },
            'Social': {
                'keywords': ['social', 'chat', 'message', 'friend', 'community', 'share', 'connect'],
                'description': 'تطبيق اجتماعي',
                'style': 'social media design',
                'colors': ['#FF69B4', '#00BFFF', '#32CD32'],
                'mood': 'friendly, connecting'
            },
            'Productivity': {
                'keywords': ['todo', 'task', 'organize', 'calendar', 'note', 'productivity', 'work'],
                'description': 'تطبيق إنتاجية',
                'style': 'productivity clean design',
                'colors': ['#696969', '#FF8C00', '#6495ED'],
                'mood': 'organized, efficient'
            }
        }
    
    def analyze_code_advanced(self, file_path_or_text):
        """تحليل متقدم للكود"""
        try:
            # قراءة الكود
            if os.path.isfile(file_path_or_text):
                with open(file_path_or_text, 'r', encoding='utf-8', errors='ignore') as f:
                    code_content = f.read().lower()
                file_name = os.path.basename(file_path_or_text)
            else:
                code_content = file_path_or_text.lower()
                file_name = "user_input"
            
            # تحليل نوع التطبيق
            app_type = self._detect_app_type(code_content, file_name)
            app_info = self.app_patterns.get(app_type, self.app_patterns['GUI'])
            
            # استخراج الكلمات المفتاحية
            keywords = self._extract_keywords(code_content)
            
            # تحليل الوظائف
            functions = self._extract_functions(code_content)
            
            # تحليل المكتبات
            libraries = self._extract_libraries(code_content)
            
            # تحليل التعقيد
            complexity = self._analyze_complexity(code_content)
            
            # إنشاء وصف ذكي
            smart_description = self._generate_smart_description(
                app_type, keywords, functions, libraries, file_name
            )
            
            # إنشاء كائن التحليل
            analysis = type('Analysis', (), {
                'app_type': app_type,
                'description': smart_description,
                'keywords': keywords,
                'functions': functions,
                'libraries': libraries,
                'complexity': complexity,
                'style_suggestion': app_info['style'],
                'color_palette': app_info['colors'],
                'mood': app_info['mood'],
                'category': app_info['description'],
                'confidence': self._calculate_confidence(code_content, app_type)
            })()
            
            return analysis
            
        except Exception as e:
            # تحليل افتراضي في حالة الخطأ
            return self._create_default_analysis(str(e))
    
    def _detect_app_type(self, code_content, file_name):
        """اكتشاف نوع التطبيق"""
        scores = {}
        
        for app_type, info in self.app_patterns.items():
            score = 0
            for keyword in info['keywords']:
                score += code_content.count(keyword) * 10
            
            # إضافة نقاط بناءً على اسم الملف
            if any(word in file_name.lower() for word in info['keywords']):
                score += 20
            
            scores[app_type] = score
        
        # العثور على أعلى نقاط
        best_type = max(scores, key=scores.get)
        return best_type if scores[best_type] > 0 else 'GUI'
    
    def _extract_keywords(self, code_content):
        """استخراج الكلمات المفتاحية"""
        # كلمات مفتاحية شائعة في البرمجة
        common_keywords = [
            'app', 'application', 'program', 'software', 'tool', 'system',
            'manager', 'editor', 'viewer', 'player', 'calculator', 'converter',
            'analyzer', 'generator', 'creator', 'designer', 'builder'
        ]
        
        found_keywords = []
        for keyword in common_keywords:
            if keyword in code_content:
                found_keywords.append(keyword)
        
        return found_keywords[:10]  # أهم 10 كلمات
    
    def _extract_functions(self, code_content):
        """استخراج أسماء الوظائف"""
        import re
        
        # البحث عن تعريفات الوظائف
        function_pattern = r'def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\('
        functions = re.findall(function_pattern, code_content)
        
        return functions[:15]  # أهم 15 وظيفة
    
    def _extract_libraries(self, code_content):
        """استخراج المكتبات المستخدمة"""
        import re
        
        # البحث عن استيرادات
        import_patterns = [
            r'import\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            r'from\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+import'
        ]
        
        libraries = []
        for pattern in import_patterns:
            matches = re.findall(pattern, code_content)
            libraries.extend(matches)
        
        return list(set(libraries))[:10]  # أهم 10 مكتبات
    
    def _analyze_complexity(self, code_content):
        """تحليل تعقيد الكود"""
        lines = code_content.split('\n')
        non_empty_lines = [line for line in lines if line.strip()]
        
        if len(non_empty_lines) < 50:
            return 'simple'
        elif len(non_empty_lines) < 200:
            return 'medium'
        else:
            return 'complex'
    
    def _generate_smart_description(self, app_type, keywords, functions, libraries, file_name):
        """توليد وصف ذكي للتطبيق"""
        app_info = self.app_patterns.get(app_type, self.app_patterns['GUI'])
        base_description = app_info['description']
        
        # إضافة تفاصيل بناءً على التحليل
        details = []
        
        if 'calculator' in keywords or 'calc' in file_name.lower():
            details.append('للحسابات الرياضية')
        elif 'editor' in keywords or 'edit' in file_name.lower():
            details.append('لتحرير النصوص أو الملفات')
        elif 'player' in keywords or 'music' in keywords:
            details.append('لتشغيل الوسائط')
        elif 'manager' in keywords or 'manage' in file_name.lower():
            details.append('لإدارة الملفات أو البيانات')
        elif 'converter' in keywords or 'convert' in file_name.lower():
            details.append('لتحويل الملفات أو البيانات')
        
        # دمج الوصف
        if details:
            return f"{base_description} {' و'.join(details)}"
        else:
            return base_description
    
    def _calculate_confidence(self, code_content, app_type):
        """حساب مستوى الثقة في التحليل"""
        app_info = self.app_patterns.get(app_type, self.app_patterns['GUI'])
        
        matches = 0
        for keyword in app_info['keywords']:
            if keyword in code_content:
                matches += 1
        
        confidence = min(matches / len(app_info['keywords']), 1.0)
        return max(confidence, 0.3)  # حد أدنى 30%
    
    def _create_default_analysis(self, error_msg):
        """إنشاء تحليل افتراضي"""
        return type('Analysis', (), {
            'app_type': 'GUI',
            'description': 'تطبيق واجهة رسومية عام',
            'keywords': ['application', 'software', 'tool'],
            'functions': [],
            'libraries': [],
            'complexity': 'medium',
            'style_suggestion': 'modern interface design',
            'color_palette': ['#4169E1', '#32CD32', '#FF6347'],
            'mood': 'professional, user-friendly',
            'category': 'تطبيق عام',
            'confidence': 0.5,
            'error': error_msg
        })()

class AILogoGenerator:
    """مولد اللوجوهات بالذكاء الاصطناعي"""
    
    def __init__(self, callback=None):
        self.callback = callback
        self.analyzer = SmartCodeAnalyzer()
        self.generated_logos = []
        self.cache_dir = "logo_cache"
        
        # إنشاء مجلد التخزين المؤقت
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
        
        # أنماط التصميم المتقدمة
        self.design_styles = {
            'minimalist': {
                'name': 'تصميم بسيط',
                'description': 'تصميم نظيف وبسيط مع عناصر قليلة',
                'keywords': ['clean', 'simple', 'minimal', 'modern']
            },
            'corporate': {
                'name': 'تصميم مؤسسي',
                'description': 'تصميم احترافي للشركات والمؤسسات',
                'keywords': ['professional', 'corporate', 'business', 'formal']
            },
            'creative': {
                'name': 'تصميم إبداعي',
                'description': 'تصميم فني وإبداعي مع ألوان زاهية',
                'keywords': ['creative', 'artistic', 'colorful', 'unique']
            },
            'tech': {
                'name': 'تصميم تقني',
                'description': 'تصميم عصري للتطبيقات التقنية',
                'keywords': ['tech', 'digital', 'futuristic', 'innovation']
            },
            'playful': {
                'name': 'تصميم مرح',
                'description': 'تصميم ممتع ومرح للألعاب والتطبيقات الترفيهية',
                'keywords': ['fun', 'playful', 'game', 'entertainment']
            },
            'elegant': {
                'name': 'تصميم أنيق',
                'description': 'تصميم راقي وأنيق مع تفاصيل دقيقة',
                'keywords': ['elegant', 'sophisticated', 'luxury', 'premium']
            }
        }
    
    def log(self, message):
        """إرسال رسالة للواجهة"""
        if self.callback:
            self.callback(message)
        else:
            print(message)
    
    def generate_logos_from_analysis(self, analysis, count=6):
        """توليد لوجوهات بناءً على التحليل"""
        self.log("🎨 بدء توليد اللوجوهات بالذكاء الاصطناعي...")
        
        generated_logos = []
        
        # توليد أوصاف متنوعة
        prompts = self._create_prompts_from_analysis(analysis, count)
        
        for i, prompt in enumerate(prompts):
            self.log(f"🖼️ توليد لوجو {i+1}/{count}: {prompt.style}")
            
            try:
                # توليد اللوجو
                logo_image = self._generate_single_logo(prompt, analysis)
                
                if logo_image:
                    # إنشاء كائن LogoDesign
                    logo_design = LogoDesign(
                        name=f"{prompt.style} Logo",
                        image=logo_image,
                        prompt=prompt,
                        confidence=0.9 - (i * 0.05),
                        source="ai_generated",
                        metadata={
                            'app_type': analysis.app_type,
                            'category': analysis.category,
                            'generated_at': time.time()
                        }
                    )
                    
                    generated_logos.append(logo_design)
                    
            except Exception as e:
                self.log(f"❌ خطأ في توليد اللوجو {i+1}: {e}")
                continue
        
        self.generated_logos = generated_logos
        self.log(f"✅ تم توليد {len(generated_logos)} لوجو بنجاح")
        
        return generated_logos
    
    def generate_logo_from_prompt(self, user_prompt, style="minimalist"):
        """توليد لوجو من وصف المستخدم"""
        self.log(f"🎨 توليد لوجو من الوصف: {user_prompt}")
        
        try:
            # إنشاء prompt متقدم
            ai_prompt = AIPrompt(
                text=f"professional logo design for {user_prompt}",
                style=style,
                colors=self._suggest_colors_for_prompt(user_prompt),
                mood=self._analyze_mood_from_prompt(user_prompt),
                category=self._categorize_prompt(user_prompt),
                keywords=user_prompt.split()[:5],
                quality="high",
                size="1024x1024"
            )
            
            # توليد اللوجو
            logo_image = self._generate_single_logo(ai_prompt)
            
            if logo_image:
                logo_design = LogoDesign(
                    name=f"Custom: {user_prompt[:20]}...",
                    image=logo_image,
                    prompt=ai_prompt,
                    confidence=0.85,
                    source="user_prompt",
                    metadata={
                        'user_input': user_prompt,
                        'generated_at': time.time()
                    }
                )
                
                self.log("✅ تم توليد اللوجو المخصص بنجاح")
                return logo_design
            
        except Exception as e:
            self.log(f"❌ خطأ في توليد اللوجو المخصص: {e}")
            return None

    def _create_prompts_from_analysis(self, analysis, count):
        """إنشاء أوصاف متنوعة من التحليل"""
        prompts = []
        styles = list(self.design_styles.keys())

        for i in range(count):
            style = styles[i % len(styles)]
            style_info = self.design_styles[style]

            # بناء النص الأساسي
            base_text = f"professional {style} logo design for {analysis.description}"

            # إضافة الكلمات المفتاحية
            if analysis.keywords:
                keywords_text = ", ".join(analysis.keywords[:3])
                base_text += f" related to {keywords_text}"

            # إضافة نمط التصميم
            base_text += f", {style_info['description']}"

            # إنشاء الـ prompt
            prompt = AIPrompt(
                text=base_text,
                style=style_info['name'],
                colors=analysis.color_palette,
                mood=analysis.mood,
                category=analysis.category,
                keywords=analysis.keywords + style_info['keywords'],
                quality="high",
                size="1024x1024"
            )

            prompts.append(prompt)

        return prompts

    def _generate_single_logo(self, prompt, analysis=None):
        """توليد لوجو واحد"""
        # محاولة طرق متعددة للتوليد

        # 1. التوليد المحلي المتقدم
        try:
            return self._generate_local_logo(prompt, analysis)
        except Exception as e:
            self.log(f"فشل التوليد المحلي: {e}")

        # 2. التوليد من القوالب
        try:
            return self._generate_template_logo(prompt, analysis)
        except Exception as e:
            self.log(f"فشل التوليد من القوالب: {e}")

        # 3. التوليد الإجرائي
        try:
            return self._generate_procedural_logo(prompt, analysis)
        except Exception as e:
            self.log(f"فشل التوليد الإجرائي: {e}")

        return None

    def _generate_local_logo(self, prompt, analysis=None):
        """توليد محلي متقدم للوجو"""
        size = 1024
        image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)

        # اختيار ألوان ذكية
        colors = self._parse_colors(prompt.colors)

        # رسم خلفية متدرجة أو شفافة حسب النمط
        if 'corporate' in prompt.style.lower():
            self._draw_corporate_background(draw, size, colors)
        elif 'creative' in prompt.style.lower():
            self._draw_creative_background(draw, size, colors)
        else:
            # خلفية شفافة للأنماط الأخرى
            pass

        # إضافة العناصر الرئيسية
        self._add_logo_elements(draw, prompt, analysis, size, colors)

        # تطبيق تأثيرات النمط
        image = self._apply_logo_effects(image, prompt.style)

        return image

    def _generate_template_logo(self, prompt, analysis=None):
        """توليد من قوالب جاهزة"""
        templates = {
            'minimalist': self._create_minimalist_template,
            'corporate': self._create_corporate_template,
            'creative': self._create_creative_template,
            'tech': self._create_tech_template,
            'playful': self._create_playful_template,
            'elegant': self._create_elegant_template
        }

        # العثور على القالب المناسب
        template_key = 'minimalist'
        for key in templates.keys():
            if key in prompt.style.lower():
                template_key = key
                break

        template_func = templates[template_key]
        return template_func(prompt, analysis)

    def _generate_procedural_logo(self, prompt, analysis=None):
        """توليد إجرائي متقدم"""
        size = 1024
        image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)

        colors = self._parse_colors(prompt.colors)
        center = size // 2

        # توليد أشكال هندسية ذكية
        if analysis and analysis.app_type == 'Tech':
            self._draw_tech_shapes(draw, center, size//3, colors)
        elif analysis and analysis.app_type == 'Game':
            self._draw_game_shapes(draw, center, size//3, colors)
        elif analysis and analysis.app_type == 'Finance':
            self._draw_finance_shapes(draw, center, size//3, colors)
        else:
            self._draw_generic_shapes(draw, center, size//3, colors)

        return image

    def _parse_colors(self, color_list):
        """تحليل قائمة الألوان"""
        parsed_colors = []
        for color in color_list[:3]:  # أهم 3 ألوان
            try:
                if color.startswith('#'):
                    # تحويل من hex إلى RGB
                    hex_color = color.lstrip('#')
                    rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
                    parsed_colors.append(rgb)
                else:
                    # ألوان افتراضية
                    parsed_colors.append((65, 105, 225))  # Royal Blue
            except:
                parsed_colors.append((65, 105, 225))

        return parsed_colors if parsed_colors else [(65, 105, 225), (50, 205, 50), (255, 99, 71)]

    def _draw_corporate_background(self, draw, size, colors):
        """رسم خلفية مؤسسية"""
        # تدرج لوني هادئ
        color1 = colors[0]
        color2 = tuple(min(255, c + 30) for c in color1)

        for y in range(size):
            ratio = y / size
            r = int(color1[0] + (color2[0] - color1[0]) * ratio)
            g = int(color1[1] + (color2[1] - color1[1]) * ratio)
            b = int(color1[2] + (color2[2] - color1[2]) * ratio)
            color = (r, g, b, 50)  # شفافية خفيفة
            draw.line([(0, y), (size, y)], fill=color)

    def _draw_creative_background(self, draw, size, colors):
        """رسم خلفية إبداعية"""
        # دوائر ملونة متداخلة
        for i, color in enumerate(colors):
            radius = size // (4 + i)
            x = size // 2 + (i - 1) * 100
            y = size // 2 + (i - 1) * 50

            # رسم دائرة مع شفافية
            alpha_color = color + (80,)  # شفافية 80
            draw.ellipse([x-radius, y-radius, x+radius, y+radius], fill=alpha_color)

    def _add_logo_elements(self, draw, prompt, analysis, size, colors):
        """إضافة عناصر اللوجو الرئيسية"""
        center = size // 2

        if analysis:
            if analysis.app_type == 'GUI':
                self._draw_gui_logo_elements(draw, center, size//3, colors)
            elif analysis.app_type == 'Web':
                self._draw_web_logo_elements(draw, center, size//3, colors)
            elif analysis.app_type == 'Game':
                self._draw_game_logo_elements(draw, center, size//3, colors)
            elif analysis.app_type == 'Data Science':
                self._draw_data_logo_elements(draw, center, size//3, colors)
            elif analysis.app_type == 'AI/ML':
                self._draw_ai_logo_elements(draw, center, size//3, colors)
            else:
                self._draw_generic_logo_elements(draw, center, size//3, colors)
        else:
            self._draw_generic_logo_elements(draw, center, size//3, colors)

    def _draw_gui_logo_elements(self, draw, center, radius, colors):
        """رسم عناصر لوجو تطبيق GUI"""
        # نافذة مع أزرار
        window_color = colors[0]

        # النافذة الرئيسية
        draw.rectangle([center-radius, center-radius//2, center+radius, center+radius],
                      fill=window_color, outline=(0, 0, 0, 100), width=4)

        # شريط العنوان
        title_color = colors[1] if len(colors) > 1 else window_color
        draw.rectangle([center-radius, center-radius//2, center+radius, center-radius//4],
                      fill=title_color)

        # أزرار النافذة
        btn_colors = ['#ff5f56', '#ffbd2e', '#27ca3f']
        btn_size = radius // 12
        for i, btn_color in enumerate(btn_colors):
            x = center + radius - (i + 1) * (btn_size * 3)
            y = center - radius//2 + btn_size
            rgb_color = tuple(int(btn_color[j:j+2], 16) for j in (1, 3, 5))
            draw.ellipse([x, y, x + btn_size*2, y + btn_size*2], fill=rgb_color)

    def _draw_web_logo_elements(self, draw, center, radius, colors):
        """رسم عناصر لوجو تطبيق ويب"""
        # كرة أرضية مع شبكة
        globe_color = colors[0]

        # الكرة الأرضية
        draw.ellipse([center-radius, center-radius, center+radius, center+radius],
                    fill=globe_color, outline=(0, 0, 0, 100), width=4)

        # خطوط الطول والعرض
        line_color = colors[1] if len(colors) > 1 else (255, 255, 255)

        # خطوط الطول
        for i in range(3):
            x_offset = (i - 1) * radius // 3
            draw.arc([center-radius//2+x_offset, center-radius//2,
                     center+radius//2+x_offset, center+radius//2],
                    0, 180, fill=line_color, width=3)

        # خط الاستواء
        draw.line([center-radius, center, center+radius, center], fill=line_color, width=3)

    def _draw_game_logo_elements(self, draw, center, radius, colors):
        """رسم عناصر لوجو لعبة"""
        # يد تحكم
        controller_color = colors[0]

        # جسم يد التحكم
        draw.ellipse([center-radius, center-radius//3, center+radius, center+radius//2],
                    fill=controller_color, outline=(0, 0, 0, 100), width=4)

        # الأزرار
        btn_color = colors[1] if len(colors) > 1 else (255, 255, 255)

        # أزرار يسار (D-pad)
        pad_size = radius // 8
        draw.rectangle([center-radius//2-pad_size, center-pad_size//2,
                       center-radius//2+pad_size, center+pad_size//2], fill=btn_color)
        draw.rectangle([center-radius//2-pad_size*2, center-pad_size,
                       center-radius//2, center+pad_size], fill=btn_color)

        # أزرار يمين
        for i, pos in enumerate([(0, -1), (1, 0), (0, 1), (-1, 0)]):
            x = center + radius//3 + pos[0] * pad_size
            y = center + pos[1] * pad_size
            draw.ellipse([x-pad_size//2, y-pad_size//2, x+pad_size//2, y+pad_size//2],
                        fill=btn_color)

    def _draw_data_logo_elements(self, draw, center, radius, colors):
        """رسم عناصر لوجو تطبيق البيانات"""
        # مخطط بياني
        bg_color = colors[0]

        # خلفية المخطط
        draw.rectangle([center-radius, center-radius//2, center+radius, center+radius],
                      fill=bg_color, outline=(0, 0, 0, 100), width=3)

        # أعمدة البيانات
        bar_colors = colors[1:] if len(colors) > 1 else [(255, 255, 255)]
        bar_width = radius // 6
        heights = [radius//4, radius//2, radius//3, radius//1.5, radius//2.5]

        for i, height in enumerate(heights):
            if i >= 5:
                break
            x = center - radius + (i + 1) * bar_width + bar_width//2
            color = bar_colors[i % len(bar_colors)]
            draw.rectangle([x, center + radius - height, x + bar_width//2, center + radius],
                          fill=color)

        # خطوط الشبكة
        grid_color = (255, 255, 255, 100)
        for i in range(1, 4):
            y = center + radius - (i * radius // 4)
            draw.line([center-radius, y, center+radius, y], fill=grid_color, width=1)

    def _draw_ai_logo_elements(self, draw, center, radius, colors):
        """رسم عناصر لوجو الذكاء الاصطناعي"""
        # شبكة عصبية
        node_color = colors[0]
        connection_color = colors[1] if len(colors) > 1 else node_color

        # عقد الشبكة
        nodes = [
            # الطبقة الأولى
            (center - radius//2, center - radius//2),
            (center - radius//2, center),
            (center - radius//2, center + radius//2),
            # الطبقة الوسطى
            (center, center - radius//3),
            (center, center + radius//3),
            # الطبقة الأخيرة
            (center + radius//2, center)
        ]

        # رسم الاتصالات
        connections = [
            (0, 3), (0, 4), (1, 3), (1, 4), (2, 3), (2, 4),  # من الأولى للوسطى
            (3, 5), (4, 5)  # من الوسطى للأخيرة
        ]

        for start, end in connections:
            draw.line([nodes[start], nodes[end]], fill=connection_color, width=3)

        # رسم العقد
        node_radius = radius // 12
        for node in nodes:
            draw.ellipse([node[0]-node_radius, node[1]-node_radius,
                         node[0]+node_radius, node[1]+node_radius],
                        fill=node_color, outline=(0, 0, 0, 100), width=2)

    def _draw_generic_logo_elements(self, draw, center, radius, colors):
        """رسم عناصر لوجو عامة"""
        # شكل هندسي أنيق
        main_color = colors[0]
        accent_color = colors[1] if len(colors) > 1 else main_color

        # دائرة خارجية
        draw.ellipse([center-radius, center-radius, center+radius, center+radius],
                    outline=main_color, width=8)

        # شكل داخلي
        inner_radius = radius // 2
        draw.ellipse([center-inner_radius, center-inner_radius,
                     center+inner_radius, center+inner_radius],
                    fill=accent_color)

        # نقطة مركزية
        dot_radius = radius // 8
        draw.ellipse([center-dot_radius, center-dot_radius,
                     center+dot_radius, center+dot_radius],
                    fill=(255, 255, 255))

    def _apply_logo_effects(self, image, style):
        """تطبيق تأثيرات النمط على اللوجو"""
        if "elegant" in style.lower():
            # تأثير أنيق مع نعومة
            image = image.filter(ImageFilter.SMOOTH_MORE)
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.1)
        elif "tech" in style.lower():
            # تأثير تقني مع حدة
            image = image.filter(ImageFilter.SHARPEN)
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.3)
        elif "creative" in style.lower():
            # تأثير إبداعي مع ألوان زاهية
            enhancer = ImageEnhance.Color(image)
            image = enhancer.enhance(1.4)
        elif "corporate" in style.lower():
            # تأثير مؤسسي مع توازن
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.05)

        return image

    def _suggest_colors_for_prompt(self, prompt_text):
        """اقتراح ألوان بناءً على النص"""
        color_associations = {
            'tech': ['#6366f1', '#8b5cf6', '#06b6d4'],
            'finance': ['#2E8B57', '#FFD700', '#1E90FF'],
            'health': ['#DC143C', '#00FA9A', '#4682B4'],
            'education': ['#4169E1', '#FF6347', '#32CD32'],
            'game': ['#8A2BE2', '#FF1493', '#00CED1'],
            'food': ['#FF6347', '#FFD700', '#32CD32'],
            'travel': ['#00BFFF', '#32CD32', '#FFD700'],
            'music': ['#8A2BE2', '#FF69B4', '#00CED1']
        }

        prompt_lower = prompt_text.lower()
        for category, colors in color_associations.items():
            if category in prompt_lower:
                return colors

        # ألوان افتراضية
        return ['#4169E1', '#32CD32', '#FF6347']

    def _analyze_mood_from_prompt(self, prompt_text):
        """تحليل المزاج من النص"""
        mood_keywords = {
            'professional': ['business', 'corporate', 'office', 'work'],
            'fun': ['game', 'play', 'entertainment', 'fun'],
            'elegant': ['luxury', 'premium', 'elegant', 'sophisticated'],
            'modern': ['tech', 'digital', 'modern', 'innovation'],
            'friendly': ['social', 'community', 'friendly', 'people']
        }

        prompt_lower = prompt_text.lower()
        for mood, keywords in mood_keywords.items():
            if any(keyword in prompt_lower for keyword in keywords):
                return mood

        return 'professional'

    def _categorize_prompt(self, prompt_text):
        """تصنيف النص"""
        categories = {
            'Technology': ['app', 'software', 'tech', 'digital', 'system'],
            'Business': ['business', 'company', 'corporate', 'finance'],
            'Entertainment': ['game', 'music', 'entertainment', 'media'],
            'Education': ['education', 'learning', 'school', 'course'],
            'Health': ['health', 'medical', 'fitness', 'wellness'],
            'Food': ['food', 'restaurant', 'cooking', 'recipe'],
            'Travel': ['travel', 'tourism', 'hotel', 'vacation']
        }

        prompt_lower = prompt_text.lower()
        for category, keywords in categories.items():
            if any(keyword in prompt_lower for keyword in keywords):
                return category

        return 'General'
