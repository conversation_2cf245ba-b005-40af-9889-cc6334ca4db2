#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ultimate Icon Editor - محرر الأيقونات المتكامل النهائي
تطبيق شامل لتصميم وتحرير الأيقونات بالذكاء الاصطناعي
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance, ImageOps, ImageTk
import os
import sys
import json
import threading
import time
import math
import random
import base64
import io
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import tkinter.dnd as dnd

@dataclass
class Layer:
    """طبقة في المحرر"""
    id: str
    name: str
    image: Image.Image
    visible: bool = True
    locked: bool = False
    opacity: float = 1.0
    blend_mode: str = "normal"
    x: int = 0
    y: int = 0

@dataclass
class IconSuggestion:
    """اقتراح أيقونة"""
    name: str
    image: Image.Image
    style: str
    confidence: float
    colors: List[str]
    description: str

class SmartCodeAnalyzer:
    """محلل الكود الذكي المتطور"""
    
    def __init__(self):
        self.app_patterns = {
            'GUI': {
                'keywords': ['tkinter', 'pyqt', 'pyside', 'kivy', 'gui', 'window', 'button'],
                'description': 'تطبيق واجهة رسومية',
                'colors': ['#4169E1', '#32CD32', '#FF6347'],
                'style': 'modern interface'
            },
            'Web': {
                'keywords': ['flask', 'django', 'fastapi', 'web', 'html', 'server'],
                'description': 'تطبيق ويب',
                'colors': ['#FF4500', '#1E90FF', '#32CD32'],
                'style': 'web application'
            },
            'Game': {
                'keywords': ['pygame', 'game', 'player', 'score', 'level'],
                'description': 'لعبة',
                'colors': ['#8A2BE2', '#FF1493', '#00CED1'],
                'style': 'gaming design'
            },
            'Data': {
                'keywords': ['pandas', 'numpy', 'matplotlib', 'data', 'analysis'],
                'description': 'تطبيق تحليل البيانات',
                'colors': ['#2E8B57', '#4682B4', '#FF8C00'],
                'style': 'data visualization'
            },
            'AI': {
                'keywords': ['tensorflow', 'pytorch', 'sklearn', 'ai', 'ml'],
                'description': 'تطبيق ذكاء اصطناعي',
                'colors': ['#6366f1', '#8b5cf6', '#06b6d4'],
                'style': 'futuristic AI'
            }
        }
    
    def analyze_code(self, file_path_or_text):
        """تحليل الكود"""
        try:
            if os.path.isfile(file_path_or_text):
                with open(file_path_or_text, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read().lower()
                file_name = os.path.basename(file_path_or_text)
            else:
                content = file_path_or_text.lower()
                file_name = "user_input"
            
            # تحديد نوع التطبيق
            app_type = self._detect_app_type(content)
            app_info = self.app_patterns.get(app_type, self.app_patterns['GUI'])
            
            # استخراج المعلومات
            keywords = self._extract_keywords(content)
            functions = self._extract_functions(content)
            libraries = self._extract_libraries(content)
            
            # إنشاء التحليل
            analysis = type('Analysis', (), {
                'app_type': app_type,
                'description': app_info['description'],
                'keywords': keywords,
                'functions': functions,
                'libraries': libraries,
                'colors': app_info['colors'],
                'style': app_info['style'],
                'confidence': self._calculate_confidence(content, app_type),
                'file_name': file_name
            })()
            
            return analysis
            
        except Exception as e:
            return self._create_default_analysis(str(e))
    
    def _detect_app_type(self, content):
        """اكتشاف نوع التطبيق"""
        scores = {}
        for app_type, info in self.app_patterns.items():
            score = sum(content.count(keyword) for keyword in info['keywords'])
            scores[app_type] = score
        
        return max(scores, key=scores.get) if max(scores.values()) > 0 else 'GUI'
    
    def _extract_keywords(self, content):
        """استخراج الكلمات المفتاحية"""
        keywords = ['app', 'program', 'tool', 'system', 'manager', 'editor', 'player']
        return [kw for kw in keywords if kw in content][:10]
    
    def _extract_functions(self, content):
        """استخراج الوظائف"""
        import re
        functions = re.findall(r'def\s+([a-zA-Z_][a-zA-Z0-9_]*)', content)
        return functions[:10]
    
    def _extract_libraries(self, content):
        """استخراج المكتبات"""
        import re
        imports = re.findall(r'import\s+([a-zA-Z_][a-zA-Z0-9_]*)', content)
        from_imports = re.findall(r'from\s+([a-zA-Z_][a-zA-Z0-9_]*)', content)
        return list(set(imports + from_imports))[:10]
    
    def _calculate_confidence(self, content, app_type):
        """حساب مستوى الثقة"""
        app_info = self.app_patterns[app_type]
        matches = sum(1 for kw in app_info['keywords'] if kw in content)
        return min(matches / len(app_info['keywords']), 1.0)
    
    def _create_default_analysis(self, error):
        """تحليل افتراضي"""
        return type('Analysis', (), {
            'app_type': 'GUI',
            'description': 'تطبيق عام',
            'keywords': ['application'],
            'functions': [],
            'libraries': [],
            'colors': ['#4169E1', '#32CD32', '#FF6347'],
            'style': 'modern',
            'confidence': 0.5,
            'file_name': 'unknown',
            'error': error
        })()

class ModernIconGenerator:
    """مولد الأيقونات العصرية المتطور"""
    
    def __init__(self, callback=None):
        self.callback = callback
        self.analyzer = SmartCodeAnalyzer()
        
        # أنماط التصميم العصرية
        self.modern_styles = {
            'glassmorphism': {
                'name': 'زجاجي عصري',
                'colors': ['rgba(255,255,255,0.25)', 'rgba(255,255,255,0.18)'],
                'effects': ['blur', 'transparency', 'border']
            },
            'neumorphism': {
                'name': 'ناعم ثلاثي الأبعاد',
                'colors': ['#e0e5ec', '#ffffff', '#a3b1c6'],
                'effects': ['soft_shadow', 'highlight', 'depth']
            },
            'gradient': {
                'name': 'تدرج عصري',
                'colors': ['#667eea', '#764ba2', '#f093fb'],
                'effects': ['gradient', 'glow', 'vibrant']
            },
            'neon': {
                'name': 'نيون مستقبلي',
                'colors': ['#00f5ff', '#ff00ff', '#ffff00'],
                'effects': ['neon_glow', 'electric', 'bright']
            },
            'minimal': {
                'name': 'بسيط أنيق',
                'colors': ['#2d3748', '#4a5568', '#718096'],
                'effects': ['clean', 'simple', 'elegant']
            },
            'holographic': {
                'name': 'هولوجرافي',
                'colors': ['#ff006e', '#8338ec', '#3a86ff'],
                'effects': ['hologram', 'iridescent', 'futuristic']
            }
        }
    
    def log(self, message):
        """إرسال رسالة"""
        if self.callback:
            self.callback(message)
        print(message)
    
    def generate_modern_icons(self, analysis, count=6):
        """توليد أيقونات عصرية"""
        self.log("🎨 بدء توليد الأيقونات العصرية...")
        
        suggestions = []
        styles = list(self.modern_styles.keys())
        
        for i in range(count):
            style_key = styles[i % len(styles)]
            style_info = self.modern_styles[style_key]
            
            try:
                # توليد الأيقونة
                icon_image = self._create_modern_icon(analysis, style_key, style_info)
                
                if icon_image:
                    suggestion = IconSuggestion(
                        name=f"{style_info['name']} - {analysis.app_type}",
                        image=icon_image,
                        style=style_info['name'],
                        confidence=0.9 - (i * 0.1),
                        colors=style_info['colors'][:3],
                        description=f"أيقونة {style_info['name']} لـ {analysis.description}"
                    )
                    suggestions.append(suggestion)
                    
                    self.log(f"✅ تم توليد: {suggestion.name}")
                
            except Exception as e:
                self.log(f"❌ خطأ في توليد النمط {style_key}: {e}")
                continue
        
        self.log(f"🎉 تم توليد {len(suggestions)} أيقونة عصرية")
        return suggestions
    
    def _create_modern_icon(self, analysis, style_key, style_info):
        """إنشاء أيقونة عصرية"""
        size = 512
        image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)
        
        # اختيار ألوان ذكية
        colors = self._get_smart_colors(analysis, style_info)
        center = size // 2
        
        # رسم الخلفية حسب النمط
        if style_key == 'glassmorphism':
            self._draw_glass_background(draw, size, colors)
        elif style_key == 'neumorphism':
            self._draw_neuro_background(draw, size, colors)
        elif style_key == 'gradient':
            self._draw_gradient_background(draw, size, colors)
        elif style_key == 'neon':
            self._draw_neon_background(draw, size, colors)
        elif style_key == 'minimal':
            self._draw_minimal_background(draw, size, colors)
        elif style_key == 'holographic':
            self._draw_holo_background(draw, size, colors)
        
        # إضافة العناصر الرئيسية
        self._add_app_elements(draw, analysis, center, size//3, colors)
        
        # تطبيق التأثيرات
        image = self._apply_modern_effects(image, style_key)
        
        return image
    
    def _get_smart_colors(self, analysis, style_info):
        """الحصول على ألوان ذكية"""
        # دمج ألوان التحليل مع ألوان النمط
        analysis_colors = getattr(analysis, 'colors', ['#4169E1'])
        style_colors = style_info['colors']
        
        # تحويل الألوان وتنظيفها
        colors = []
        for color in (analysis_colors + style_colors)[:6]:
            if color.startswith('rgba'):
                # تحويل rgba إلى rgb
                color = '#4169E1'  # لون افتراضي
            colors.append(color)
        
        return colors
    
    def _draw_glass_background(self, draw, size, colors):
        """رسم خلفية زجاجية"""
        # تدرج زجاجي شفاف
        for y in range(size):
            alpha = int(50 + (y / size) * 30)
            color = self._hex_to_rgb(colors[0]) + (alpha,)
            draw.line([(0, y), (size, y)], fill=color)
    
    def _draw_neuro_background(self, draw, size, colors):
        """رسم خلفية ناعمة"""
        # خلفية ناعمة مع ظلال
        bg_color = self._hex_to_rgb(colors[0])
        draw.rectangle([0, 0, size, size], fill=bg_color)
        
        # إضافة ظلال ناعمة
        for i in range(5):
            offset = i * 10
            shadow_color = tuple(max(0, c - 20) for c in bg_color)
            draw.ellipse([offset, offset, size-offset, size-offset], 
                        outline=shadow_color, width=2)
    
    def _draw_gradient_background(self, draw, size, colors):
        """رسم خلفية متدرجة"""
        color1 = self._hex_to_rgb(colors[0])
        color2 = self._hex_to_rgb(colors[1]) if len(colors) > 1 else color1
        
        for y in range(size):
            ratio = y / size
            r = int(color1[0] + (color2[0] - color1[0]) * ratio)
            g = int(color1[1] + (color2[1] - color1[1]) * ratio)
            b = int(color1[2] + (color2[2] - color1[2]) * ratio)
            draw.line([(0, y), (size, y)], fill=(r, g, b))
    
    def _draw_neon_background(self, draw, size, colors):
        """رسم خلفية نيون"""
        # خلفية داكنة
        draw.rectangle([0, 0, size, size], fill=(20, 20, 40))
        
        # خطوط نيون
        neon_color = self._hex_to_rgb(colors[0])
        for i in range(0, size, 40):
            draw.line([(i, 0), (i, size)], fill=neon_color, width=2)
            draw.line([(0, i), (size, i)], fill=neon_color, width=2)
    
    def _draw_minimal_background(self, draw, size, colors):
        """رسم خلفية بسيطة"""
        # خلفية نظيفة
        bg_color = self._hex_to_rgb(colors[0])
        draw.rectangle([0, 0, size, size], fill=bg_color)
    
    def _draw_holo_background(self, draw, size, colors):
        """رسم خلفية هولوجرافية"""
        # تدرج قوس قزح
        for y in range(size):
            hue = (y / size) * 360
            color = self._hsv_to_rgb(hue, 0.7, 0.9)
            draw.line([(0, y), (size, y)], fill=color)
    
    def _add_app_elements(self, draw, analysis, center, radius, colors):
        """إضافة عناصر التطبيق"""
        app_type = analysis.app_type
        color = self._hex_to_rgb(colors[0])
        
        if app_type == 'GUI':
            self._draw_gui_icon(draw, center, radius, color)
        elif app_type == 'Web':
            self._draw_web_icon(draw, center, radius, color)
        elif app_type == 'Game':
            self._draw_game_icon(draw, center, radius, color)
        elif app_type == 'Data':
            self._draw_data_icon(draw, center, radius, color)
        elif app_type == 'AI':
            self._draw_ai_icon(draw, center, radius, color)
        else:
            self._draw_generic_icon(draw, center, radius, color)
    
    def _draw_gui_icon(self, draw, center, radius, color):
        """رسم أيقونة GUI عصرية"""
        # نافذة حديثة
        draw.rounded_rectangle([center-radius, center-radius//2, center+radius, center+radius], 
                              radius=20, fill=color, outline=(255,255,255), width=3)
        # شريط العنوان
        draw.rounded_rectangle([center-radius, center-radius//2, center+radius, center-radius//4], 
                              radius=15, fill=(255,255,255,180))
        # أزرار حديثة
        for i, btn_color in enumerate(['#ff5f56', '#ffbd2e', '#27ca3f']):
            x = center + radius - (i + 1) * 25
            y = center - radius//2 + 15
            btn_rgb = self._hex_to_rgb(btn_color)
            draw.ellipse([x, y, x+15, y+15], fill=btn_rgb)
    
    def _draw_web_icon(self, draw, center, radius, color):
        """رسم أيقونة ويب عصرية"""
        # كرة أرضية حديثة
        draw.ellipse([center-radius, center-radius, center+radius, center+radius], 
                    fill=color, outline=(255,255,255), width=4)
        # شبكة حديثة
        for i in range(-2, 3):
            x_offset = i * radius // 4
            draw.arc([center-radius//2+x_offset, center-radius//2, 
                     center+radius//2+x_offset, center+radius//2], 
                    0, 180, fill=(255,255,255), width=3)
        # خط الاستواء
        draw.line([center-radius, center, center+radius, center], 
                 fill=(255,255,255), width=4)
    
    def _draw_game_icon(self, draw, center, radius, color):
        """رسم أيقونة لعبة عصرية"""
        # يد تحكم حديثة
        draw.rounded_rectangle([center-radius, center-radius//3, center+radius, center+radius//2], 
                              radius=25, fill=color, outline=(255,255,255), width=3)
        # أزرار D-pad
        pad_size = radius // 6
        draw.ellipse([center-radius//2-pad_size, center-pad_size//2, 
                     center-radius//2+pad_size, center+pad_size//2], fill=(255,255,255))
        # أزرار يمين
        for i, pos in enumerate([(30, 0), (0, 30), (-30, 0), (0, -30)]):
            x = center + radius//3 + pos[0]
            y = center + pos[1]
            draw.ellipse([x-8, y-8, x+8, y+8], fill=(255,255,255))
    
    def _draw_data_icon(self, draw, center, radius, color):
        """رسم أيقونة بيانات عصرية"""
        # مخطط بياني حديث
        draw.rounded_rectangle([center-radius, center-radius//2, center+radius, center+radius], 
                              radius=15, fill=color, outline=(255,255,255), width=3)
        # أعمدة بيانات
        heights = [radius//3, radius//2, radius//4, radius//1.5, radius//2.5]
        bar_width = radius // 8
        for i, height in enumerate(heights):
            x = center - radius + (i + 1) * bar_width + bar_width
            draw.rounded_rectangle([x, center + radius - height, x + bar_width//2, center + radius], 
                                  radius=5, fill=(255,255,255))
    
    def _draw_ai_icon(self, draw, center, radius, color):
        """رسم أيقونة ذكاء اصطناعي عصرية"""
        # دماغ أو شبكة عصبية
        draw.ellipse([center-radius, center-radius, center+radius, center+radius], 
                    fill=color, outline=(255,255,255), width=4)
        # نقاط الشبكة
        nodes = [(center-radius//3, center-radius//3), (center+radius//3, center-radius//3),
                (center, center), (center-radius//3, center+radius//3), (center+radius//3, center+radius//3)]
        # رسم الاتصالات
        for i, node1 in enumerate(nodes):
            for j, node2 in enumerate(nodes[i+1:], i+1):
                draw.line([node1, node2], fill=(255,255,255,100), width=2)
        # رسم النقاط
        for node in nodes:
            draw.ellipse([node[0]-8, node[1]-8, node[0]+8, node[1]+8], fill=(255,255,255))
    
    def _draw_generic_icon(self, draw, center, radius, color):
        """رسم أيقونة عامة عصرية"""
        # شكل هندسي حديث
        draw.ellipse([center-radius, center-radius, center+radius, center+radius], 
                    outline=color, width=8)
        draw.ellipse([center-radius//2, center-radius//2, center+radius//2, center+radius//2], 
                    fill=color)
        draw.ellipse([center-radius//4, center-radius//4, center+radius//4, center+radius//4], 
                    fill=(255,255,255))
    
    def _apply_modern_effects(self, image, style_key):
        """تطبيق التأثيرات العصرية"""
        if style_key == 'glassmorphism':
            image = image.filter(ImageFilter.GaussianBlur(radius=1))
        elif style_key == 'neumorphism':
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.1)
        elif style_key == 'gradient':
            enhancer = ImageEnhance.Color(image)
            image = enhancer.enhance(1.3)
        elif style_key == 'neon':
            image = image.filter(ImageFilter.SMOOTH)
            enhancer = ImageEnhance.Brightness(image)
            image = enhancer.enhance(1.2)
        elif style_key == 'holographic':
            enhancer = ImageEnhance.Color(image)
            image = enhancer.enhance(1.5)
        
        return image
    
    def _hex_to_rgb(self, hex_color):
        """تحويل من hex إلى RGB"""
        try:
            hex_color = hex_color.lstrip('#')
            return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        except:
            return (65, 105, 225)  # لون افتراضي
    
    def _hsv_to_rgb(self, h, s, v):
        """تحويل من HSV إلى RGB"""
        import colorsys
        r, g, b = colorsys.hsv_to_rgb(h/360, s, v)
        return (int(r*255), int(g*255), int(b*255))

class UltimateIconEditor:
    """محرر الأيقونات المتكامل النهائي"""

    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()

        # إعدادات المحرر
        self.canvas_size = 512
        self.zoom_level = 1.0
        self.grid_size = 32
        self.show_grid = True

        # الطبقات والأدوات
        self.layers = []
        self.current_layer = None
        self.current_tool = "brush"
        self.current_color = "#4169E1"
        self.brush_size = 20
        self.opacity = 100

        # نظام التراجع
        self.history = []
        self.history_index = -1
        self.max_history = 30

        # الذكاء الاصطناعي
        self.analyzer = SmartCodeAnalyzer()
        self.icon_generator = ModernIconGenerator(callback=self.log_message)
        self.current_analysis = None
        self.icon_suggestions = []

        # الصور
        self.original_image = None
        self.preview_image = None
        self.current_image = None

        # ألوان الواجهة العصرية العربية
        self.colors = {
            'bg_primary': '#0f0f23',
            'bg_secondary': '#1a1a2e',
            'bg_tertiary': '#16213e',
            'bg_card': '#1e1e3f',
            'accent_blue': '#6366f1',
            'accent_purple': '#8b5cf6',
            'accent_cyan': '#06b6d4',
            'accent_pink': '#ec4899',
            'accent_green': '#10b981',
            'accent_orange': '#f59e0b',
            'text_primary': '#ffffff',
            'text_secondary': '#a1a1aa',
            'text_muted': '#6b7280',
            'border': '#374151',
            'border_light': '#4b5563',
            'success': '#10b981',
            'warning': '#f59e0b',
            'error': '#ef4444',
            'glass_bg': 'rgba(255,255,255,0.1)',
            'glass_border': 'rgba(255,255,255,0.2)'
        }

        self.setup_ui()
        self.create_default_layer()

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("🎨 Ultimate Icon Editor - محرر الأيقونات المتكامل النهائي")
        self.root.geometry("1800x1000")
        self.root.configure(bg='#0f0f23')
        self.root.state('zoomed')  # ملء الشاشة

        # تطبيق نمط عصري
        self.root.attributes('-alpha', 0.98)

        # إعداد الخطوط العربية
        try:
            self.arabic_font = ('Segoe UI', 10)
            self.arabic_font_bold = ('Segoe UI', 10, 'bold')
            self.arabic_font_large = ('Segoe UI', 12, 'bold')
        except:
            self.arabic_font = ('Arial', 10)
            self.arabic_font_bold = ('Arial', 10, 'bold')
            self.arabic_font_large = ('Arial', 12, 'bold')

    def setup_ui(self):
        """إعداد الواجهة الرئيسية"""
        # شريط القوائم العربي
        self.create_arabic_menu_bar()

        # شريط الأدوات الرئيسي
        self.create_main_toolbar()

        # المنطقة الرئيسية
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # لوحة الذكاء الاصطناعي (أعلى)
        self.create_ai_panel(main_frame)

        # المنطقة الوسطى
        middle_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
        middle_frame.pack(fill='both', expand=True, pady=10)

        # لوحة المعاينة والتحكم (يمين)
        self.create_preview_panel(middle_frame)

        # منطقة الرسم (وسط)
        self.create_canvas_area(middle_frame)

        # شريط الحالة العربي
        self.create_arabic_status_bar()

        # لوحة الأدوات والخصائص (يسار)
        self.create_tools_panel(middle_frame)

    def create_arabic_menu_bar(self):
        """إنشاء شريط قوائم عربي"""
        menu_frame = tk.Frame(self.root, bg=self.colors['bg_secondary'], height=45)
        menu_frame.pack(fill='x')
        menu_frame.pack_propagate(False)

        # قوائم عربية من اليمين لليسار
        arabic_menus = [
            ("🧠 ذكاء اصطناعي", [
                ("🎨 توليد أيقونات عصرية", self.generate_modern_icons),
                ("📊 تحليل الكود", self.analyze_code),
                ("✨ تحسين الصورة", self.enhance_image),
                ("🎯 إزالة الخلفية", self.remove_background),
                ("🔄 إعادة توليد", self.regenerate_icons)
            ]),
            ("🎨 تأثيرات", [
                ("🌫️ ضبابية", lambda: self.apply_filter("blur")),
                ("⚡ حدة", lambda: self.apply_filter("sharpen")),
                ("🎭 نقش", lambda: self.apply_filter("emboss")),
                ("🔍 كشف الحواف", lambda: self.apply_filter("edge")),
                ("🌈 تدرج لوني", lambda: self.apply_filter("gradient")),
                ("✨ توهج", lambda: self.apply_filter("glow"))
            ]),
            ("🗂️ طبقات", [
                ("➕ طبقة جديدة", self.add_new_layer),
                ("📋 تكرار طبقة", self.duplicate_layer),
                ("🔗 دمج لأسفل", self.merge_down),
                ("📄 تسطيح الصورة", self.flatten_image),
                ("👁️ إظهار/إخفاء", self.toggle_layer_visibility),
                ("🔒 قفل/إلغاء قفل", self.toggle_layer_lock)
            ]),
            ("✏️ تحرير", [
                ("↶ تراجع", self.undo),
                ("↷ إعادة", self.redo),
                ("📋 نسخ", self.copy_layer),
                ("📄 لصق", self.paste_layer),
                ("🗑️ حذف", self.delete_layer),
                ("🔄 مسح الكل", self.clear_canvas)
            ]),
            ("📁 ملف", [
                ("🆕 مشروع جديد", self.new_project),
                ("📂 فتح صورة", self.open_image),
                ("📂 فتح مشروع", self.open_project),
                ("💾 حفظ مشروع", self.save_project),
                ("📤 تصدير أيقونة", self.export_icon),
                ("🖼️ تصدير صورة", self.export_image)
            ])
        ]

        # إضافة القوائم من اليمين لليسار
        for menu_name, menu_items in arabic_menus:
            menu_btn = tk.Button(
                menu_frame,
                text=menu_name,
                font=self.arabic_font_bold,
                fg=self.colors['text_primary'],
                bg=self.colors['bg_secondary'],
                relief='flat',
                padx=20,
                pady=10,
                command=lambda items=menu_items: self.show_context_menu(items)
            )
            menu_btn.pack(side='right', padx=2)

            # تأثير hover عصري
            self.add_hover_effect(menu_btn, self.colors['accent_blue'])

    def create_main_toolbar(self):
        """إنشاء شريط الأدوات الرئيسي العربي"""
        toolbar = tk.Frame(self.root, bg=self.colors['bg_tertiary'], height=70)
        toolbar.pack(fill='x', padx=10, pady=5)
        toolbar.pack_propagate(False)

        # مجموعة الذكاء الاصطناعي (يمين)
        ai_group = tk.Frame(toolbar, bg=self.colors['bg_tertiary'])
        ai_group.pack(side='right', padx=20, pady=12)

        ai_buttons = [
            ("🧠", "توليد بالذكاء الاصطناعي", self.generate_modern_icons, self.colors['accent_pink']),
            ("📊", "تحليل الكود", self.analyze_code, self.colors['accent_purple']),
            ("✨", "تحسين ذكي", self.enhance_image, self.colors['accent_cyan']),
            ("🎯", "إزالة خلفية", self.remove_background, self.colors['accent_green'])
        ]

        for icon, tooltip, command, color in ai_buttons:
            btn = self.create_glass_button(ai_group, icon, command, color, tooltip)
            btn.pack(side='right', padx=3)

        # فاصل عصري
        separator = tk.Frame(toolbar, bg=self.colors['border'], width=2)
        separator.pack(side='right', fill='y', padx=20, pady=15)

        # مجموعة أدوات التحرير (وسط)
        edit_group = tk.Frame(toolbar, bg=self.colors['bg_tertiary'])
        edit_group.pack(side='right', padx=20, pady=12)

        edit_buttons = [
            ("↶", "تراجع", self.undo, self.colors['warning']),
            ("↷", "إعادة", self.redo, self.colors['warning']),
            ("📋", "نسخ", self.copy_layer, self.colors['accent_blue']),
            ("📄", "لصق", self.paste_layer, self.colors['accent_blue'])
        ]

        for icon, tooltip, command, color in edit_buttons:
            btn = self.create_glass_button(edit_group, icon, command, color, tooltip)
            btn.pack(side='right', padx=3)

        # فاصل عصري
        separator2 = tk.Frame(toolbar, bg=self.colors['border'], width=2)
        separator2.pack(side='right', fill='y', padx=20, pady=15)

        # مجموعة أدوات الملف (يسار من الوسط)
        file_group = tk.Frame(toolbar, bg=self.colors['bg_tertiary'])
        file_group.pack(side='right', padx=20, pady=12)

        file_buttons = [
            ("🆕", "مشروع جديد", self.new_project, self.colors['accent_green']),
            ("📂", "فتح صورة", self.open_image, self.colors['accent_blue']),
            ("💾", "حفظ مشروع", self.save_project, self.colors['accent_purple']),
            ("📤", "تصدير", self.export_icon, self.colors['accent_cyan'])
        ]

        for icon, tooltip, command, color in file_buttons:
            btn = self.create_glass_button(file_group, icon, command, color, tooltip)
            btn.pack(side='right', padx=3)

        # معلومات المشروع (يسار)
        info_frame = tk.Frame(toolbar, bg=self.colors['bg_tertiary'])
        info_frame.pack(side='left', padx=20, pady=12)

        self.project_info = tk.Label(
            info_frame,
            text="🎨 محرر الأيقونات المتكامل - جاهز للإبداع",
            font=self.arabic_font_bold,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_tertiary']
        )
        self.project_info.pack()

    def create_glass_button(self, parent, icon, command, color, tooltip=""):
        """إنشاء زر زجاجي عصري"""
        btn = tk.Button(
            parent,
            text=icon,
            font=('Segoe UI', 16, 'bold'),
            fg='white',
            bg=color,
            relief='flat',
            width=4,
            height=2,
            command=command,
            cursor='hand2',
            bd=0
        )

        # تأثيرات hover عصرية
        self.add_hover_effect(btn, color)

        # إضافة tooltip
        if tooltip:
            self.create_tooltip(btn, tooltip)

        return btn

    def add_hover_effect(self, widget, base_color):
        """إضافة تأثير hover"""
        def on_enter(e):
            hover_color = self.lighten_color(base_color, 30)
            widget.configure(bg=hover_color)

        def on_leave(e):
            widget.configure(bg=base_color)

        widget.bind('<Enter>', on_enter)
        widget.bind('<Leave>', on_leave)

    def lighten_color(self, color, amount):
        """تفتيح لون"""
        try:
            color = color.lstrip('#')
            rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
            lightened = tuple(min(255, c + amount) for c in rgb)
            return f"#{lightened[0]:02x}{lightened[1]:02x}{lightened[2]:02x}"
        except:
            return color

    def create_tooltip(self, widget, text):
        """إنشاء tooltip"""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
            tooltip.configure(bg=self.colors['bg_card'])

            label = tk.Label(
                tooltip,
                text=text,
                font=self.arabic_font,
                bg=self.colors['bg_card'],
                fg=self.colors['text_primary'],
                relief='solid',
                bd=1,
                padx=8,
                pady=5
            )
            label.pack()

            widget.tooltip = tooltip

        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip

        widget.bind('<Enter>', on_enter)
        widget.bind('<Leave>', on_leave)

    def create_ai_panel(self, parent):
        """إنشاء لوحة الذكاء الاصطناعي العربية"""
        ai_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], relief='solid', bd=1)
        ai_frame.pack(fill='x', pady=(0, 10))

        # عنوان اللوحة العربي
        title_frame = tk.Frame(ai_frame, bg=self.colors['bg_secondary'])
        title_frame.pack(fill='x', padx=20, pady=15)

        title_label = tk.Label(
            title_frame,
            text="🧠 مولد الأيقونات العصرية بالذكاء الاصطناعي المتطور",
            font=self.arabic_font_large,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        title_label.pack(side='right')

        # أزرار التحكم
        controls_frame = tk.Frame(title_frame, bg=self.colors['bg_secondary'])
        controls_frame.pack(side='left')

        generate_btn = self.create_glass_button(
            controls_frame, "🎨", self.generate_modern_icons,
            self.colors['accent_pink'], "توليد أيقونات عصرية جديدة"
        )
        generate_btn.pack(side='left', padx=5)

        analyze_btn = self.create_glass_button(
            controls_frame, "📊", self.analyze_code,
            self.colors['accent_purple'], "تحليل الكود"
        )
        analyze_btn.pack(side='left', padx=5)

        # المحتوى الرئيسي
        content_frame = tk.Frame(ai_frame, bg=self.colors['bg_secondary'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))

        # منطقة الإدخال العربية
        input_frame = tk.Frame(content_frame, bg=self.colors['bg_secondary'])
        input_frame.pack(fill='x', pady=(0, 15))

        # تحليل التطبيق
        analysis_frame = tk.LabelFrame(
            input_frame,
            text="📊 تحليل التطبيق الذكي",
            font=self.arabic_font_bold,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            bd=2,
            relief='groove'
        )
        analysis_frame.pack(side='right', fill='both', expand=True, padx=(0, 15))

        self.analysis_text = tk.Text(
            analysis_frame,
            height=5,
            font=self.arabic_font,
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            relief='flat',
            wrap='word',
            bd=0,
            padx=10,
            pady=8
        )
        self.analysis_text.pack(fill='both', expand=True, padx=8, pady=8)

        # إدخال مخصص
        custom_frame = tk.LabelFrame(
            input_frame,
            text="✏️ وصف الأيقونة المطلوبة",
            font=self.arabic_font_bold,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            bd=2,
            relief='groove'
        )
        custom_frame.pack(side='left', fill='both', expand=True)

        self.custom_prompt_text = tk.Text(
            custom_frame,
            height=5,
            font=self.arabic_font,
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            relief='flat',
            wrap='word',
            bd=0,
            padx=10,
            pady=8
        )
        self.custom_prompt_text.pack(fill='both', expand=True, padx=8, pady=8)

        # إدراج نص تلقائي عربي
        placeholder_text = """اكتب وصف الأيقونة المطلوبة هنا...

أمثلة:
• أيقونة تطبيق تعليمي للأطفال بألوان زاهية
• لوجو شركة تقنية بتصميم مستقبلي
• أيقونة لعبة مغامرات بنمط كرتوني
• رمز تطبيق مالي بتصميم أنيق ومحترف"""

        self.custom_prompt_text.insert('1.0', placeholder_text)
        self.custom_prompt_text.bind('<FocusIn>', self.clear_placeholder)

        # منطقة النتائج العربية
        results_frame = tk.Frame(content_frame, bg=self.colors['bg_secondary'])
        results_frame.pack(fill='both', expand=True)

        # قائمة الاقتراحات
        suggestions_frame = tk.LabelFrame(
            results_frame,
            text="💡 الأيقونات المولدة بالذكاء الاصطناعي",
            font=self.arabic_font_bold,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            bd=2,
            relief='groove'
        )
        suggestions_frame.pack(side='right', fill='both', expand=True, padx=(0, 15))

        # إطار التمرير للاقتراحات
        suggestions_scroll_frame = tk.Frame(suggestions_frame, bg=self.colors['bg_secondary'])
        suggestions_scroll_frame.pack(fill='both', expand=True, padx=8, pady=8)

        # Canvas للاقتراحات مع شريط تمرير
        self.suggestions_canvas = tk.Canvas(
            suggestions_scroll_frame,
            bg=self.colors['bg_tertiary'],
            relief='flat',
            highlightthickness=0,
            bd=0
        )
        suggestions_scrollbar = tk.Scrollbar(
            suggestions_scroll_frame,
            orient='vertical',
            command=self.suggestions_canvas.yview,
            bg=self.colors['bg_tertiary'],
            troughcolor=self.colors['bg_card'],
            activebackground=self.colors['accent_blue']
        )

        self.suggestions_canvas.configure(yscrollcommand=suggestions_scrollbar.set)

        self.suggestions_canvas.pack(side='right', fill='both', expand=True)
        suggestions_scrollbar.pack(side='left', fill='y')

        # إطار داخلي للاقتراحات
        self.suggestions_inner_frame = tk.Frame(self.suggestions_canvas, bg=self.colors['bg_tertiary'])
        self.suggestions_canvas.create_window((0, 0), window=self.suggestions_inner_frame, anchor='ne')

        # معاينة مفصلة
        preview_frame = tk.LabelFrame(
            results_frame,
            text="👁️ معاينة مفصلة",
            font=self.arabic_font_bold,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            bd=2,
            relief='groove'
        )
        preview_frame.pack(side='left', fill='both', expand=True)

        # منطقة المعاينة
        preview_container = tk.Frame(preview_frame, bg=self.colors['bg_tertiary'])
        preview_container.pack(fill='both', expand=True, padx=8, pady=8)

        self.ai_preview_canvas = tk.Canvas(
            preview_container,
            width=300,
            height=300,
            bg='white',
            relief='solid',
            bd=2,
            highlightthickness=0
        )
        self.ai_preview_canvas.pack(pady=10)

        # معلومات المعاينة
        self.preview_info = tk.Label(
            preview_container,
            text="اختر أيقونة من القائمة لمعاينتها",
            font=self.arabic_font,
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_tertiary'],
            wraplength=280,
            justify='center'
        )
        self.preview_info.pack(pady=8)

        # أزرار المعاينة
        preview_buttons = tk.Frame(preview_container, bg=self.colors['bg_tertiary'])
        preview_buttons.pack(pady=15)

        apply_btn = self.create_glass_button(
            preview_buttons, "✅", self.apply_selected_suggestion,
            self.colors['success'], "تطبيق الأيقونة المحددة"
        )
        apply_btn.pack(side='right', padx=8)

        edit_btn = self.create_glass_button(
            preview_buttons, "✏️", self.edit_selected_suggestion,
            self.colors['accent_blue'], "تحرير الأيقونة"
        )
        edit_btn.pack(side='right', padx=8)

        save_btn = self.create_glass_button(
            preview_buttons, "💾", self.save_selected_suggestion,
            self.colors['accent_purple'], "حفظ الأيقونة"
        )
        save_btn.pack(side='right', padx=8)

        # عرض التحليل الافتراضي
        self.display_default_analysis()

    def clear_placeholder(self, event):
        """مسح النص التلقائي"""
        current_text = self.custom_prompt_text.get('1.0', tk.END).strip()
        if current_text.startswith('اكتب وصف الأيقونة'):
            self.custom_prompt_text.delete('1.0', tk.END)

    def display_default_analysis(self):
        """عرض التحليل الافتراضي"""
        default_text = """🎯 تحليل تلقائي للمشروع:

📝 النوع: تطبيق واجهة رسومية
🎨 النمط المقترح: عصري وأنيق
🎭 المزاج: احترافي وودود
📊 مستوى الثقة: 85%

🔑 الكلمات المفتاحية:
تطبيق، واجهة، أدوات، تصميم، إبداع

🎨 الألوان المقترحة:
أزرق ملكي، أخضر زمردي، برتقالي دافئ

💡 جاهز لتوليد أيقونات مخصصة!
استخدم الوصف المخصص أو حلل ملف كود"""

        self.analysis_text.delete('1.0', tk.END)
        self.analysis_text.insert('1.0', default_text)

    def create_preview_panel(self, parent):
        """إنشاء لوحة المعاينة العربية"""
        preview_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], width=350, relief='solid', bd=1)
        preview_frame.pack(side='right', fill='y', padx=(10, 0))
        preview_frame.pack_propagate(False)

        # عنوان اللوحة
        title_label = tk.Label(
            preview_frame,
            text="🖼️ معاينة الصورة والتحكم",
            font=self.arabic_font_large,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        title_label.pack(pady=15)

        # منطقة سحب وإفلات
        drop_frame = tk.LabelFrame(
            preview_frame,
            text="📁 سحب وإفلات الصور",
            font=self.arabic_font_bold,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            bd=2,
            relief='groove'
        )
        drop_frame.pack(fill='x', padx=15, pady=10)

        self.drop_area = tk.Label(
            drop_frame,
            text="🖼️\n\nاسحب الصورة هنا\nأو انقر للتصفح\n\nالتنسيقات المدعومة:\nPNG, JPG, JPEG, BMP, GIF",
            font=self.arabic_font,
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_tertiary'],
            relief='ridge',
            bd=2,
            height=8,
            cursor='hand2',
            justify='center'
        )
        self.drop_area.pack(fill='x', padx=8, pady=8)
        self.drop_area.bind('<Button-1>', lambda e: self.open_image())

        # معاينة الصورة الأصلية
        original_frame = tk.LabelFrame(
            preview_frame,
            text="📷 الصورة الأصلية",
            font=self.arabic_font_bold,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            bd=2,
            relief='groove'
        )
        original_frame.pack(fill='x', padx=15, pady=10)

        self.original_preview = tk.Label(
            original_frame,
            text="لا توجد صورة",
            font=self.arabic_font,
            fg=self.colors['text_muted'],
            bg=self.colors['bg_tertiary'],
            width=25,
            height=8
        )
        self.original_preview.pack(padx=8, pady=8)

        # معاينة الصورة المعدلة
        edited_frame = tk.LabelFrame(
            preview_frame,
            text="🎨 الصورة المعدلة",
            font=self.arabic_font_bold,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            bd=2,
            relief='groove'
        )
        edited_frame.pack(fill='x', padx=15, pady=10)

        self.edited_preview = tk.Label(
            edited_frame,
            text="لا توجد تعديلات",
            font=self.arabic_font,
            fg=self.colors['text_muted'],
            bg=self.colors['bg_tertiary'],
            width=25,
            height=8
        )
        self.edited_preview.pack(padx=8, pady=8)

        # أدوات التحكم السريع
        controls_frame = tk.LabelFrame(
            preview_frame,
            text="⚙️ تحكم سريع",
            font=self.arabic_font_bold,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            bd=2,
            relief='groove'
        )
        controls_frame.pack(fill='x', padx=15, pady=10)

        # أزرار التحكم السريع
        quick_buttons = [
            ("🔄", "تدوير 90°", self.rotate_90, self.colors['accent_blue']),
            ("↔️", "انعكاس أفقي", self.flip_horizontal, self.colors['accent_green']),
            ("↕️", "انعكاس عمودي", self.flip_vertical, self.colors['accent_green']),
            ("📐", "تغيير الحجم", self.resize_image, self.colors['accent_purple'])
        ]

        buttons_grid = tk.Frame(controls_frame, bg=self.colors['bg_secondary'])
        buttons_grid.pack(padx=8, pady=8)

        for i, (icon, tooltip, command, color) in enumerate(quick_buttons):
            row, col = i // 2, i % 2
            btn = self.create_glass_button(buttons_grid, icon, command, color, tooltip)
            btn.grid(row=row, column=col, padx=5, pady=5, sticky='ew')

        # تكوين الشبكة
        buttons_grid.columnconfigure(0, weight=1)
        buttons_grid.columnconfigure(1, weight=1)

        # معلومات الصورة
        info_frame = tk.LabelFrame(
            preview_frame,
            text="📊 معلومات الصورة",
            font=self.arabic_font_bold,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            bd=2,
            relief='groove'
        )
        info_frame.pack(fill='x', padx=15, pady=10)

        self.image_info = tk.Label(
            info_frame,
            text="لا توجد صورة محملة",
            font=self.arabic_font,
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_secondary'],
            justify='right',
            anchor='e'
        )
        self.image_info.pack(fill='x', padx=8, pady=8)

    def create_canvas_area(self, parent):
        """إنشاء منطقة الرسم العربية"""
        canvas_frame = tk.Frame(parent, bg=self.colors['bg_primary'])
        canvas_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))

        # شريط أدوات الرسم العربي
        canvas_toolbar = tk.Frame(canvas_frame, bg=self.colors['bg_tertiary'], height=60)
        canvas_toolbar.pack(fill='x', pady=(0, 10))
        canvas_toolbar.pack_propagate(False)

        # أزرار الزوم (يمين)
        zoom_frame = tk.Frame(canvas_toolbar, bg=self.colors['bg_tertiary'])
        zoom_frame.pack(side='right', padx=20, pady=10)

        zoom_buttons = [
            ("🔍+", "تكبير", self.zoom_in, self.colors['accent_green']),
            ("🔍-", "تصغير", self.zoom_out, self.colors['accent_green']),
            ("🎯", "ملائمة", self.zoom_fit, self.colors['accent_blue']),
            ("📐", "حجم فعلي", self.zoom_actual, self.colors['accent_purple'])
        ]

        for icon, tooltip, command, color in zoom_buttons:
            btn = self.create_glass_button(zoom_frame, icon, command, color, tooltip)
            btn.pack(side='right', padx=3)

        # إعدادات الشبكة (وسط)
        grid_frame = tk.Frame(canvas_toolbar, bg=self.colors['bg_tertiary'])
        grid_frame.pack(side='right', padx=30, pady=10)

        self.grid_var = tk.BooleanVar(value=self.show_grid)
        grid_check = tk.Checkbutton(
            grid_frame,
            text="📐 إظهار الشبكة",
            variable=self.grid_var,
            command=self.toggle_grid,
            font=self.arabic_font,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_tertiary'],
            selectcolor=self.colors['accent_blue'],
            activebackground=self.colors['bg_tertiary'],
            activeforeground=self.colors['text_primary']
        )
        grid_check.pack()

        # معلومات اللوحة (يسار)
        info_frame = tk.Frame(canvas_toolbar, bg=self.colors['bg_tertiary'])
        info_frame.pack(side='left', padx=20, pady=10)

        self.canvas_info = tk.Label(
            info_frame,
            text=f"📐 {self.canvas_size}×{self.canvas_size}px | 🔍 {int(self.zoom_level * 100)}%",
            font=self.arabic_font,
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_tertiary']
        )
        self.canvas_info.pack()

        # منطقة الرسم مع إطار عصري
        canvas_container = tk.Frame(canvas_frame, bg=self.colors['bg_primary'], relief='solid', bd=2)
        canvas_container.pack(fill='both', expand=True)

        # Canvas للرسم
        display_size = int(self.canvas_size * self.zoom_level)
        self.canvas = tk.Canvas(
            canvas_container,
            bg='white',
            width=display_size,
            height=display_size,
            scrollregion=(0, 0, display_size, display_size),
            relief='flat',
            highlightthickness=0,
            bd=0
        )

        # أشرطة التمرير العصرية
        h_scrollbar = tk.Scrollbar(
            canvas_container,
            orient='horizontal',
            command=self.canvas.xview,
            bg=self.colors['bg_tertiary'],
            troughcolor=self.colors['bg_card'],
            activebackground=self.colors['accent_blue']
        )
        v_scrollbar = tk.Scrollbar(
            canvas_container,
            orient='vertical',
            command=self.canvas.yview,
            bg=self.colors['bg_tertiary'],
            troughcolor=self.colors['bg_card'],
            activebackground=self.colors['accent_blue']
        )

        self.canvas.configure(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)

        # تخطيط العناصر
        self.canvas.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')

        # ربط أحداث الماوس
        self.canvas.bind('<Button-1>', self.on_canvas_click)
        self.canvas.bind('<B1-Motion>', self.on_canvas_drag)
        self.canvas.bind('<ButtonRelease-1>', self.on_canvas_release)
        self.canvas.bind('<MouseWheel>', self.on_mouse_wheel)
        self.canvas.bind('<Motion>', self.on_mouse_move)

        # رسم الشبكة
        self.draw_grid()

    def create_tools_panel(self, parent):
        """إنشاء لوحة الأدوات والخصائص العربية"""
        tools_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], width=300, relief='solid', bd=1)
        tools_frame.pack(side='left', fill='y')
        tools_frame.pack_propagate(False)

        # تبويبات عربية
        notebook_frame = tk.Frame(tools_frame, bg=self.colors['bg_secondary'])
        notebook_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # إنشاء تبويبات مخصصة عربية
        self.create_arabic_tabs(notebook_frame)

    def create_arabic_tabs(self, parent):
        """إنشاء تبويبات عربية مخصصة"""
        # شريط التبويبات
        tabs_bar = tk.Frame(parent, bg=self.colors['bg_secondary'])
        tabs_bar.pack(fill='x', pady=(0, 10))

        # محتوى التبويبات
        self.tabs_content = tk.Frame(parent, bg=self.colors['bg_secondary'])
        self.tabs_content.pack(fill='both', expand=True)

        # التبويبات العربية
        self.tabs = {
            'tools': {'name': '🛠️ الأدوات', 'frame': None, 'button': None},
            'layers': {'name': '🗂️ الطبقات', 'frame': None, 'button': None},
            'properties': {'name': '⚙️ الخصائص', 'frame': None, 'button': None},
            'history': {'name': '📜 التاريخ', 'frame': None, 'button': None}
        }

        self.current_tab = 'tools'

        # إنشاء أزرار التبويبات من اليمين لليسار
        tab_order = ['history', 'properties', 'layers', 'tools']
        for tab_id in tab_order:
            tab_info = self.tabs[tab_id]
            btn = tk.Button(
                tabs_bar,
                text=tab_info['name'],
                font=self.arabic_font_bold,
                fg=self.colors['text_primary'],
                bg=self.colors['accent_blue'] if tab_id == self.current_tab else self.colors['bg_tertiary'],
                relief='flat',
                padx=12,
                pady=8,
                command=lambda t=tab_id: self.switch_tab(t)
            )
            btn.pack(side='right', padx=1)
            tab_info['button'] = btn
            self.add_hover_effect(btn, self.colors['accent_blue'] if tab_id == self.current_tab else self.colors['bg_tertiary'])

        # إنشاء محتوى التبويبات
        for tab_id in self.tabs.keys():
            frame = tk.Frame(self.tabs_content, bg=self.colors['bg_secondary'])
            self.tabs[tab_id]['frame'] = frame

            if tab_id == 'tools':
                self.create_tools_tab(frame)
            elif tab_id == 'layers':
                self.create_layers_tab(frame)
            elif tab_id == 'properties':
                self.create_properties_tab(frame)
            elif tab_id == 'history':
                self.create_history_tab(frame)

        # عرض التبويب الحالي
        self.switch_tab(self.current_tab)

    def create_tools_tab(self, parent):
        """إنشاء تبويب الأدوات"""
        # أدوات الرسم
        drawing_frame = tk.LabelFrame(
            parent,
            text="🎨 أدوات الرسم",
            font=self.arabic_font_bold,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            bd=2,
            relief='groove'
        )
        drawing_frame.pack(fill='x', padx=5, pady=5)

        drawing_tools = [
            ("🖌️", "brush", "فرشاة احترافية"),
            ("✏️", "pencil", "قلم رصاص دقيق"),
            ("🖍️", "eraser", "ممحاة ذكية"),
            ("🪣", "bucket", "دلو الطلاء"),
            ("💧", "dropper", "قطارة الألوان"),
            ("📏", "line", "خط مستقيم")
        ]

        self.tool_buttons = {}
        tools_grid = tk.Frame(drawing_frame, bg=self.colors['bg_secondary'])
        tools_grid.pack(padx=8, pady=8)

        for i, (icon, tool_id, tooltip) in enumerate(drawing_tools):
            row, col = i // 3, i % 3
            btn = tk.Button(
                tools_grid,
                text=icon,
                font=('Segoe UI', 18),
                command=lambda t=tool_id: self.select_tool(t),
                bg=self.colors['bg_tertiary'],
                fg=self.colors['text_primary'],
                relief='flat',
                width=4,
                height=2,
                cursor='hand2',
                bd=0
            )
            btn.grid(row=row, column=col, padx=3, pady=3)
            self.tool_buttons[tool_id] = btn
            self.create_tooltip(btn, tooltip)
            self.add_hover_effect(btn, self.colors['bg_tertiary'])

        # أدوات الأشكال
        shapes_frame = tk.LabelFrame(
            parent,
            text="📐 الأشكال الهندسية",
            font=self.arabic_font_bold,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            bd=2,
            relief='groove'
        )
        shapes_frame.pack(fill='x', padx=5, pady=5)

        shape_tools = [
            ("⬜", "rectangle", "مستطيل"),
            ("⭕", "circle", "دائرة"),
            ("🔺", "triangle", "مثلث"),
            ("⭐", "star", "نجمة"),
            ("💎", "diamond", "معين"),
            ("🔶", "hexagon", "سداسي")
        ]

        shapes_grid = tk.Frame(shapes_frame, bg=self.colors['bg_secondary'])
        shapes_grid.pack(padx=8, pady=8)

        for i, (icon, tool_id, tooltip) in enumerate(shape_tools):
            row, col = i // 3, i % 3
            btn = tk.Button(
                shapes_grid,
                text=icon,
                font=('Segoe UI', 18),
                command=lambda t=tool_id: self.select_tool(t),
                bg=self.colors['bg_tertiary'],
                fg=self.colors['text_primary'],
                relief='flat',
                width=4,
                height=2,
                cursor='hand2',
                bd=0
            )
            btn.grid(row=row, column=col, padx=3, pady=3)
            self.tool_buttons[tool_id] = btn
            self.create_tooltip(btn, tooltip)
            self.add_hover_effect(btn, self.colors['bg_tertiary'])

        # إعدادات الأداة
        settings_frame = tk.LabelFrame(
            parent,
            text="⚙️ إعدادات الأداة",
            font=self.arabic_font_bold,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            bd=2,
            relief='groove'
        )
        settings_frame.pack(fill='x', padx=5, pady=5)

        # حجم الفرشاة
        size_frame = tk.Frame(settings_frame, bg=self.colors['bg_secondary'])
        size_frame.pack(fill='x', padx=8, pady=5)

        tk.Label(
            size_frame,
            text="📏 حجم الفرشاة:",
            font=self.arabic_font,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        ).pack(anchor='e')

        self.brush_size_var = tk.IntVar(value=self.brush_size)
        brush_scale = tk.Scale(
            size_frame,
            from_=1,
            to=100,
            orient='horizontal',
            variable=self.brush_size_var,
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            troughcolor=self.colors['bg_tertiary'],
            activebackground=self.colors['accent_blue'],
            command=self.update_brush_size,
            font=self.arabic_font
        )
        brush_scale.pack(fill='x', pady=3)

        # الشفافية
        opacity_frame = tk.Frame(settings_frame, bg=self.colors['bg_secondary'])
        opacity_frame.pack(fill='x', padx=8, pady=5)

        tk.Label(
            opacity_frame,
            text="🌫️ الشفافية:",
            font=self.arabic_font,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        ).pack(anchor='e')

        self.opacity_var = tk.IntVar(value=self.opacity)
        opacity_scale = tk.Scale(
            opacity_frame,
            from_=1,
            to=100,
            orient='horizontal',
            variable=self.opacity_var,
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            troughcolor=self.colors['bg_tertiary'],
            activebackground=self.colors['accent_purple'],
            command=self.update_opacity,
            font=self.arabic_font
        )
        opacity_scale.pack(fill='x', pady=3)

        # اختيار اللون العصري
        color_frame = tk.Frame(settings_frame, bg=self.colors['bg_secondary'])
        color_frame.pack(fill='x', padx=8, pady=10)

        tk.Label(
            color_frame,
            text="🎨 اللون الحالي:",
            font=self.arabic_font,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        ).pack(anchor='e')

        color_display_frame = tk.Frame(color_frame, bg=self.colors['bg_secondary'])
        color_display_frame.pack(fill='x', pady=5)

        self.color_display = tk.Label(
            color_display_frame,
            bg=self.current_color,
            width=10,
            height=3,
            relief='solid',
            bd=2,
            cursor='hand2'
        )
        self.color_display.pack(side='right')
        self.color_display.bind('<Button-1>', self.choose_color)

        # ألوان سريعة
        quick_colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF', '#000000', '#FFFFFF']
        quick_frame = tk.Frame(color_display_frame, bg=self.colors['bg_secondary'])
        quick_frame.pack(side='left', padx=10)

        for i, color in enumerate(quick_colors):
            row, col = i // 4, i % 4
            color_btn = tk.Label(
                quick_frame,
                bg=color,
                width=2,
                height=1,
                relief='solid',
                bd=1,
                cursor='hand2'
            )
            color_btn.grid(row=row, column=col, padx=1, pady=1)
            color_btn.bind('<Button-1>', lambda e, c=color: self.set_color(c))

        # تحديد الأداة الافتراضية
        self.select_tool("brush")

    def create_layers_tab(self, parent):
        """إنشاء تبويب الطبقات"""
        # أزرار إدارة الطبقات
        layers_controls = tk.Frame(parent, bg=self.colors['bg_secondary'])
        layers_controls.pack(fill='x', padx=5, pady=5)

        control_buttons = [
            ("➕", "طبقة جديدة", self.add_new_layer, self.colors['accent_green']),
            ("📋", "تكرار", self.duplicate_layer, self.colors['accent_blue']),
            ("🗑️", "حذف", self.delete_layer, self.colors['error'])
        ]

        for icon, tooltip, command, color in control_buttons:
            btn = self.create_glass_button(layers_controls, icon, command, color, tooltip)
            btn.pack(side='right', padx=2)

        # قائمة الطبقات
        layers_frame = tk.LabelFrame(
            parent,
            text="🗂️ قائمة الطبقات",
            font=self.arabic_font_bold,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            bd=2,
            relief='groove'
        )
        layers_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # إطار التمرير للطبقات
        layers_scroll_frame = tk.Frame(layers_frame, bg=self.colors['bg_secondary'])
        layers_scroll_frame.pack(fill='both', expand=True, padx=5, pady=5)

        self.layers_listbox = tk.Listbox(
            layers_scroll_frame,
            font=self.arabic_font,
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            selectbackground=self.colors['accent_blue'],
            relief='flat',
            bd=0,
            activestyle='none'
        )

        layers_scrollbar = tk.Scrollbar(
            layers_scroll_frame,
            orient='vertical',
            command=self.layers_listbox.yview,
            bg=self.colors['bg_tertiary'],
            troughcolor=self.colors['bg_card'],
            activebackground=self.colors['accent_blue']
        )

        self.layers_listbox.configure(yscrollcommand=layers_scrollbar.set)
        self.layers_listbox.bind('<<ListboxSelect>>', self.on_layer_select)

        self.layers_listbox.pack(side='right', fill='both', expand=True)
        layers_scrollbar.pack(side='left', fill='y')

    def create_properties_tab(self, parent):
        """إنشاء تبويب الخصائص"""
        # معلومات الصورة
        info_frame = tk.LabelFrame(
            parent,
            text="📊 معلومات الصورة",
            font=self.arabic_font_bold,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            bd=2,
            relief='groove'
        )
        info_frame.pack(fill='x', padx=5, pady=5)

        self.properties_info = tk.Label(
            info_frame,
            text="لا توجد صورة محملة",
            font=self.arabic_font,
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_secondary'],
            justify='right',
            anchor='e'
        )
        self.properties_info.pack(fill='x', padx=8, pady=8)

        # أدوات التحويل
        transform_frame = tk.LabelFrame(
            parent,
            text="🔄 أدوات التحويل",
            font=self.arabic_font_bold,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            bd=2,
            relief='groove'
        )
        transform_frame.pack(fill='x', padx=5, pady=5)

        transform_buttons = [
            ("🔄", "تدوير 90°", self.rotate_90, self.colors['accent_blue']),
            ("↔️", "انعكاس أفقي", self.flip_horizontal, self.colors['accent_green']),
            ("↕️", "انعكاس عمودي", self.flip_vertical, self.colors['accent_green']),
            ("📐", "تغيير الحجم", self.resize_image, self.colors['accent_purple'])
        ]

        transform_grid = tk.Frame(transform_frame, bg=self.colors['bg_secondary'])
        transform_grid.pack(padx=8, pady=8)

        for i, (icon, tooltip, command, color) in enumerate(transform_buttons):
            row, col = i // 2, i % 2
            btn = self.create_glass_button(transform_grid, icon, command, color, tooltip)
            btn.grid(row=row, column=col, padx=3, pady=3, sticky='ew')

        transform_grid.columnconfigure(0, weight=1)
        transform_grid.columnconfigure(1, weight=1)

        # تأثيرات سريعة
        effects_frame = tk.LabelFrame(
            parent,
            text="✨ تأثيرات سريعة",
            font=self.arabic_font_bold,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            bd=2,
            relief='groove'
        )
        effects_frame.pack(fill='x', padx=5, pady=5)

        effects_buttons = [
            ("🌫️", "ضبابية", lambda: self.apply_filter("blur"), self.colors['accent_cyan']),
            ("⚡", "حدة", lambda: self.apply_filter("sharpen"), self.colors['accent_orange']),
            ("🎭", "نقش", lambda: self.apply_filter("emboss"), self.colors['accent_pink']),
            ("🔍", "كشف الحواف", lambda: self.apply_filter("edge"), self.colors['accent_purple'])
        ]

        effects_grid = tk.Frame(effects_frame, bg=self.colors['bg_secondary'])
        effects_grid.pack(padx=8, pady=8)

        for i, (icon, tooltip, command, color) in enumerate(effects_buttons):
            row, col = i // 2, i % 2
            btn = self.create_glass_button(effects_grid, icon, command, color, tooltip)
            btn.grid(row=row, column=col, padx=3, pady=3, sticky='ew')

        effects_grid.columnconfigure(0, weight=1)
        effects_grid.columnconfigure(1, weight=1)

    def create_history_tab(self, parent):
        """إنشاء تبويب التاريخ"""
        # أزرار التحكم في التاريخ
        history_controls = tk.Frame(parent, bg=self.colors['bg_secondary'])
        history_controls.pack(fill='x', padx=5, pady=5)

        history_buttons = [
            ("↶", "تراجع", self.undo, self.colors['warning']),
            ("↷", "إعادة", self.redo, self.colors['warning']),
            ("🗑️", "مسح التاريخ", self.clear_history, self.colors['error'])
        ]

        for icon, tooltip, command, color in history_buttons:
            btn = self.create_glass_button(history_controls, icon, command, color, tooltip)
            btn.pack(side='right', padx=2)

        # قائمة التاريخ
        history_frame = tk.LabelFrame(
            parent,
            text="📜 سجل العمليات",
            font=self.arabic_font_bold,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            bd=2,
            relief='groove'
        )
        history_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # إطار التمرير للتاريخ
        history_scroll_frame = tk.Frame(history_frame, bg=self.colors['bg_secondary'])
        history_scroll_frame.pack(fill='both', expand=True, padx=5, pady=5)

        self.history_listbox = tk.Listbox(
            history_scroll_frame,
            font=self.arabic_font,
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            selectbackground=self.colors['accent_blue'],
            relief='flat',
            bd=0,
            activestyle='none'
        )

        history_scrollbar = tk.Scrollbar(
            history_scroll_frame,
            orient='vertical',
            command=self.history_listbox.yview,
            bg=self.colors['bg_tertiary'],
            troughcolor=self.colors['bg_card'],
            activebackground=self.colors['accent_blue']
        )

        self.history_listbox.configure(yscrollcommand=history_scrollbar.set)
        self.history_listbox.bind('<<ListboxSelect>>', self.on_history_select)

        self.history_listbox.pack(side='right', fill='both', expand=True)
        history_scrollbar.pack(side='left', fill='y')

    def create_arabic_status_bar(self):
        """إنشاء شريط حالة عربي"""
        status_frame = tk.Frame(self.root, bg=self.colors['bg_tertiary'], height=35)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)

        # رسالة الحالة (يمين)
        self.status_label = tk.Label(
            status_frame,
            text="🎨 محرر الأيقونات المتكامل - جاهز للإبداع",
            font=self.arabic_font,
            fg=self.colors['text_primary'],
            bg=self.colors['bg_tertiary']
        )
        self.status_label.pack(side='right', padx=20, pady=8)

        # معلومات الطبقة الحالية (وسط)
        self.layer_info_label = tk.Label(
            status_frame,
            text="",
            font=self.arabic_font,
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_tertiary']
        )
        self.layer_info_label.pack(side='right', padx=20, pady=8)

        # معلومات الماوس (يسار)
        self.mouse_pos_label = tk.Label(
            status_frame,
            text="",
            font=self.arabic_font,
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_tertiary']
        )
        self.mouse_pos_label.pack(side='left', padx=20, pady=8)

    # ==================== وظائف مساعدة ====================

    def show_context_menu(self, items):
        """عرض قائمة سياق عربية"""
        menu = tk.Menu(self.root, tearoff=0)
        menu.configure(
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            activebackground=self.colors['accent_blue'],
            activeforeground='white',
            font=self.arabic_font
        )

        for item_text, command in items:
            if item_text == "---":
                menu.add_separator()
            else:
                menu.add_command(label=item_text, command=command)

        try:
            menu.tk_popup(self.root.winfo_pointerx(), self.root.winfo_pointery())
        finally:
            menu.grab_release()

    def switch_tab(self, tab_id):
        """تبديل التبويب"""
        # إخفاء جميع التبويبات
        for tab_info in self.tabs.values():
            if tab_info['frame']:
                tab_info['frame'].pack_forget()
            if tab_info['button']:
                tab_info['button'].configure(bg=self.colors['bg_tertiary'])

        # عرض التبويب المحدد
        if tab_id in self.tabs:
            self.tabs[tab_id]['frame'].pack(fill='both', expand=True)
            self.tabs[tab_id]['button'].configure(bg=self.colors['accent_blue'])
            self.current_tab = tab_id

    def log_message(self, message):
        """تسجيل رسالة"""
        self.status_label.configure(text=message)
        self.root.update_idletasks()

    def update_mouse_position(self, x, y):
        """تحديث موقع الماوس"""
        self.mouse_pos_label.configure(text=f"📍 X: {int(x)}, Y: {int(y)}")

    def update_layer_info(self):
        """تحديث معلومات الطبقة"""
        if self.current_layer:
            info = f"🗂️ {self.current_layer.name} | 🌫️ {int(self.current_layer.opacity * 100)}%"
            self.layer_info_label.configure(text=info)
        else:
            self.layer_info_label.configure(text="")

    def update_canvas_info(self):
        """تحديث معلومات اللوحة"""
        self.canvas_info.configure(
            text=f"📐 {self.canvas_size}×{self.canvas_size}px | 🔍 {int(self.zoom_level * 100)}%"
        )

    def update_image_info(self, image=None):
        """تحديث معلومات الصورة"""
        if image:
            width, height = image.size
            mode = image.mode
            info_text = f"""📐 الأبعاد: {width} × {height}
🎨 النمط: {mode}
📊 الحجم: {width * height} بكسل
💾 الذاكرة: {(width * height * 4) // 1024} KB"""
        else:
            info_text = "لا توجد صورة محملة"

        self.image_info.configure(text=info_text)
        if hasattr(self, 'properties_info'):
            self.properties_info.configure(text=info_text)

    def create_default_layer(self):
        """إنشاء طبقة افتراضية"""
        default_image = Image.new('RGBA', (self.canvas_size, self.canvas_size), (255, 255, 255, 0))
        layer = Layer(
            id="layer_0",
            name="الخلفية",
            image=default_image,
            visible=True,
            locked=False,
            opacity=1.0
        )
        self.layers.append(layer)
        self.current_layer = layer
        self.update_layers_list()
        self.update_layer_info()
        self.save_state("إنشاء طبقة افتراضية")

    def update_layers_list(self):
        """تحديث قائمة الطبقات"""
        if hasattr(self, 'layers_listbox'):
            self.layers_listbox.delete(0, tk.END)
            for i, layer in enumerate(reversed(self.layers)):
                status = "👁️" if layer.visible else "🙈"
                lock = "🔒" if layer.locked else "🔓"
                opacity = f"{int(layer.opacity * 100)}%"
                item_text = f"{status} {lock} {layer.name} ({opacity})"
                self.layers_listbox.insert(0, item_text)

                # تمييز الطبقة الحالية
                if layer == self.current_layer:
                    self.layers_listbox.selection_set(len(self.layers) - 1 - i)

    def save_state(self, action_name):
        """حفظ حالة للتراجع"""
        # حفظ حالة الطبقات
        state = {
            'layers': [],
            'current_layer_id': self.current_layer.id if self.current_layer else None,
            'action': action_name
        }

        for layer in self.layers:
            layer_data = {
                'id': layer.id,
                'name': layer.name,
                'image': layer.image.copy(),
                'visible': layer.visible,
                'locked': layer.locked,
                'opacity': layer.opacity,
                'x': layer.x,
                'y': layer.y
            }
            state['layers'].append(layer_data)

        # إضافة للتاريخ
        if self.history_index < len(self.history) - 1:
            self.history = self.history[:self.history_index + 1]

        self.history.append(state)

        # الحد الأقصى للتاريخ
        if len(self.history) > self.max_history:
            self.history.pop(0)
        else:
            self.history_index += 1

        self.update_history_list()

    def update_history_list(self):
        """تحديث قائمة التاريخ"""
        if hasattr(self, 'history_listbox'):
            self.history_listbox.delete(0, tk.END)
            for i, state in enumerate(self.history):
                marker = "👉" if i == self.history_index else "  "
                self.history_listbox.insert(tk.END, f"{marker} {state['action']}")

            if self.history_index >= 0:
                self.history_listbox.selection_set(self.history_index)
                self.history_listbox.see(self.history_index)

    # ==================== وظائف الأدوات ====================

    def select_tool(self, tool_name):
        """تحديد أداة"""
        # إعادة تعيين ألوان الأزرار
        for btn in self.tool_buttons.values():
            btn.configure(bg=self.colors['bg_tertiary'])

        # تمييز الأداة المحددة
        if tool_name in self.tool_buttons:
            self.tool_buttons[tool_name].configure(bg=self.colors['accent_blue'])

        self.current_tool = tool_name
        self.log_message(f"🛠️ تم تحديد أداة: {tool_name}")

    def choose_color(self, event=None):
        """اختيار لون"""
        color = colorchooser.askcolor(title="اختر لون", color=self.current_color)[1]
        if color:
            self.set_color(color)

    def set_color(self, color):
        """تعيين لون"""
        self.current_color = color
        self.color_display.configure(bg=color)
        self.log_message(f"🎨 تم تحديد اللون: {color}")

    def update_brush_size(self, value):
        """تحديث حجم الفرشاة"""
        self.brush_size = int(value)
        self.log_message(f"📏 حجم الفرشاة: {self.brush_size}px")

    def update_opacity(self, value):
        """تحديث الشفافية"""
        self.opacity = int(value)
        self.log_message(f"🌫️ الشفافية: {self.opacity}%")

    # ==================== وظائف الرسم ====================

    def on_canvas_click(self, event):
        """النقر على اللوحة"""
        x, y = self.canvas.canvasx(event.x), self.canvas.canvasy(event.y)
        actual_x = x / self.zoom_level
        actual_y = y / self.zoom_level

        if self.current_tool == "brush":
            self.start_brush_stroke(actual_x, actual_y)
        elif self.current_tool == "pencil":
            self.start_pencil_stroke(actual_x, actual_y)
        elif self.current_tool == "eraser":
            self.start_eraser_stroke(actual_x, actual_y)
        elif self.current_tool == "bucket":
            self.bucket_fill(actual_x, actual_y)
        elif self.current_tool == "dropper":
            self.pick_color(actual_x, actual_y)
        elif self.current_tool in ["line", "rectangle", "circle", "triangle", "star", "diamond"]:
            self.start_shape_draw(actual_x, actual_y)

        self.last_x, self.last_y = actual_x, actual_y

    def on_canvas_drag(self, event):
        """سحب على اللوحة"""
        x, y = self.canvas.canvasx(event.x), self.canvas.canvasy(event.y)
        actual_x = x / self.zoom_level
        actual_y = y / self.zoom_level

        if self.current_tool in ["brush", "pencil", "eraser"]:
            self.continue_stroke(actual_x, actual_y)
        elif self.current_tool in ["line", "rectangle", "circle", "triangle", "star", "diamond"]:
            self.continue_shape_draw(actual_x, actual_y)

        self.last_x, self.last_y = actual_x, actual_y
        self.update_mouse_position(actual_x, actual_y)

    def on_canvas_release(self, event):
        """تحرير الماوس"""
        if self.current_tool in ["brush", "pencil", "eraser"]:
            self.end_stroke()
        elif self.current_tool in ["line", "rectangle", "circle", "triangle", "star", "diamond"]:
            self.end_shape_draw()

    def on_mouse_move(self, event):
        """حركة الماوس"""
        x, y = self.canvas.canvasx(event.x), self.canvas.canvasy(event.y)
        actual_x = x / self.zoom_level
        actual_y = y / self.zoom_level
        self.update_mouse_position(actual_x, actual_y)

    def on_mouse_wheel(self, event):
        """عجلة الماوس للزوم"""
        if event.state & 0x4:  # Ctrl مضغوط
            if event.delta > 0:
                self.zoom_in()
            else:
                self.zoom_out()

    # ==================== وظائف الزوم ====================

    def zoom_in(self):
        """تكبير"""
        self.zoom_level = min(self.zoom_level * 1.5, 5.0)
        self.update_zoom()

    def zoom_out(self):
        """تصغير"""
        self.zoom_level = max(self.zoom_level / 1.5, 0.1)
        self.update_zoom()

    def zoom_fit(self):
        """ملائمة الحجم"""
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        if canvas_width > 0 and canvas_height > 0:
            zoom_x = canvas_width / self.canvas_size
            zoom_y = canvas_height / self.canvas_size
            self.zoom_level = min(zoom_x, zoom_y) * 0.9
        else:
            self.zoom_level = 0.5

        self.update_zoom()

    def zoom_actual(self):
        """الحجم الفعلي"""
        self.zoom_level = 1.0
        self.update_zoom()

    def update_zoom(self):
        """تحديث الزوم"""
        display_size = int(self.canvas_size * self.zoom_level)
        self.canvas.configure(
            width=display_size,
            height=display_size,
            scrollregion=(0, 0, display_size, display_size)
        )

        self.update_canvas_info()
        self.update_canvas_display()
        self.draw_grid()

    def toggle_grid(self):
        """تبديل عرض الشبكة"""
        self.show_grid = self.grid_var.get()
        if self.show_grid:
            self.draw_grid()
        else:
            self.canvas.delete("grid")

    def draw_grid(self):
        """رسم الشبكة"""
        if self.show_grid:
            self.canvas.delete("grid")

            grid_size = int(self.grid_size * self.zoom_level)
            display_size = int(self.canvas_size * self.zoom_level)

            # خطوط عمودية
            for x in range(0, display_size, grid_size):
                self.canvas.create_line(
                    x, 0, x, display_size,
                    fill='#e2e8f0',
                    width=1,
                    tags="grid"
                )

            # خطوط أفقية
            for y in range(0, display_size, grid_size):
                self.canvas.create_line(
                    0, y, display_size, y,
                    fill='#e2e8f0',
                    width=1,
                    tags="grid"
                )

    def update_canvas_display(self):
        """تحديث عرض اللوحة"""
        # مسح اللوحة
        self.canvas.delete("layer")

        # رسم الطبقات
        for layer in self.layers:
            if layer.visible:
                # تحويل الصورة للعرض
                display_image = layer.image.resize(
                    (int(self.canvas_size * self.zoom_level),
                     int(self.canvas_size * self.zoom_level)),
                    Image.Resampling.NEAREST
                )

                # تطبيق الشفافية
                if layer.opacity < 1.0:
                    display_image = display_image.copy()
                    alpha = display_image.split()[-1]
                    alpha = alpha.point(lambda p: int(p * layer.opacity))
                    display_image.putalpha(alpha)

                # تحويل لـ PhotoImage
                photo = ImageTk.PhotoImage(display_image)

                # رسم على اللوحة
                self.canvas.create_image(
                    layer.x * self.zoom_level,
                    layer.y * self.zoom_level,
                    anchor='nw',
                    image=photo,
                    tags="layer"
                )

                # حفظ مرجع للصورة
                if not hasattr(self, 'canvas_images'):
                    self.canvas_images = []
                self.canvas_images.append(photo)

    # ==================== وظائف الذكاء الاصطناعي ====================

    def generate_modern_icons(self):
        """توليد أيقونات عصرية"""
        self.log_message("🧠 بدء توليد الأيقونات العصرية...")

        def run_generation():
            try:
                # الحصول على الوصف المخصص
                custom_text = self.custom_prompt_text.get('1.0', tk.END).strip()

                # إنشاء تحليل مؤقت
                if custom_text and not custom_text.startswith('اكتب وصف'):
                    analysis = self.analyzer.analyze_code(custom_text)
                elif self.current_analysis:
                    analysis = self.current_analysis
                else:
                    analysis = self.analyzer.analyze_code("GUI application")

                # توليد الأيقونات
                suggestions = self.icon_generator.generate_modern_icons(analysis, count=6)
                self.icon_suggestions = suggestions

                # عرض النتائج
                self.root.after(0, self.display_icon_suggestions)
                self.root.after(0, lambda: self.log_message(f"✅ تم توليد {len(suggestions)} أيقونة عصرية"))

            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"❌ خطأ في التوليد: {e}"))

        threading.Thread(target=run_generation, daemon=True).start()

    def analyze_code(self):
        """تحليل الكود"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف الكود للتحليل",
            filetypes=[
                ("Python files", "*.py"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            self.log_message("📊 تحليل الكود جاري...")

            def run_analysis():
                try:
                    analysis = self.analyzer.analyze_code(file_path)
                    self.current_analysis = analysis

                    # عرض النتائج
                    self.root.after(0, lambda: self.display_code_analysis(analysis))
                    self.root.after(0, lambda: self.log_message("✅ تم تحليل الكود بنجاح"))

                except Exception as e:
                    self.root.after(0, lambda: self.log_message(f"❌ خطأ في التحليل: {e}"))

            threading.Thread(target=run_analysis, daemon=True).start()

    def display_code_analysis(self, analysis):
        """عرض نتائج تحليل الكود"""
        analysis_text = f"""🧠 تحليل الكود المتطور:
{"=" * 40}

🎯 نوع التطبيق: {analysis.app_type}
📝 الوصف: {analysis.description}
📊 مستوى الثقة: {int(analysis.confidence * 100)}%
🎨 النمط المقترح: {analysis.style}

🔑 الكلمات المفتاحية:
{', '.join(analysis.keywords[:10])}

🔧 الوظائف المكتشفة:
{', '.join(analysis.functions[:8])}

📚 المكتبات المستخدمة:
{', '.join(analysis.libraries[:8])}

🎨 الألوان المقترحة:
{', '.join(analysis.colors)}

✨ جاهز لتوليد أيقونات مخصصة!"""

        self.analysis_text.delete('1.0', tk.END)
        self.analysis_text.insert('1.0', analysis_text)

    def display_icon_suggestions(self):
        """عرض اقتراحات الأيقونات"""
        # مسح الاقتراحات السابقة
        for widget in self.suggestions_inner_frame.winfo_children():
            widget.destroy()

        if not self.icon_suggestions:
            no_suggestions = tk.Label(
                self.suggestions_inner_frame,
                text="لا توجد اقتراحات متاحة",
                font=self.arabic_font,
                fg=self.colors['text_muted'],
                bg=self.colors['bg_tertiary']
            )
            no_suggestions.pack(pady=20)
            return

        # عرض الاقتراحات في شبكة
        for i, suggestion in enumerate(self.icon_suggestions):
            row, col = i // 2, i % 2

            # إطار الاقتراح
            suggestion_frame = tk.Frame(
                self.suggestions_inner_frame,
                bg=self.colors['bg_card'],
                relief='solid',
                bd=1
            )
            suggestion_frame.grid(row=row, column=col, padx=5, pady=5, sticky='ew')

            # صورة مصغرة
            thumbnail = suggestion.image.resize((80, 80), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(thumbnail)

            img_label = tk.Label(
                suggestion_frame,
                image=photo,
                bg=self.colors['bg_card'],
                cursor='hand2'
            )
            img_label.pack(pady=5)
            img_label.image = photo  # حفظ مرجع

            # معلومات الاقتراح
            info_label = tk.Label(
                suggestion_frame,
                text=f"{suggestion.style}\n⭐ {suggestion.confidence:.1f}",
                font=self.arabic_font,
                fg=self.colors['text_primary'],
                bg=self.colors['bg_card'],
                justify='center'
            )
            info_label.pack(pady=2)

            # ربط النقر
            def on_click(s=suggestion):
                self.preview_suggestion(s)

            img_label.bind('<Button-1>', lambda e, s=suggestion: on_click(s))
            suggestion_frame.bind('<Button-1>', lambda e, s=suggestion: on_click(s))

        # تكوين الشبكة
        self.suggestions_inner_frame.columnconfigure(0, weight=1)
        self.suggestions_inner_frame.columnconfigure(1, weight=1)

        # تحديث منطقة التمرير
        self.suggestions_inner_frame.update_idletasks()
        self.suggestions_canvas.configure(scrollregion=self.suggestions_canvas.bbox("all"))

    def preview_suggestion(self, suggestion):
        """معاينة اقتراح"""
        # عرض الصورة الكبيرة
        preview_image = suggestion.image.resize((280, 280), Image.Resampling.LANCZOS)
        photo = ImageTk.PhotoImage(preview_image)

        self.ai_preview_canvas.delete("all")
        self.ai_preview_canvas.create_image(150, 150, image=photo)
        self.ai_preview_canvas.image = photo  # حفظ مرجع

        # عرض المعلومات
        info_text = f"""🎨 النمط: {suggestion.style}
📝 الوصف: {suggestion.description}
⭐ مستوى الثقة: {suggestion.confidence:.1f}
🎨 الألوان: {', '.join(suggestion.colors[:3])}

انقر "تطبيق" لإضافة الأيقونة كطبقة جديدة"""

        self.preview_info.configure(text=info_text)
        self.selected_suggestion = suggestion

    def apply_selected_suggestion(self):
        """تطبيق الاقتراح المحدد"""
        if hasattr(self, 'selected_suggestion'):
            suggestion = self.selected_suggestion

            # إنشاء طبقة جديدة
            layer_id = f"ai_layer_{len(self.layers)}"
            layer = Layer(
                id=layer_id,
                name=f"AI - {suggestion.style}",
                image=suggestion.image.resize((self.canvas_size, self.canvas_size), Image.Resampling.LANCZOS),
                visible=True,
                locked=False,
                opacity=1.0
            )

            self.layers.append(layer)
            self.current_layer = layer
            self.update_layers_list()
            self.update_layer_info()
            self.update_canvas_display()
            self.save_state(f"تطبيق أيقونة AI: {suggestion.style}")

            self.log_message(f"✅ تم تطبيق الأيقونة: {suggestion.style}")
        else:
            self.log_message("❌ لم يتم تحديد أيقونة")

    def edit_selected_suggestion(self):
        """تحرير الاقتراح المحدد"""
        if hasattr(self, 'selected_suggestion'):
            self.apply_selected_suggestion()
            self.log_message("🎨 تم إضافة الأيقونة للتحرير")
        else:
            self.log_message("❌ لم يتم تحديد أيقونة")

    def save_selected_suggestion(self):
        """حفظ الاقتراح المحدد"""
        if hasattr(self, 'selected_suggestion'):
            suggestion = self.selected_suggestion

            file_path = filedialog.asksaveasfilename(
                title="حفظ الأيقونة",
                defaultextension=".png",
                filetypes=[
                    ("PNG files", "*.png"),
                    ("ICO files", "*.ico"),
                    ("JPEG files", "*.jpg"),
                    ("All files", "*.*")
                ]
            )

            if file_path:
                try:
                    if file_path.lower().endswith('.ico'):
                        # حفظ كأيقونة ICO
                        sizes = [(16,16), (32,32), (48,48), (64,64), (128,128), (256,256)]
                        images = []
                        for size in sizes:
                            resized = suggestion.image.resize(size, Image.Resampling.LANCZOS)
                            images.append(resized)
                        images[0].save(file_path, format='ICO', sizes=[(img.width, img.height) for img in images])
                    else:
                        suggestion.image.save(file_path)

                    self.log_message(f"💾 تم حفظ الأيقونة: {file_path}")
                except Exception as e:
                    self.log_message(f"❌ خطأ في الحفظ: {e}")
        else:
            self.log_message("❌ لم يتم تحديد أيقونة")

    def regenerate_icons(self):
        """إعادة توليد الأيقونات"""
        self.icon_suggestions = []
        self.ai_preview_canvas.delete("all")
        self.preview_info.configure(text="اختر أيقونة من القائمة لمعاينتها")
        self.generate_modern_icons()

    # ==================== وظائف الملف ====================

    def new_project(self):
        """مشروع جديد"""
        # مسح الطبقات
        self.layers = []
        self.current_layer = None
        self.history = []
        self.history_index = -1

        # إنشاء طبقة افتراضية
        self.create_default_layer()

        # مسح اللوحة
        self.canvas.delete("all")
        self.update_canvas_display()
        self.draw_grid()

        self.log_message("🆕 تم إنشاء مشروع جديد")

    def open_image(self):
        """فتح صورة"""
        file_path = filedialog.askopenfilename(
            title="اختر صورة",
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.bmp *.gif"),
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                # تحميل الصورة
                image = Image.open(file_path)

                # تحويل لـ RGBA
                if image.mode != 'RGBA':
                    image = image.convert('RGBA')

                # تغيير الحجم إذا لزم الأمر
                if image.size != (self.canvas_size, self.canvas_size):
                    image = image.resize((self.canvas_size, self.canvas_size), Image.Resampling.LANCZOS)

                # حفظ الصورة الأصلية
                self.original_image = image.copy()
                self.current_image = image.copy()

                # إنشاء طبقة جديدة
                layer_id = f"image_layer_{len(self.layers)}"
                layer = Layer(
                    id=layer_id,
                    name=f"صورة - {os.path.basename(file_path)}",
                    image=image,
                    visible=True,
                    locked=False,
                    opacity=1.0
                )

                self.layers.append(layer)
                self.current_layer = layer

                # تحديث الواجهة
                self.update_layers_list()
                self.update_layer_info()
                self.update_canvas_display()
                self.update_image_info(image)
                self.update_preview_images()
                self.save_state(f"فتح صورة: {os.path.basename(file_path)}")

                self.log_message(f"📂 تم فتح الصورة: {os.path.basename(file_path)}")

            except Exception as e:
                self.log_message(f"❌ خطأ في فتح الصورة: {e}")

    def update_preview_images(self):
        """تحديث صور المعاينة"""
        if self.original_image:
            # معاينة الصورة الأصلية
            original_thumb = self.original_image.resize((150, 150), Image.Resampling.LANCZOS)
            original_photo = ImageTk.PhotoImage(original_thumb)
            self.original_preview.configure(image=original_photo, text="")
            self.original_preview.image = original_photo

        if self.current_image:
            # معاينة الصورة المعدلة
            edited_thumb = self.current_image.resize((150, 150), Image.Resampling.LANCZOS)
            edited_photo = ImageTk.PhotoImage(edited_thumb)
            self.edited_preview.configure(image=edited_photo, text="")
            self.edited_preview.image = edited_photo

    # ==================== وظائف أساسية مؤقتة ====================

    def start_brush_stroke(self, x, y):
        """بدء ضربة فرشاة"""
        pass

    def continue_stroke(self, x, y):
        """متابعة الضربة"""
        pass

    def end_stroke(self):
        """إنهاء الضربة"""
        pass

    def start_pencil_stroke(self, x, y):
        """بدء ضربة قلم"""
        pass

    def start_eraser_stroke(self, x, y):
        """بدء المسح"""
        pass

    def bucket_fill(self, x, y):
        """تعبئة بالدلو"""
        pass

    def pick_color(self, x, y):
        """اختيار لون"""
        pass

    def start_shape_draw(self, x, y):
        """بدء رسم شكل"""
        pass

    def continue_shape_draw(self, x, y):
        """متابعة رسم الشكل"""
        pass

    def end_shape_draw(self):
        """إنهاء رسم الشكل"""
        pass

    def open_project(self):
        """فتح مشروع"""
        self.log_message("📂 فتح مشروع - قريباً")

    def save_project(self):
        """حفظ مشروع"""
        self.log_message("💾 حفظ مشروع - قريباً")

    def export_icon(self):
        """تصدير أيقونة"""
        if not self.layers:
            self.log_message("❌ لا توجد طبقات للتصدير")
            return

        file_path = filedialog.asksaveasfilename(
            title="تصدير أيقونة",
            defaultextension=".ico",
            filetypes=[
                ("ICO files", "*.ico"),
                ("PNG files", "*.png"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                # دمج جميع الطبقات
                final_image = Image.new('RGBA', (self.canvas_size, self.canvas_size), (0, 0, 0, 0))

                for layer in self.layers:
                    if layer.visible:
                        layer_image = layer.image.copy()
                        if layer.opacity < 1.0:
                            alpha = layer_image.split()[-1]
                            alpha = alpha.point(lambda p: int(p * layer.opacity))
                            layer_image.putalpha(alpha)

                        final_image = Image.alpha_composite(final_image, layer_image)

                if file_path.lower().endswith('.ico'):
                    # حفظ كأيقونة ICO متعددة الأحجام
                    sizes = [(16,16), (32,32), (48,48), (64,64), (128,128), (256,256)]
                    images = []
                    for size in sizes:
                        resized = final_image.resize(size, Image.Resampling.LANCZOS)
                        images.append(resized)
                    images[0].save(file_path, format='ICO', sizes=[(img.width, img.height) for img in images])
                else:
                    final_image.save(file_path)

                self.log_message(f"📤 تم تصدير الأيقونة: {file_path}")

            except Exception as e:
                self.log_message(f"❌ خطأ في التصدير: {e}")

    def export_image(self):
        """تصدير صورة"""
        self.export_icon()  # نفس الوظيفة مؤقتاً

    def enhance_image(self):
        """تحسين الصورة"""
        self.log_message("✨ تحسين الصورة - قريباً")

    def remove_background(self):
        """إزالة الخلفية"""
        self.log_message("🎯 إزالة الخلفية - قريباً")

    def apply_filter(self, filter_name):
        """تطبيق فلتر"""
        self.log_message(f"🎨 تطبيق فلتر {filter_name} - قريباً")

    def add_new_layer(self):
        """إضافة طبقة جديدة"""
        layer_id = f"layer_{len(self.layers)}"
        layer = Layer(
            id=layer_id,
            name=f"طبقة {len(self.layers) + 1}",
            image=Image.new('RGBA', (self.canvas_size, self.canvas_size), (0, 0, 0, 0)),
            visible=True,
            locked=False,
            opacity=1.0
        )

        self.layers.append(layer)
        self.current_layer = layer
        self.update_layers_list()
        self.update_layer_info()
        self.save_state("إضافة طبقة جديدة")
        self.log_message("➕ تم إضافة طبقة جديدة")

    def duplicate_layer(self):
        """تكرار طبقة"""
        if self.current_layer:
            layer_id = f"layer_{len(self.layers)}"
            layer = Layer(
                id=layer_id,
                name=f"{self.current_layer.name} - نسخة",
                image=self.current_layer.image.copy(),
                visible=True,
                locked=False,
                opacity=self.current_layer.opacity
            )

            self.layers.append(layer)
            self.current_layer = layer
            self.update_layers_list()
            self.update_layer_info()
            self.save_state("تكرار طبقة")
            self.log_message("📋 تم تكرار الطبقة")

    def delete_layer(self):
        """حذف طبقة"""
        if self.current_layer and len(self.layers) > 1:
            self.layers.remove(self.current_layer)
            self.current_layer = self.layers[-1] if self.layers else None
            self.update_layers_list()
            self.update_layer_info()
            self.update_canvas_display()
            self.save_state("حذف طبقة")
            self.log_message("🗑️ تم حذف الطبقة")

    def merge_down(self):
        """دمج لأسفل"""
        self.log_message("🔗 دمج الطبقات - قريباً")

    def flatten_image(self):
        """تسطيح الصورة"""
        self.log_message("📄 تسطيح الصورة - قريباً")

    def toggle_layer_visibility(self):
        """تبديل رؤية الطبقة"""
        if self.current_layer:
            self.current_layer.visible = not self.current_layer.visible
            self.update_layers_list()
            self.update_canvas_display()
            self.log_message(f"👁️ تم {'إظهار' if self.current_layer.visible else 'إخفاء'} الطبقة")

    def toggle_layer_lock(self):
        """تبديل قفل الطبقة"""
        if self.current_layer:
            self.current_layer.locked = not self.current_layer.locked
            self.update_layers_list()
            self.log_message(f"🔒 تم {'قفل' if self.current_layer.locked else 'إلغاء قفل'} الطبقة")

    def undo(self):
        """تراجع"""
        if self.history_index > 0:
            self.history_index -= 1
            self.restore_state(self.history[self.history_index])
            self.update_history_list()
            self.log_message("↶ تراجع")

    def redo(self):
        """إعادة"""
        if self.history_index < len(self.history) - 1:
            self.history_index += 1
            self.restore_state(self.history[self.history_index])
            self.update_history_list()
            self.log_message("↷ إعادة")

    def restore_state(self, state):
        """استعادة حالة"""
        # مسح الطبقات الحالية
        self.layers = []

        # استعادة الطبقات
        for layer_data in state['layers']:
            layer = Layer(
                id=layer_data['id'],
                name=layer_data['name'],
                image=layer_data['image'].copy(),
                visible=layer_data['visible'],
                locked=layer_data['locked'],
                opacity=layer_data['opacity'],
                x=layer_data['x'],
                y=layer_data['y']
            )
            self.layers.append(layer)

        # استعادة الطبقة الحالية
        current_id = state['current_layer_id']
        self.current_layer = next((l for l in self.layers if l.id == current_id), None)

        # تحديث الواجهة
        self.update_layers_list()
        self.update_layer_info()
        self.update_canvas_display()

    def copy_layer(self):
        """نسخ طبقة"""
        self.log_message("📋 نسخ طبقة - قريباً")

    def paste_layer(self):
        """لصق طبقة"""
        self.log_message("📄 لصق طبقة - قريباً")

    def clear_canvas(self):
        """مسح اللوحة"""
        if self.current_layer:
            self.current_layer.image = Image.new('RGBA', (self.canvas_size, self.canvas_size), (0, 0, 0, 0))
            self.update_canvas_display()
            self.save_state("مسح اللوحة")
            self.log_message("🔄 تم مسح اللوحة")

    def clear_history(self):
        """مسح التاريخ"""
        self.history = []
        self.history_index = -1
        self.update_history_list()
        self.log_message("🗑️ تم مسح التاريخ")

    def rotate_90(self):
        """تدوير 90 درجة"""
        if self.current_layer:
            self.current_layer.image = self.current_layer.image.rotate(90, expand=True)
            self.update_canvas_display()
            self.save_state("تدوير 90°")
            self.log_message("🔄 تم تدوير الطبقة 90°")

    def flip_horizontal(self):
        """انعكاس أفقي"""
        if self.current_layer:
            self.current_layer.image = self.current_layer.image.transpose(Image.Transpose.FLIP_LEFT_RIGHT)
            self.update_canvas_display()
            self.save_state("انعكاس أفقي")
            self.log_message("↔️ تم الانعكاس الأفقي")

    def flip_vertical(self):
        """انعكاس عمودي"""
        if self.current_layer:
            self.current_layer.image = self.current_layer.image.transpose(Image.Transpose.FLIP_TOP_BOTTOM)
            self.update_canvas_display()
            self.save_state("انعكاس عمودي")
            self.log_message("↕️ تم الانعكاس العمودي")

    def resize_image(self):
        """تغيير حجم الصورة"""
        self.log_message("📐 تغيير الحجم - قريباً")

    def on_layer_select(self, event):
        """تحديد طبقة"""
        selection = self.layers_listbox.curselection()
        if selection:
            index = len(self.layers) - 1 - selection[0]
            if 0 <= index < len(self.layers):
                self.current_layer = self.layers[index]
                self.update_layer_info()

    def on_history_select(self, event):
        """تحديد من التاريخ"""
        selection = self.history_listbox.curselection()
        if selection:
            index = selection[0]
            if 0 <= index < len(self.history):
                self.history_index = index
                self.restore_state(self.history[index])
                self.update_history_list()

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        app = UltimateIconEditor()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
