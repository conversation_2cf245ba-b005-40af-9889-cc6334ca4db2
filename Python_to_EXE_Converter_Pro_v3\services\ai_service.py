#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python to EXE Converter Pro v3.0 - AI Service
خدمة الذكاء الاصطناعي

خدمة متقدمة للذكاء الاصطناعي تدعم:
- تحليل الكود الذكي
- اقتراحات التحسين
- توليد الأيقونات
- المساعدة التفاعلية
"""

import os
import ast
import re
import json
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
import threading
import time

from . import Service
from ..core.logger import Logger
from ..core.exceptions import AIServiceError

class AnalysisType(Enum):
    """أنواع التحليل"""
    CODE_STRUCTURE = "code_structure"
    DEPENDENCIES = "dependencies"
    PERFORMANCE = "performance"
    SECURITY = "security"
    OPTIMIZATION = "optimization"

@dataclass
class CodeAnalysis:
    """نتيجة تحليل الكود"""
    file_path: str
    analysis_type: AnalysisType
    results: Dict[str, Any]
    suggestions: List[str]
    confidence: float
    timestamp: str

class AIService(Service):
    """خدمة الذكاء الاصطناعي"""
    
    def __init__(self):
        super().__init__("AIService")
        
        # قواعد التحليل الذكي
        self.analysis_rules = {
            'performance': {
                'heavy_loops': r'for\s+\w+\s+in\s+range\s*\(\s*\d{4,}',
                'inefficient_string': r'(\w+\s*\+=\s*["\'].*["\']|\w+\s*=\s*\w+\s*\+\s*["\'])',
                'global_variables': r'global\s+\w+',
                'recursive_functions': r'def\s+(\w+).*:\s*.*\1\s*\(',
            },
            'security': {
                'eval_usage': r'eval\s*\(',
                'exec_usage': r'exec\s*\(',
                'input_without_validation': r'input\s*\([^)]*\)\s*(?!.*(?:int|float|str)\s*\()',
                'file_operations': r'open\s*\([^)]*["\']w["\']',
                'subprocess_shell': r'subprocess\.\w+\([^)]*shell\s*=\s*True',
            },
            'structure': {
                'long_functions': r'def\s+\w+.*?(?=\ndef|\nclass|\Z)',
                'deep_nesting': r'(\s{4,}){4,}',
                'duplicate_code': r'(.{20,})\s*\n.*\1',
                'magic_numbers': r'\b(?<![\w.])\d{2,}\b(?![\w.])',
            }
        }
        
        # قاعدة بيانات الاقتراحات
        self.suggestions_db = {
            'tkinter_detected': "💡 تم اكتشاف Tkinter - يُنصح بتفعيل --noconsole للحصول على واجهة نظيفة",
            'pygame_detected': "🎮 تم اكتشاف Pygame - قد تحتاج لإضافة ملفات الصوت والصور كـ --add-data",
            'requests_detected': "🌐 تم اكتشاف Requests - تأكد من الاتصال بالإنترنت عند تشغيل التطبيق",
            'numpy_detected': "📊 تم اكتشاف NumPy - قد يكون الملف النهائي كبيراً، فكر في --onedir",
            'pandas_detected': "📈 تم اكتشاف Pandas - مكتبة ثقيلة، استخدم --exclude-module للمكتبات غير المستخدمة",
            'matplotlib_detected': "📊 تم اكتشاف Matplotlib - أضف --hidden-import matplotlib.backends.backend_tkagg",
            'opencv_detected': "📷 تم اكتشاف OpenCV - مكتبة كبيرة، تأكد من إضافة ملفات DLL المطلوبة",
            'tensorflow_detected': "🤖 تم اكتشاف TensorFlow - مكتبة ضخمة، استخدم --onedir وتأكد من المساحة الكافية",
            'torch_detected': "🔥 تم اكتشاف PyTorch - مكتبة كبيرة، قد تحتاج لإعدادات خاصة للـ CUDA",
            'flask_detected': "🌐 تم اكتشاف Flask - أضف مجلد templates و static كـ --add-data",
            'django_detected': "🎯 تم اكتشاف Django - معقد للتحويل، فكر في استخدام Docker بدلاً من ذلك",
            'file_usage': "⚠️ استخدام __file__ قد يسبب مشاكل - استخدم sys._MEIPASS للمسارات",
            'input_detected': "⌨️ تم اكتشاف input() - تأكد من عدم تفعيل --noconsole",
            'print_detected': "🖨️ تم اكتشاف print() - قد لا تظهر في --noconsole، استخدم logging",
            'multiprocessing_detected': "⚡ تم اكتشاف multiprocessing - أضف if __name__ == '__main__':",
            'threading_detected': "🧵 تم اكتشاف threading - تأكد من إنهاء الخيوط بشكل صحيح",
            'database_detected': "🗄️ تم اكتشاف قاعدة بيانات - أضف ملف قاعدة البيانات كـ --add-data",
            'config_file_detected': "⚙️ تم اكتشاف ملفات إعداد - أضفها كـ --add-data",
            'large_file_detected': "📦 الملف كبير - فكر في تقسيمه أو استخدام --onedir",
            'many_imports_detected': "📚 عدد كبير من المكتبات - راجع الضرورية منها فقط",
        }
        
        # ذاكرة التخزين المؤقت
        self.analysis_cache = {}
        self.cache_lock = threading.Lock()
    
    def initialize(self) -> bool:
        """تهيئة الخدمة"""
        try:
            self.logger.info("تم تهيئة خدمة الذكاء الاصطناعي")
            return True
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة خدمة AI: {e}")
            return False
    
    def start(self) -> bool:
        """بدء الخدمة"""
        self.logger.info("تم بدء خدمة الذكاء الاصطناعي")
        return True
    
    def stop(self) -> bool:
        """إيقاف الخدمة"""
        self.logger.info("تم إيقاف خدمة الذكاء الاصطناعي")
        return True
    
    def cleanup(self) -> bool:
        """تنظيف الخدمة"""
        with self.cache_lock:
            self.analysis_cache.clear()
        return True
    
    def analyze_code(self, file_path: str, analysis_types: List[AnalysisType] = None) -> List[CodeAnalysis]:
        """تحليل الكود الذكي"""
        if not os.path.exists(file_path):
            raise AIServiceError(f"الملف غير موجود: {file_path}")
        
        if analysis_types is None:
            analysis_types = list(AnalysisType)
        
        # فحص الذاكرة المؤقتة
        cache_key = self._get_cache_key(file_path, analysis_types)
        with self.cache_lock:
            if cache_key in self.analysis_cache:
                return self.analysis_cache[cache_key]
        
        results = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # تحليل AST
            try:
                tree = ast.parse(content)
            except SyntaxError as e:
                raise AIServiceError(f"خطأ في بناء الجملة: {e}")
            
            # تنفيذ أنواع التحليل المختلفة
            for analysis_type in analysis_types:
                analysis = self._perform_analysis(file_path, content, tree, analysis_type)
                if analysis:
                    results.append(analysis)
            
            # حفظ في الذاكرة المؤقتة
            with self.cache_lock:
                self.analysis_cache[cache_key] = results
            
            return results
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الكود {file_path}: {e}")
            raise AIServiceError(f"فشل في تحليل الكود: {e}")
    
    def _get_cache_key(self, file_path: str, analysis_types: List[AnalysisType]) -> str:
        """إنشاء مفتاح للذاكرة المؤقتة"""
        file_stat = os.stat(file_path)
        content_hash = f"{file_path}:{file_stat.st_mtime}:{file_stat.st_size}"
        types_str = ",".join(sorted([t.value for t in analysis_types]))
        return hashlib.md5(f"{content_hash}:{types_str}".encode()).hexdigest()
    
    def _perform_analysis(self, file_path: str, content: str, tree: ast.AST, analysis_type: AnalysisType) -> Optional[CodeAnalysis]:
        """تنفيذ نوع تحليل معين"""
        if analysis_type == AnalysisType.CODE_STRUCTURE:
            return self._analyze_structure(file_path, content, tree)
        elif analysis_type == AnalysisType.DEPENDENCIES:
            return self._analyze_dependencies(file_path, content, tree)
        elif analysis_type == AnalysisType.PERFORMANCE:
            return self._analyze_performance(file_path, content, tree)
        elif analysis_type == AnalysisType.SECURITY:
            return self._analyze_security(file_path, content, tree)
        elif analysis_type == AnalysisType.OPTIMIZATION:
            return self._analyze_optimization(file_path, content, tree)
        
        return None
    
    def _analyze_structure(self, file_path: str, content: str, tree: ast.AST) -> CodeAnalysis:
        """تحليل بنية الكود"""
        results = {
            'functions_count': 0,
            'classes_count': 0,
            'lines_count': len(content.split('\n')),
            'complexity_score': 0,
            'long_functions': [],
            'deep_nesting_lines': []
        }
        
        suggestions = []
        
        # عد الدوال والفئات
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                results['functions_count'] += 1
                # فحص الدوال الطويلة
                if hasattr(node, 'end_lineno') and hasattr(node, 'lineno'):
                    func_length = node.end_lineno - node.lineno
                    if func_length > 50:
                        results['long_functions'].append({
                            'name': node.name,
                            'lines': func_length,
                            'start_line': node.lineno
                        })
            elif isinstance(node, ast.ClassDef):
                results['classes_count'] += 1
        
        # فحص التعقيد
        if results['functions_count'] > 20:
            suggestions.append("🔧 عدد كبير من الدوال - فكر في تقسيم الكود لملفات متعددة")
        
        if results['long_functions']:
            suggestions.append(f"📏 {len(results['long_functions'])} دالة طويلة - فكر في تقسيمها")
        
        if results['lines_count'] > 1000:
            suggestions.append("📄 ملف كبير - فكر في تقسيمه لوحدات أصغر")
        
        # حساب نقاط التعقيد
        complexity = (results['functions_count'] * 2) + (results['classes_count'] * 3)
        results['complexity_score'] = min(complexity / 100, 1.0)
        
        return CodeAnalysis(
            file_path=file_path,
            analysis_type=AnalysisType.CODE_STRUCTURE,
            results=results,
            suggestions=suggestions,
            confidence=0.9,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )
    
    def _analyze_dependencies(self, file_path: str, content: str, tree: ast.AST) -> CodeAnalysis:
        """تحليل التبعيات"""
        results = {
            'imports': [],
            'standard_libs': [],
            'third_party_libs': [],
            'local_imports': []
        }
        
        suggestions = []
        
        # استخراج الاستيرادات
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    module_name = alias.name.split('.')[0]
                    results['imports'].append(module_name)
                    
                    # تصنيف المكتبة
                    if self._is_standard_library(module_name):
                        results['standard_libs'].append(module_name)
                    elif self._is_third_party_library(module_name):
                        results['third_party_libs'].append(module_name)
                    else:
                        results['local_imports'].append(module_name)
                    
                    # اقتراحات خاصة بالمكتبات
                    suggestion_key = f"{module_name}_detected"
                    if suggestion_key in self.suggestions_db:
                        suggestions.append(self.suggestions_db[suggestion_key])
            
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    module_name = node.module.split('.')[0]
                    results['imports'].append(module_name)
                    
                    suggestion_key = f"{module_name}_detected"
                    if suggestion_key in self.suggestions_db:
                        suggestions.append(self.suggestions_db[suggestion_key])
        
        # إزالة التكرار
        results['imports'] = list(set(results['imports']))
        results['standard_libs'] = list(set(results['standard_libs']))
        results['third_party_libs'] = list(set(results['third_party_libs']))
        results['local_imports'] = list(set(results['local_imports']))
        
        # اقتراحات عامة
        if len(results['third_party_libs']) > 10:
            suggestions.append(self.suggestions_db['many_imports_detected'])
        
        return CodeAnalysis(
            file_path=file_path,
            analysis_type=AnalysisType.DEPENDENCIES,
            results=results,
            suggestions=list(set(suggestions)),  # إزالة التكرار
            confidence=0.95,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )
    
    def _analyze_performance(self, file_path: str, content: str, tree: ast.AST) -> CodeAnalysis:
        """تحليل الأداء"""
        results = {
            'performance_issues': [],
            'optimization_opportunities': []
        }
        
        suggestions = []
        
        # فحص الحلقات الثقيلة
        heavy_loops = re.findall(self.analysis_rules['performance']['heavy_loops'], content)
        if heavy_loops:
            results['performance_issues'].append('heavy_loops')
            suggestions.append("⚡ حلقات ثقيلة مكتشفة - فكر في استخدام NumPy أو تحسين الخوارزمية")
        
        # فحص عمليات النصوص غير الفعالة
        inefficient_strings = re.findall(self.analysis_rules['performance']['inefficient_string'], content)
        if inefficient_strings:
            results['performance_issues'].append('inefficient_string_operations')
            suggestions.append("📝 عمليات نصوص غير فعالة - استخدم join() أو f-strings")
        
        # فحص المتغيرات العامة
        global_vars = re.findall(self.analysis_rules['performance']['global_variables'], content)
        if global_vars:
            results['performance_issues'].append('global_variables')
            suggestions.append("🌐 متغيرات عامة مكتشفة - قلل استخدامها لتحسين الأداء")
        
        return CodeAnalysis(
            file_path=file_path,
            analysis_type=AnalysisType.PERFORMANCE,
            results=results,
            suggestions=suggestions,
            confidence=0.8,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )
    
    def _analyze_security(self, file_path: str, content: str, tree: ast.AST) -> CodeAnalysis:
        """تحليل الأمان"""
        results = {
            'security_issues': [],
            'risk_level': 'low'
        }
        
        suggestions = []
        risk_score = 0
        
        # فحص استخدام eval
        if re.search(self.analysis_rules['security']['eval_usage'], content):
            results['security_issues'].append('eval_usage')
            suggestions.append("🚨 استخدام eval() خطير - تجنبه أو استخدم ast.literal_eval()")
            risk_score += 3
        
        # فحص استخدام exec
        if re.search(self.analysis_rules['security']['exec_usage'], content):
            results['security_issues'].append('exec_usage')
            suggestions.append("⚠️ استخدام exec() خطير - تجنبه أو تأكد من التحقق من المدخلات")
            risk_score += 3
        
        # فحص input بدون تحقق
        if re.search(self.analysis_rules['security']['input_without_validation'], content):
            results['security_issues'].append('unvalidated_input')
            suggestions.append("🔒 input() بدون تحقق - تأكد من التحقق من صحة المدخلات")
            risk_score += 2
        
        # تحديد مستوى المخاطر
        if risk_score >= 5:
            results['risk_level'] = 'high'
        elif risk_score >= 2:
            results['risk_level'] = 'medium'
        
        return CodeAnalysis(
            file_path=file_path,
            analysis_type=AnalysisType.SECURITY,
            results=results,
            suggestions=suggestions,
            confidence=0.85,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )
    
    def _analyze_optimization(self, file_path: str, content: str, tree: ast.AST) -> CodeAnalysis:
        """تحليل التحسين"""
        results = {
            'optimization_suggestions': [],
            'estimated_improvement': 0
        }
        
        suggestions = []
        
        # فحص استخدام __file__
        if '__file__' in content:
            suggestions.append(self.suggestions_db['file_usage'])
        
        # فحص استخدام input
        if 'input(' in content:
            suggestions.append(self.suggestions_db['input_detected'])
        
        # فحص استخدام print
        if 'print(' in content:
            suggestions.append(self.suggestions_db['print_detected'])
        
        # فحص multiprocessing
        if 'multiprocessing' in content:
            suggestions.append(self.suggestions_db['multiprocessing_detected'])
        
        return CodeAnalysis(
            file_path=file_path,
            analysis_type=AnalysisType.OPTIMIZATION,
            results=results,
            suggestions=suggestions,
            confidence=0.9,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )
    
    def _is_standard_library(self, module_name: str) -> bool:
        """فحص ما إذا كانت المكتبة من المكتبات الأساسية"""
        standard_libs = {
            'os', 'sys', 'json', 'sqlite3', 'datetime', 'time', 'math', 'random',
            'collections', 'itertools', 'functools', 'operator', 'pathlib',
            'urllib', 'http', 'email', 'html', 'xml', 'csv', 'configparser',
            'logging', 'unittest', 'threading', 'multiprocessing', 'subprocess',
            'socket', 'ssl', 'hashlib', 'hmac', 'secrets', 'uuid', 'base64',
            'binascii', 'struct', 'codecs', 'locale', 'gettext', 'argparse',
            'shutil', 'glob', 'fnmatch', 'tempfile', 'gzip', 'bz2', 'lzma',
            'zipfile', 'tarfile'
        }
        return module_name in standard_libs
    
    def _is_third_party_library(self, module_name: str) -> bool:
        """فحص ما إذا كانت المكتبة خارجية"""
        third_party_libs = {
            'tkinter', 'PyQt5', 'PyQt6', 'PySide2', 'PySide6', 'kivy',
            'flask', 'django', 'fastapi', 'requests', 'urllib3',
            'pandas', 'numpy', 'matplotlib', 'seaborn', 'plotly',
            'tensorflow', 'torch', 'sklearn', 'opencv', 'PIL',
            'pygame', 'arcade', 'pyglet', 'panda3d'
        }
        return module_name in third_party_libs
    
    def get_smart_suggestions(self, file_path: str) -> List[str]:
        """الحصول على اقتراحات ذكية شاملة"""
        try:
            analyses = self.analyze_code(file_path)
            all_suggestions = []
            
            for analysis in analyses:
                all_suggestions.extend(analysis.suggestions)
            
            # إزالة التكرار والترتيب حسب الأهمية
            unique_suggestions = list(set(all_suggestions))
            
            # ترتيب حسب الأولوية (الأمان أولاً، ثم الأداء، ثم التحسين)
            priority_order = ['🚨', '⚠️', '🔒', '⚡', '💡', '📦', '🔧']
            
            def get_priority(suggestion):
                for i, emoji in enumerate(priority_order):
                    if suggestion.startswith(emoji):
                        return i
                return len(priority_order)
            
            unique_suggestions.sort(key=get_priority)
            
            return unique_suggestions
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الاقتراحات: {e}")
            return [f"❌ خطأ في التحليل: {e}"]
