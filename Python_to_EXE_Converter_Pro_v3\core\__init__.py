#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python to EXE Converter Pro v3.0 - Core Module
النواة الأساسية للتطبيق

هذا الملف يحتوي على النواة الأساسية للتطبيق والتي تشمل:
- محرك التحويل الرئيسي
- إدارة الإعدادات
- نظام السجلات
- معالجة الاستثناءات
"""

__version__ = "3.0.0"
__author__ = "Python to EXE Converter Pro Team"
__description__ = "Advanced Python to EXE converter with AI-powered features"

# استيراد المكونات الأساسية
from .converter import PyToExeConverter
from .config_manager import ConfigManager
from .logger import Logger
from .exceptions import (
    ConverterError,
    ConfigError,
    DependencyError,
    ValidationError
)

# تصدير المكونات الرئيسية
__all__ = [
    'PyToExeConverter',
    'ConfigManager', 
    'Logger',
    'ConverterError',
    'ConfigError',
    'DependencyError',
    'ValidationError'
]

# إعداد السجل الافتراضي
logger = Logger()

def get_version():
    """إرجاع إصدار التطبيق"""
    return __version__

def get_info():
    """إرجاع معلومات التطبيق"""
    return {
        'name': 'Python to EXE Converter Pro',
        'version': __version__,
        'author': __author__,
        'description': __description__
    }
