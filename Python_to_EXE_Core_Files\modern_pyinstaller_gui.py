#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python to EXE Converter Pro v2.0 - Modern Glass UI
واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة وألوان زاهية
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
import subprocess
import threading
import json
import time
import math
import random
from datetime import datetime
from smart_dependency_manager import SmartDependencyManager, LibraryInfo
from ai_icon_generator import AIIconGenerator, SmartCodeAnalyzer, IconSuggestion
from icon_editor import IconEditor
from ai_image_studio import AIImageStudio # <<< استبدال المحول القديم بالاستوديو الذكي
from professional_icon_editor import open_professional_icon_editor
from ultimate_icon_editor import ModernIconGenerator, SmartCodeAnalyzer

class ModernPyInstallerGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_modern_window()
        self.setup_variables()
        self.load_settings()
        self.create_modern_interface()
        
    def setup_modern_window(self):
        """إعداد النافذة العصرية"""
        self.root.title("🚀 Python to EXE Converter Pro v2.0 - Modern Glass UI")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        
        # ألوان عصرية متوافقة مع Tkinter
        self.colors = {
            'bg_primary': '#0a0a0f',      # أسود عميق
            'bg_secondary': '#1a1a2e',    # أزرق غامق
            'bg_tertiary': '#16213e',     # أزرق متوسط
            'accent_blue': '#6366f1',     # أزرق نيون
            'accent_pink': '#ec4899',     # وردي نيون
            'accent_cyan': '#06b6d4',     # سماوي نيون
            'success': '#10b981',         # أخضر نيون
            'warning': '#f59e0b',         # برتقالي نيون
            'error': '#ef4444',           # أحمر نيون
            'text_primary': '#ffffff',    # أبيض
            'text_secondary': '#e2e8f0',  # رمادي فاتح
            'text_muted': '#94a3b8',      # رمادي متوسط
            'glass': '#2a2a3e',           # زجاج محاكاة
            'glass_hover': '#3a3a4e',     # زجاج عند التمرير
            'border': '#4a4a5e',          # حدود
            'accent_blue_light': '#8b8cf8',   # أزرق فاتح
            'accent_pink_light': '#f472b6',   # وردي فاتح
            'accent_cyan_light': '#22d3ee',   # سماوي فاتح
            'success_light': '#34d399',       # أخضر فاتح
        }
        
        self.root.configure(bg=self.colors['bg_primary'])
        
        # شفافية خفيفة
        try:
            self.root.attributes('-alpha', 0.98)
        except:
            pass
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def setup_variables(self):
        """إعداد المتغيرات"""
        self.source_path = tk.StringVar()
        self.output_path = tk.StringVar()
        self.icon_path = tk.StringVar()
        
        # خيارات التحويل
        self.onefile = tk.BooleanVar(value=True)
        self.noconsole = tk.BooleanVar(value=False)
        self.debug = tk.BooleanVar(value=False)
        self.optimize = tk.BooleanVar(value=False)
        
        # متغيرات الحالة
        self.is_converting = False
        self.current_tab = "command"

        # نظام إدارة المكتبات الذكي
        self.dependency_manager = SmartDependencyManager(callback=self.dependency_callback)
        self.libraries_info = {}
        self.is_analyzing = False

        # نظام تصميم الأيقونات الذكي
        self.icon_generator = AIIconGenerator(callback=self.icon_callback)
        self.icon_suggestions = []
        self.current_analysis = None
        self.is_generating_icons = False
        
    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            if os.path.exists('settings.json'):
                with open('settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.output_path.set(settings.get('output_path', ''))
                    self.onefile.set(settings.get('onefile', True))
                    self.noconsole.set(settings.get('noconsole', False))
                    self.debug.set(settings.get('debug', False))
                    self.optimize.set(settings.get('optimize', False))
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            settings = {
                'output_path': self.output_path.get(),
                'onefile': self.onefile.get(),
                'noconsole': self.noconsole.get(),
                'debug': self.debug.get(),
                'optimize': self.optimize.get()
            }
            with open('settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
    
    def create_modern_interface(self):
        """إنشاء الواجهة العصرية"""
        # الإطار الرئيسي مع تدرج
        main_frame = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان العصري
        self.create_modern_header(main_frame)
        
        # المحتوى الرئيسي
        content_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
        content_frame.pack(fill='both', expand=True, pady=20)
        
        # العمود الأيسر - الإعدادات
        self.create_settings_section(content_frame)
        
        # العمود الأيمن - المعاينة والسجل
        self.create_preview_section(content_frame)
        
        # شريط الحالة
        self.create_status_bar(main_frame)
        
        # بدء الانيميشن
        self.start_background_animation()
    
    def open_code_editor(self):
        """فتح محرر الكود المتقدم"""
        try:
            from code_editor import CodeEditor
            CodeEditor(self.root)
        except Exception as e:
            messagebox.showerror("خطأ في محرر الكود", str(e))

    def create_modern_header(self, parent):
        """إنشاء رأس عصري"""
        header_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], relief='flat', bd=0)
        header_frame.pack(fill='x', pady=(0, 20))
        
        # إضافة padding داخلي
        inner_frame = tk.Frame(header_frame, bg=self.colors['bg_secondary'])
        inner_frame.pack(fill='x', padx=30, pady=20)
        
        # العنوان الرئيسي
        title_label = tk.Label(
            inner_frame,
            text="🚀 Python to EXE Converter Pro v2.0",
            font=('Segoe UI', 24, 'bold'),
            fg=self.colors['accent_blue'],
            bg=self.colors['bg_secondary']
        )
        title_label.pack()
        
        # العنوان الفرعي
        subtitle_label = tk.Label(
            inner_frame,
            text="✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة",
            font=('Segoe UI', 12),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_secondary']
        )
        subtitle_label.pack(pady=(5, 0))
        
        # شريط أدوات سريع
        toolbar_frame = tk.Frame(inner_frame, bg=self.colors['bg_secondary'])
        toolbar_frame.pack(pady=(15, 0))
        
        # أزرار سريعة
        self.create_glass_button(toolbar_frame, "🔍 فحص سريع", self.quick_check, self.colors['accent_cyan']).pack(side='left', padx=5)
        self.create_glass_button(toolbar_frame, "📁 فتح سريع", self.quick_open, self.colors['accent_pink']).pack(side='left', padx=5)
        self.create_glass_button(toolbar_frame, "🧠 تحليل ذكي", self.smart_analysis, self.colors['warning']).pack(side='left', padx=5)
        self.create_glass_button(toolbar_frame, "🎨 تصميم أيقونة", self.quick_icon_design, self.colors['accent_pink']).pack(side='left', padx=5)
        self.create_glass_button(toolbar_frame, "❓ مساعدة", self.quick_help, self.colors['success']).pack(side='left', padx=5)
        # زر محرر الكود الاحترافي
        self.create_glass_button(toolbar_frame, "📝 تحرير الكود", self.open_code_editor, self.colors['accent_blue']).pack(side='left', padx=5)
    
    def create_glass_button(self, parent, text, command, color):
        """إنشاء زر زجاجي عصري"""
        button = tk.Button(
            parent,
            text=text,
            command=command,
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=color,
            activebackground=color,
            activeforeground=self.colors['text_primary'],
            relief='flat',
            bd=0,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        
        # تأثيرات التفاعل
        def on_enter(e):
            button.configure(bg=self.lighten_color(color))
        
        def on_leave(e):
            button.configure(bg=color)
        
        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)
        
        return button
    
    def lighten_color(self, color):
        """تفتيح اللون"""
        # محاكاة بسيطة لتفتيح اللون
        light_colors = {
            self.colors['accent_blue']: self.colors['accent_blue_light'],
            self.colors['accent_pink']: self.colors['accent_pink_light'],
            self.colors['accent_cyan']: self.colors['accent_cyan_light'],
            self.colors['success']: self.colors['success_light'],
        }
        return light_colors.get(color, color)
    
    def create_settings_section(self, parent):
        """إنشاء قسم الإعدادات"""
        settings_frame = tk.Frame(parent, bg=self.colors['bg_tertiary'], relief='flat', bd=0)
        settings_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        # عنوان القسم
        title_label = tk.Label(
            settings_frame,
            text="⚙️ إعدادات التحويل",
            font=('Segoe UI', 16, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_tertiary']
        )
        title_label.pack(pady=20)
        
        # إطار المحتوى
        content_frame = tk.Frame(settings_frame, bg=self.colors['bg_tertiary'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # اختيار الملف المصدر
        self.create_source_selection(content_frame)
        
        # اختيار مجلد الحفظ
        self.create_output_selection(content_frame)
        
        # الخيارات المتقدمة
        self.create_advanced_options(content_frame)
        
        # أزرار العمليات
        self.create_action_buttons(content_frame)
    
    def create_source_selection(self, parent):
        """إنشاء قسم اختيار الملف المصدر"""
        frame = tk.Frame(parent, bg=self.colors['bg_tertiary'])
        frame.pack(fill='x', pady=(0, 20))
        
        label = tk.Label(
            frame,
            text="📁 الملف المصدر:",
            font=('Segoe UI', 12, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_tertiary']
        )
        label.pack(anchor='w', pady=(0, 10))
        
        # إطار الإدخال
        input_frame = tk.Frame(frame, bg=self.colors['bg_tertiary'])
        input_frame.pack(fill='x', pady=(0, 10))
        
        self.source_entry = tk.Entry(
            input_frame,
            textvariable=self.source_path,
            font=('Segoe UI', 11),
            bg=self.colors['glass'],
            fg=self.colors['text_primary'],
            relief='flat',
            bd=0,
            insertbackground=self.colors['accent_blue']
        )
        self.source_entry.pack(side='left', fill='x', expand=True, padx=(0, 10), ipady=8)
        
        # أزرار الاختيار
        btn_frame = tk.Frame(input_frame, bg=self.colors['bg_tertiary'])
        btn_frame.pack(side='right')
        
        self.create_glass_button(btn_frame, "📄 ملف", self.browse_file, self.colors['accent_blue']).pack(side='left', padx=(0, 5))
        self.create_glass_button(btn_frame, "📁 مجلد", self.browse_folder, self.colors['accent_pink']).pack(side='left')
    
    def create_output_selection(self, parent):
        """إنشاء قسم اختيار مجلد الحفظ"""
        frame = tk.Frame(parent, bg=self.colors['bg_tertiary'])
        frame.pack(fill='x', pady=(0, 20))
        
        label = tk.Label(
            frame,
            text="💾 مجلد الحفظ:",
            font=('Segoe UI', 12, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_tertiary']
        )
        label.pack(anchor='w', pady=(0, 10))
        
        # إطار الإدخال
        input_frame = tk.Frame(frame, bg=self.colors['bg_tertiary'])
        input_frame.pack(fill='x')
        
        self.output_entry = tk.Entry(
            input_frame,
            textvariable=self.output_path,
            font=('Segoe UI', 11),
            bg=self.colors['glass'],
            fg=self.colors['text_primary'],
            relief='flat',
            bd=0,
            insertbackground=self.colors['accent_cyan']
        )
        self.output_entry.pack(side='left', fill='x', expand=True, padx=(0, 10), ipady=8)
        
        self.create_glass_button(input_frame, "📁 تصفح", self.browse_output, self.colors['accent_cyan']).pack(side='right')
    
    def create_advanced_options(self, parent):
        """إنشاء الخيارات المتقدمة"""
        frame = tk.Frame(parent, bg=self.colors['bg_tertiary'])
        frame.pack(fill='x', pady=(0, 20))
        
        label = tk.Label(
            frame,
            text="🔧 الخيارات المتقدمة:",
            font=('Segoe UI', 12, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_tertiary']
        )
        label.pack(anchor='w', pady=(0, 15))
        
        # الخيارات
        options_frame = tk.Frame(frame, bg=self.colors['bg_tertiary'])
        options_frame.pack(fill='x')
        
        # الصف الأول
        row1 = tk.Frame(options_frame, bg=self.colors['bg_tertiary'])
        row1.pack(fill='x', pady=(0, 10))
        
        self.create_modern_checkbox(row1, "📦 ملف واحد", self.onefile).pack(side='left', padx=(0, 30))
        self.create_modern_checkbox(row1, "🖥️ إخفاء الكونسول", self.noconsole).pack(side='left')
        
        # الصف الثاني
        row2 = tk.Frame(options_frame, bg=self.colors['bg_tertiary'])
        row2.pack(fill='x')
        
        self.create_modern_checkbox(row2, "🐛 وضع التصحيح", self.debug).pack(side='left', padx=(0, 30))
        self.create_modern_checkbox(row2, "⚡ تحسين الحجم", self.optimize).pack(side='left')
    
    def create_modern_checkbox(self, parent, text, variable):
        """إنشاء checkbox عصري"""
        cb = tk.Checkbutton(
            parent,
            text=text,
            variable=variable,
            font=('Segoe UI', 10),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_tertiary'],
            activebackground=self.colors['bg_tertiary'],
            activeforeground=self.colors['text_primary'],
            selectcolor=self.colors['accent_blue'],
            relief='flat',
            bd=0
        )
        return cb

    def create_action_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        frame = tk.Frame(parent, bg=self.colors['bg_tertiary'])
        frame.pack(fill='x', pady=(20, 0))

        # الصف الأول - أزرار المساعدة
        help_row = tk.Frame(frame, bg=self.colors['bg_tertiary'])
        help_row.pack(fill='x', pady=(0, 15))

        self.create_glass_button(help_row, "👁️ معاينة الأمر", self.preview_command, self.colors['accent_cyan']).pack(side='left', padx=(0, 10))
        self.create_glass_button(help_row, "🔍 فحص المتطلبات", self.check_requirements, self.colors['warning']).pack(side='left')

        # الصف الثاني - زر التحويل الرئيسي
        main_row = tk.Frame(frame, bg=self.colors['bg_tertiary'])
        main_row.pack(fill='x')

        self.convert_btn = self.create_glass_button(main_row, "🚀 بدء التحويل", self.start_conversion, self.colors['success'])
        self.convert_btn.pack(fill='x', ipady=10)

    def create_preview_section(self, parent):
        """إنشاء قسم المعاينة والسجل"""
        preview_frame = tk.Frame(parent, bg=self.colors['bg_tertiary'], relief='flat', bd=0)
        preview_frame.pack(side='right', fill='both', expand=True)

        # عنوان القسم
        title_label = tk.Label(
            preview_frame,
            text="📊 المعاينة والسجل",
            font=('Segoe UI', 16, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_tertiary']
        )
        title_label.pack(pady=20)

        # إطار المحتوى
        content_frame = tk.Frame(preview_frame, bg=self.colors['bg_tertiary'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # التبويبات
        self.create_tabs(content_frame)

        # شريط التقدم
        self.create_progress_section(content_frame)

    def create_tabs(self, parent):
        """إنشاء التبويبات"""
        tabs_frame = tk.Frame(parent, bg=self.colors['bg_tertiary'])
        tabs_frame.pack(fill='both', expand=True, pady=(0, 20))

        # أزرار التبويبات
        tab_buttons_frame = tk.Frame(tabs_frame, bg=self.colors['bg_tertiary'])
        tab_buttons_frame.pack(fill='x', pady=(0, 15))

        # تبويب معاينة الأمر
        self.command_tab_btn = self.create_glass_button(
            tab_buttons_frame, "💻 معاينة الأمر",
            lambda: self.switch_tab("command"), self.colors['accent_blue']
        )
        self.command_tab_btn.pack(side='left', padx=(0, 10))

        # تبويب السجل
        self.log_tab_btn = self.create_glass_button(
            tab_buttons_frame, "📋 سجل العمليات",
            lambda: self.switch_tab("log"), self.colors['glass']
        )
        self.log_tab_btn.pack(side='left', padx=(0, 10))

        # تبويب الملفات
        self.files_tab_btn = self.create_glass_button(
            tab_buttons_frame, "📁 الملفات المحولة",
            lambda: self.switch_tab("files"), self.colors['glass']
        )
        self.files_tab_btn.pack(side='left', padx=(0, 10))

        # تبويب إدارة المكتبات
        self.libraries_tab_btn = self.create_glass_button(
            tab_buttons_frame, "🧠 إدارة المكتبات",
            lambda: self.switch_tab("libraries"), self.colors['glass']
        )
        self.libraries_tab_btn.pack(side='left', padx=(0, 10))

        # تبويب تصميم الأيقونات
        self.icons_tab_btn = self.create_glass_button(
            tab_buttons_frame, "🎨 تصميم الأيقونات",
            lambda: self.switch_tab("icons"), self.colors['glass']
        )
        self.icons_tab_btn.pack(side='left')

        # محتوى التبويبات
        self.tabs_content = tk.Frame(tabs_frame, bg=self.colors['bg_tertiary'])
        self.tabs_content.pack(fill='both', expand=True)

        # إنشاء محتوى كل تبويب
        self.create_command_tab()
        self.create_log_tab()
        self.create_files_tab()
        self.create_libraries_tab()
        self.create_icons_tab()

        # عرض التبويب الافتراضي
        self.switch_tab("command")

    def create_command_tab(self):
        """إنشاء تبويب معاينة الأمر"""
        self.command_frame = tk.Frame(self.tabs_content, bg=self.colors['bg_tertiary'])

        self.command_text = tk.Text(
            self.command_frame,
            font=('Consolas', 10),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            insertbackground=self.colors['accent_blue'],
            relief='flat',
            bd=0,
            wrap=tk.WORD,
            height=15
        )

        # شريط التمرير
        scrollbar = tk.Scrollbar(self.command_frame, orient=tk.VERTICAL, command=self.command_text.yview)
        self.command_text.configure(yscrollcommand=scrollbar.set)

        self.command_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # تحديث المعاينة
        self.update_command_preview()

    def create_log_tab(self):
        """إنشاء تبويب السجل"""
        self.log_frame = tk.Frame(self.tabs_content, bg=self.colors['bg_tertiary'])

        self.log_text = tk.Text(
            self.log_frame,
            font=('Consolas', 9),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_secondary'],
            insertbackground=self.colors['accent_cyan'],
            relief='flat',
            bd=0,
            wrap=tk.WORD,
            height=15
        )

        # شريط التمرير
        log_scrollbar = tk.Scrollbar(self.log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side='left', fill='both', expand=True)
        log_scrollbar.pack(side='right', fill='y')

        # إضافة رسالة ترحيب
        self.add_to_log("🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI")
        self.add_to_log("✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة")
        self.add_to_log("📝 جاهز لبدء التحويل...")

    def create_files_tab(self):
        """إنشاء تبويب الملفات"""
        self.files_frame = tk.Frame(self.tabs_content, bg=self.colors['bg_tertiary'])

        self.files_text = tk.Text(
            self.files_frame,
            font=('Segoe UI', 10),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            insertbackground=self.colors['accent_pink'],
            relief='flat',
            bd=0,
            wrap=tk.WORD,
            height=15
        )

        # شريط التمرير
        files_scrollbar = tk.Scrollbar(self.files_frame, orient=tk.VERTICAL, command=self.files_text.yview)
        self.files_text.configure(yscrollcommand=files_scrollbar.set)

        self.files_text.pack(side='left', fill='both', expand=True)
        files_scrollbar.pack(side='right', fill='y')

        # إضافة رسالة افتراضية
        self.files_text.insert('1.0', "📂 لم يتم تحويل أي ملفات بعد...\n\n")
        self.files_text.insert('end', "🔄 ستظهر الملفات المحولة هنا بعد التحويل الناجح\n")
        self.files_text.insert('end', "✨ مع معلومات مفصلة عن كل ملف محول")

    def create_libraries_tab(self):
        """إنشاء تبويب إدارة المكتبات الذكي"""
        self.libraries_frame = tk.Frame(self.tabs_content, bg=self.colors['bg_tertiary'])

        # إطار التحكم العلوي
        control_frame = tk.Frame(self.libraries_frame, bg=self.colors['bg_tertiary'])
        control_frame.pack(fill='x', padx=10, pady=10)

        # أزرار التحكم
        self.analyze_btn = self.create_glass_button(
            control_frame, "🧠 تحليل ذكي", self.analyze_dependencies, self.colors['accent_blue']
        )
        self.analyze_btn.pack(side='left', padx=(0, 10))

        self.install_btn = self.create_glass_button(
            control_frame, "📦 تثبيت المفقود", self.install_missing, self.colors['success']
        )
        self.install_btn.pack(side='left', padx=(0, 10))

        self.test_btn = self.create_glass_button(
            control_frame, "🧪 اختبار التشغيل", self.test_execution, self.colors['warning']
        )
        self.test_btn.pack(side='left', padx=(0, 10))

        self.generate_req_btn = self.create_glass_button(
            control_frame, "📝 إنشاء requirements", self.generate_requirements, self.colors['accent_cyan']
        )
        self.generate_req_btn.pack(side='left')

        # منطقة عرض المكتبات
        self.libraries_text = tk.Text(
            self.libraries_frame,
            font=('Consolas', 9),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            insertbackground=self.colors['accent_blue'],
            relief='flat',
            bd=0,
            wrap=tk.WORD,
            height=12
        )

        # شريط التمرير
        libraries_scrollbar = tk.Scrollbar(self.libraries_frame, orient=tk.VERTICAL, command=self.libraries_text.yview)
        self.libraries_text.configure(yscrollcommand=libraries_scrollbar.set)

        self.libraries_text.pack(side='left', fill='both', expand=True, padx=(10, 0), pady=(0, 10))
        libraries_scrollbar.pack(side='right', fill='y', pady=(0, 10), padx=(0, 10))

        # إضافة رسالة افتراضية
        self.libraries_text.insert('1.0', "🧠 نظام إدارة المكتبات الذكي\n\n")
        self.libraries_text.insert('end', "🔍 انقر 'تحليل ذكي' لفحص المشروع واكتشاف المكتبات المطلوبة\n\n")
        self.libraries_text.insert('end', "✨ المميزات:\n")
        self.libraries_text.insert('end', "• كشف تلقائي للمكتبات المطلوبة\n")
        self.libraries_text.insert('end', "• فحص حالة التثبيت والإصدارات\n")
        self.libraries_text.insert('end', "• تثبيت تلقائي للمكتبات المفقودة\n")
        self.libraries_text.insert('end', "• اختبار تشغيل المشروع\n")
        self.libraries_text.insert('end', "• إصلاح الأخطاء تلقائياً\n")
        self.libraries_text.insert('end', "• إنشاء ملف requirements.txt\n")

    def create_icons_tab(self):
        """إنشاء تبويب تصميم الأيقونات الذكي"""
        self.icons_frame = tk.Frame(self.tabs_content, bg=self.colors['bg_tertiary'])

        # إطار التحكم العلوي
        control_frame = tk.Frame(self.icons_frame, bg=self.colors['bg_tertiary'])
        control_frame.pack(fill='x', padx=10, pady=10)

        # أزرار التحكم
        self.analyze_code_btn = self.create_glass_button(
            control_frame, "🧠 تحليل الكود", self.analyze_code_for_icons, self.colors['accent_blue']
        )
        self.analyze_code_btn.pack(side='left', padx=(0, 10))

        self.generate_icons_btn = self.create_glass_button(
            control_frame, "🎨 توليد أيقونات", self.generate_icon_suggestions, self.colors['accent_pink']
        )
        self.generate_icons_btn.pack(side='left', padx=(0, 10))

        self.open_editor_btn = self.create_glass_button(
            control_frame, "✏️ محرر الأيقونات", self.open_icon_editor, self.colors['warning']
        )
        self.open_editor_btn.pack(side='left', padx=(0, 10))

        self.apply_icon_btn = self.create_glass_button(
            control_frame, "✅ تطبيق الأيقونة", self.apply_selected_icon, self.colors['success']
        )
        self.apply_icon_btn.pack(side='left', padx=(0, 10))

        self.image_converter_btn = self.create_glass_button(
            control_frame, "🎨 استوديو الصور الذكي", self.open_ai_image_studio, self.colors['accent_cyan']
        )
        self.image_converter_btn.pack(side='left', padx=(0, 10))

        self.pro_editor_btn = self.create_glass_button(
            control_frame, "🎨 محرر الأيقونات المتطور", self.open_advanced_icon_editor, self.colors['accent_pink']
        )
        self.pro_editor_btn.pack(side='left')

        # إطار المحتوى الرئيسي
        main_content_frame = tk.Frame(self.icons_frame, bg=self.colors['bg_tertiary'])
        main_content_frame.pack(fill='both', expand=True, padx=10, pady=(0, 10))

        # لوحة التحليل (يسار)
        analysis_frame = tk.LabelFrame(
            main_content_frame,
            text="📊 تحليل الكود",
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            bd=1,
            relief='solid'
        )
        analysis_frame.pack(side='left', fill='both', expand=True, padx=(0, 5))

        self.analysis_text = tk.Text(
            analysis_frame,
            font=('Consolas', 9),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            insertbackground=self.colors['accent_blue'],
            relief='flat',
            bd=0,
            wrap=tk.WORD,
            height=15
        )

        analysis_scrollbar = tk.Scrollbar(analysis_frame, orient=tk.VERTICAL, command=self.analysis_text.yview)
        self.analysis_text.configure(yscrollcommand=analysis_scrollbar.set)

        self.analysis_text.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        analysis_scrollbar.pack(side='right', fill='y', pady=5)

        # لوحة الاقتراحات (وسط)
        suggestions_frame = tk.LabelFrame(
            main_content_frame,
            text="💡 اقتراحات الأيقونات",
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            bd=1,
            relief='solid'
        )
        suggestions_frame.pack(side='left', fill='both', expand=True, padx=5)

        # إطار قائمة الاقتراحات
        suggestions_list_frame = tk.Frame(suggestions_frame, bg=self.colors['bg_secondary'])
        suggestions_list_frame.pack(fill='both', expand=True, padx=5, pady=5)

        self.suggestions_listbox = tk.Listbox(
            suggestions_list_frame,
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            selectbackground=self.colors['accent_blue'],
            relief='flat',
            bd=0,
            font=('Segoe UI', 9),
            height=12
        )

        suggestions_scrollbar = tk.Scrollbar(suggestions_list_frame, orient=tk.VERTICAL, command=self.suggestions_listbox.yview)
        self.suggestions_listbox.configure(yscrollcommand=suggestions_scrollbar.set)

        self.suggestions_listbox.pack(side='left', fill='both', expand=True)
        suggestions_scrollbar.pack(side='right', fill='y')

        self.suggestions_listbox.bind('<<ListboxSelect>>', self.on_suggestion_select)
        self.suggestions_listbox.bind('<Double-Button-1>', self.on_suggestion_double_click)

        # لوحة المعاينة (يمين)
        preview_frame = tk.LabelFrame(
            main_content_frame,
            text="👁️ معاينة الأيقونة",
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary'],
            bd=1,
            relief='solid'
        )
        preview_frame.pack(side='right', fill='y', padx=(5, 0))

        # منطقة المعاينة
        self.icon_preview_frame = tk.Frame(preview_frame, bg=self.colors['bg_secondary'], width=200, height=300)
        self.icon_preview_frame.pack(fill='both', expand=True, padx=10, pady=10)
        self.icon_preview_frame.pack_propagate(False)

        # معاينة افتراضية
        self.create_default_icon_preview()

        # إضافة رسالة افتراضية للتحليل
        self.analysis_text.insert('1.0', "🎨 نظام تصميم الأيقونات الذكي\n\n")
        self.analysis_text.insert('end', "🧠 انقر 'تحليل الكود' لفحص التطبيق واكتشاف خصائصه\n\n")
        self.analysis_text.insert('end', "✨ المميزات:\n")
        self.analysis_text.insert('end', "• تحليل ذكي لنوع التطبيق ومجاله\n")
        self.analysis_text.insert('end', "• توليد اقتراحات أيقونات مخصصة\n")
        self.analysis_text.insert('end', "• محرر أيقونات تفاعلي متقدم\n")
        self.analysis_text.insert('end', "• معاينة مباشرة بأحجام مختلفة\n")
        self.analysis_text.insert('end', "• تطبيق مباشر على التطبيق\n")

    def create_default_icon_preview(self):
        """إنشاء معاينة افتراضية للأيقونة"""
        # مسح المحتوى السابق
        for widget in self.icon_preview_frame.winfo_children():
            widget.destroy()

        # عنوان المعاينة
        preview_title = tk.Label(
            self.icon_preview_frame,
            text="🎨 معاينة الأيقونة",
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        preview_title.pack(pady=(0, 10))

        # إطار الأيقونة الرئيسية
        main_icon_frame = tk.Frame(self.icon_preview_frame, bg='white', relief='solid', bd=1)
        main_icon_frame.pack(pady=5)

        # أيقونة افتراضية (مربع ملون)
        default_canvas = tk.Canvas(main_icon_frame, width=64, height=64, bg='white', highlightthickness=0)
        default_canvas.pack(padx=5, pady=5)

        # رسم أيقونة افتراضية
        default_canvas.create_rectangle(16, 16, 48, 48, fill=self.colors['accent_blue'], outline='black', width=2)
        default_canvas.create_text(32, 32, text="?", fill='white', font=('Arial', 16, 'bold'))

        # أحجام مختلفة
        sizes_label = tk.Label(
            self.icon_preview_frame,
            text="📏 أحجام مختلفة:",
            font=('Segoe UI', 9),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        sizes_label.pack(pady=(10, 5))

        sizes_frame = tk.Frame(self.icon_preview_frame, bg=self.colors['bg_secondary'])
        sizes_frame.pack()

        # إنشاء معاينات بأحجام مختلفة
        sizes = [16, 24, 32, 48]
        for size in sizes:
            size_frame = tk.Frame(sizes_frame, bg=self.colors['bg_secondary'])
            size_frame.pack(side='left', padx=2)

            canvas = tk.Canvas(size_frame, width=size, height=size, bg='white', relief='solid', bd=1, highlightthickness=0)
            canvas.pack()

            # رسم أيقونة مصغرة
            margin = max(2, size//8)
            canvas.create_rectangle(margin, margin, size-margin, size-margin, fill=self.colors['accent_blue'], outline='black')

            size_label = tk.Label(
                size_frame,
                text=f"{size}px",
                font=('Segoe UI', 7),
                fg=self.colors['text_primary'],
                bg=self.colors['bg_secondary']
            )
            size_label.pack()

        # معلومات الأيقونة
        info_label = tk.Label(
            self.icon_preview_frame,
            text="ℹ️ اختر اقتراح لمعاينته",
            font=('Segoe UI', 8),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_secondary'],
            wraplength=180
        )
        info_label.pack(pady=(10, 0))

    # ==================== وظائف تصميم الأيقونات الذكية ====================

    def icon_callback(self, message):
        """استقبال رسائل من نظام تصميم الأيقونات"""
        self.add_to_log(message)

        # تحديث تبويب الأيقونات إذا كان مفتوحاً
        if hasattr(self, 'analysis_text') and self.current_tab == "icons":
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.analysis_text.insert(tk.END, f"[{timestamp}] {message}\n")
            self.analysis_text.see(tk.END)

    def analyze_code_for_icons(self):
        """تحليل الكود لتصميم الأيقونات"""
        if not self.source_path.get():
            self.show_notification("يرجى اختيار ملف أو مجلد أولاً", "warning")
            return

        if self.is_generating_icons:
            self.show_notification("تحليل جاري بالفعل...", "warning")
            return

        self.is_generating_icons = True
        self.analyze_code_btn.configure(text="⏳ جاري التحليل...", state='disabled')

        # تشغيل التحليل في thread منفصل
        def run_analysis():
            try:
                self.analysis_text.delete('1.0', tk.END)
                self.analysis_text.insert('1.0', "🧠 بدء تحليل الكود لتصميم الأيقونات...\n\n")

                # تحليل الكود المتقدم
                analyzer = SmartCodeAnalyzer()
                analysis = analyzer.analyze_code(self.source_path.get())

                # توليد اقتراحات أساسية
                suggestions, _ = self.icon_generator.generate_icon_suggestions(self.source_path.get())

                self.icon_suggestions = suggestions
                self.current_analysis = analysis

                # عرض النتائج المحسنة
                self.root.after(0, lambda: self.display_advanced_code_analysis(analysis))

            except Exception as e:
                self.root.after(0, lambda: self.show_notification(f"خطأ في التحليل: {e}", "error"))
            finally:
                self.is_generating_icons = False
                self.root.after(0, lambda: self.analyze_code_btn.configure(text="🧠 تحليل الكود", state='normal'))

        threading.Thread(target=run_analysis, daemon=True).start()

    def display_code_analysis(self):
        """عرض نتائج تحليل الكود"""
        if not self.current_analysis:
            return

        analysis = self.current_analysis

        # التحقق من نوع التحليل
        if hasattr(analysis, 'description'):
            # تحليل متقدم
            self.display_advanced_code_analysis(analysis)
            return

        # تحليل أساسي
        self.analysis_text.delete('1.0', tk.END)

        # عرض نتائج التحليل
        self.analysis_text.insert('end', "📊 نتائج تحليل الكود:\n")
        self.analysis_text.insert('end', "=" * 40 + "\n\n")

        self.analysis_text.insert('end', f"🎯 نوع التطبيق: {getattr(analysis, 'app_type', 'غير محدد')}\n")
        self.analysis_text.insert('end', f"🏢 المجال: {getattr(analysis, 'domain', 'عام')}\n")
        self.analysis_text.insert('end', f"🎨 النمط المقترح: {getattr(analysis, 'style_suggested', 'عصري')}\n\n")

        keywords = getattr(analysis, 'keywords', [])
        if keywords:
            self.analysis_text.insert('end', "🔑 الكلمات المفتاحية:\n")
            for keyword in keywords[:10]:
                self.analysis_text.insert('end', f"  • {keyword}\n")
            self.analysis_text.insert('end', "\n")

        libraries = getattr(analysis, 'libraries', [])
        if libraries:
            self.analysis_text.insert('end', "📚 المكتبات المستخدمة:\n")
            for lib in libraries[:10]:
                self.analysis_text.insert('end', f"  • {lib}\n")
            self.analysis_text.insert('end', "\n")

        colors = getattr(analysis, 'colors_suggested', [])
        if colors:
            self.analysis_text.insert('end', "🎨 الألوان المقترحة:\n")
            for color in colors:
                self.analysis_text.insert('end', f"  • {color}\n")
            self.analysis_text.insert('end', "\n")

        ui_elements = getattr(analysis, 'ui_elements', [])
        if ui_elements:
            self.analysis_text.insert('end', "🖥️ عناصر الواجهة:\n")
            for element in ui_elements:
                self.analysis_text.insert('end', f"  • {element}\n")
            self.analysis_text.insert('end', "\n")

        # عرض الاقتراحات
        self.display_icon_suggestions()

    def display_advanced_code_analysis(self, analysis):
        """عرض نتائج التحليل المتقدم"""
        self.analysis_text.delete('1.0', tk.END)

        analysis_text = f"""🧠 تحليل الكود المتقدم بالذكاء الاصطناعي:
{"=" * 50}

🎯 نوع التطبيق: {analysis.app_type}
📝 الوصف الذكي: {analysis.description}
🏷️ الفئة: {analysis.category}
🎨 النمط المقترح: {analysis.style_suggestion}
🎭 المزاج العام: {analysis.mood}
📊 مستوى الثقة: {int(analysis.confidence * 100)}%
⚙️ مستوى التعقيد: {analysis.complexity}

🔑 الكلمات المفتاحية المستخرجة:
{', '.join(analysis.keywords[:15])}

🔧 الوظائف المكتشفة:
{', '.join(analysis.functions[:10])}

📚 المكتبات المستخدمة:
{', '.join(analysis.libraries[:10])}

🎨 لوحة الألوان المقترحة:
{', '.join(analysis.color_palette)}

✨ جاهز لتوليد لوجوهات مخصصة بالذكاء الاصطناعي!
💡 تم توليد {len(self.icon_suggestions)} اقتراح أساسي

🚀 استخدم "🎨 محرر الأيقونات المتطور" لتصميم أيقونة احترافية!
"""

        self.analysis_text.insert('1.0', analysis_text)

        # عرض الاقتراحات
        self.display_icon_suggestions()

    def generate_icon_suggestions(self):
        """توليد اقتراحات الأيقونات العصرية"""
        if not self.current_analysis:
            self.show_notification("يرجى تشغيل تحليل الكود أولاً", "warning")
            return

        if self.is_generating_icons:
            self.show_notification("توليد جاري بالفعل...", "warning")
            return

        self.is_generating_icons = True
        self.generate_icons_btn.configure(text="⏳ جاري التوليد...", state='disabled')

        # تشغيل التوليد في thread منفصل
        def run_generation():
            try:
                # توليد أيقونات بالذكاء الاصطناعي المتقدم
                ai_images = self.ai_image_generator.generate_images_for_analysis(self.current_analysis, count=6)

                # توليد أيقونات عصرية إضافية
                modern_icons = self.advanced_generator.generate_modern_icons(self.current_analysis, count=4)

                # دمج جميع الاقتراحات
                all_suggestions = []

                # تحويل صور الذكاء الاصطناعي إلى اقتراحات
                for ai_img in ai_images:
                    suggestion = {
                        'name': f"AI: {ai_img.prompt.style}",
                        'description': ai_img.prompt.text,
                        'style': ai_img.prompt.style,
                        'confidence': ai_img.confidence,
                        'colors': ai_img.prompt.colors,
                        'elements': ai_img.prompt.elements,
                        'image': ai_img.image,
                        'modern': True,
                        'source': 'ai_generated'
                    }
                    all_suggestions.append(suggestion)

                # إضافة الأيقونات العصرية
                all_suggestions.extend(modern_icons)

                # دمج مع الاقتراحات الحالية
                self.icon_suggestions.extend(all_suggestions)

                # عرض النتائج
                self.root.after(0, self.display_icon_suggestions)
                self.root.after(0, lambda: self.show_notification(f"تم توليد {len(all_suggestions)} أيقونة ذكية جديدة!", "success"))

            except Exception as e:
                self.root.after(0, lambda: self.show_notification(f"خطأ في التوليد: {e}", "error"))
            finally:
                self.is_generating_icons = False
                self.root.after(0, lambda: self.generate_icons_btn.configure(text="🎨 توليد أيقونات", state='normal'))

        threading.Thread(target=run_generation, daemon=True).start()

    def display_icon_suggestions(self):
        """عرض اقتراحات الأيقونات"""
        self.suggestions_listbox.delete(0, tk.END)

        if not self.icon_suggestions:
            self.suggestions_listbox.insert(tk.END, "لا توجد اقتراحات متاحة")
            return

        for i, suggestion in enumerate(self.icon_suggestions):
            # التعامل مع كلا النوعين من الاقتراحات
            if hasattr(suggestion, 'confidence'):
                confidence = suggestion.confidence
                name = suggestion.name
            elif isinstance(suggestion, dict):
                confidence = suggestion.get('confidence', 0.5)
                name = suggestion.get('name', f'اقتراح {i+1}')
            else:
                confidence = 0.5
                name = f'اقتراح {i+1}'

            confidence_stars = "⭐" * int(confidence * 5)
            display_text = f"{name} {confidence_stars}"
            self.suggestions_listbox.insert(tk.END, display_text)

        # تحديد الاقتراح الأول
        if self.icon_suggestions:
            self.suggestions_listbox.selection_set(0)
            self.preview_icon_suggestion(0)

    def on_suggestion_select(self, event):
        """عند تحديد اقتراح"""
        selection = self.suggestions_listbox.curselection()
        if selection and self.icon_suggestions:
            index = selection[0]
            if index < len(self.icon_suggestions):
                self.preview_icon_suggestion(index)

    def on_suggestion_double_click(self, event):
        """النقر المزدوج على اقتراح"""
        selection = self.suggestions_listbox.curselection()
        if selection and self.icon_suggestions:
            index = selection[0]
            if index < len(self.icon_suggestions):
                self.apply_selected_icon()

    def preview_icon_suggestion(self, index):
        """معاينة اقتراح أيقونة"""
        if index >= len(self.icon_suggestions):
            return

        suggestion = self.icon_suggestions[index]

        # مسح المعاينة السابقة
        for widget in self.icon_preview_frame.winfo_children():
            widget.destroy()

        # عنوان الاقتراح
        title_label = tk.Label(
            self.icon_preview_frame,
            text=suggestion.name if hasattr(suggestion, 'name') else suggestion['name'],
            font=('Segoe UI', 12, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        title_label.pack(pady=(0, 5))

        # وصف الاقتراح
        description = suggestion.description if hasattr(suggestion, 'description') else suggestion.get('description', '')
        desc_label = tk.Label(
            self.icon_preview_frame,
            text=description,
            font=('Segoe UI', 9),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_secondary'],
            wraplength=180
        )
        desc_label.pack(pady=(0, 10))

        # معاينة الأيقونة
        icon_frame = tk.Frame(self.icon_preview_frame, bg='white', relief='solid', bd=1)
        icon_frame.pack(pady=5)

        # التحقق من نوع الاقتراح (عصري أم تقليدي)
        if hasattr(suggestion, 'image') or (isinstance(suggestion, dict) and 'image' in suggestion):
            # أيقونة عصرية مولدة
            self.display_modern_icon_preview(icon_frame, suggestion)
        else:
            # أيقونة تقليدية
            preview_canvas = tk.Canvas(icon_frame, width=64, height=64, bg='white', highlightthickness=0)
            preview_canvas.pack(padx=5, pady=5)
            self.draw_suggestion_preview(preview_canvas, suggestion)

        # معلومات الاقتراح
        info_frame = tk.Frame(self.icon_preview_frame, bg=self.colors['bg_secondary'])
        info_frame.pack(fill='x', pady=10)

        # النمط
        style = suggestion.style if hasattr(suggestion, 'style') else suggestion.get('style', 'unknown')
        style_label = tk.Label(
            info_frame,
            text=f"🎨 النمط: {style}",
            font=('Segoe UI', 8),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        style_label.pack(anchor='w')

        # الثقة
        confidence = suggestion.confidence if hasattr(suggestion, 'confidence') else suggestion.get('confidence', 0)
        confidence_label = tk.Label(
            info_frame,
            text=f"📊 الثقة: {int(confidence * 100)}%",
            font=('Segoe UI', 8),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        confidence_label.pack(anchor='w')

        # الألوان
        colors = suggestion.colors if hasattr(suggestion, 'colors') else suggestion.get('colors', [])
        if colors:
            colors_label = tk.Label(
                info_frame,
                text="🎨 الألوان:",
                font=('Segoe UI', 8),
                fg=self.colors['text_primary'],
                bg=self.colors['bg_secondary']
            )
            colors_label.pack(anchor='w')

            colors_frame = tk.Frame(info_frame, bg=self.colors['bg_secondary'])
            colors_frame.pack(anchor='w', pady=2)

            for color in colors[:4]:
                try:
                    color_box = tk.Label(
                        colors_frame,
                        width=3,
                        height=1,
                        bg=color,
                        relief='solid',
                        bd=1
                    )
                    color_box.pack(side='left', padx=1)
                except:
                    pass  # تجاهل الألوان غير الصحيحة

        # العناصر
        elements = suggestion.elements if hasattr(suggestion, 'elements') else suggestion.get('elements', [])
        if elements:
            elements_label = tk.Label(
                info_frame,
                text=f"🔧 العناصر: {', '.join(elements[:3])}",
                font=('Segoe UI', 8),
                fg=self.colors['text_primary'],
                bg=self.colors['bg_secondary'],
                wraplength=180
            )
            elements_label.pack(anchor='w')

        # إضافة معلومات خاصة بالأيقونات العصرية
        is_modern = hasattr(suggestion, 'modern') or (isinstance(suggestion, dict) and suggestion.get('modern', False))
        if is_modern:
            modern_label = tk.Label(
                info_frame,
                text="✨ أيقونة عصرية متقدمة",
                font=('Segoe UI', 8, 'bold'),
                fg='#10b981',
                bg=self.colors['bg_secondary']
            )
            modern_label.pack(anchor='w', pady=(5, 0))

    def display_modern_icon_preview(self, parent_frame, suggestion):
        """عرض معاينة الأيقونة العصرية"""
        try:
            # الحصول على الصورة
            image = suggestion.image if hasattr(suggestion, 'image') else suggestion['image']

            # تغيير الحجم للمعاينة
            preview_size = (64, 64)
            preview_image = image.copy()
            preview_image.thumbnail(preview_size, Image.Resampling.LANCZOS)

            # تحويل لـ PhotoImage
            photo = PhotoImage(preview_image)

            # عرض الصورة
            image_label = tk.Label(
                parent_frame,
                image=photo,
                bg='white'
            )
            image_label.image = photo  # حفظ المرجع
            image_label.pack(padx=5, pady=5)

        except Exception as e:
            # في حالة فشل عرض الصورة، عرض رسالة
            error_label = tk.Label(
                parent_frame,
                text="❌ خطأ في المعاينة",
                font=('Segoe UI', 8),
                fg='red',
                bg='white'
            )
            error_label.pack(padx=5, pady=5)

    def draw_suggestion_preview(self, canvas, suggestion):
        """رسم معاينة الاقتراح"""
        # ألوان افتراضية إذا لم تكن متوفرة
        primary_color = suggestion.colors[0] if suggestion.colors else self.colors['accent_blue']
        secondary_color = suggestion.colors[1] if len(suggestion.colors) > 1 else self.colors['accent_pink']

        # رسم بناءً على العناصر المقترحة
        if 'window' in suggestion.elements or 'computer' in suggestion.elements:
            # أيقونة تطبيق سطح المكتب
            canvas.create_rectangle(16, 20, 48, 44, fill=primary_color, outline='black', width=2)
            canvas.create_rectangle(20, 16, 44, 20, fill=secondary_color, outline='black', width=1)
            canvas.create_oval(22, 17, 25, 19, fill='white')
        elif 'globe' in suggestion.elements or 'browser' in suggestion.elements:
            # أيقونة ويب
            canvas.create_oval(16, 16, 48, 48, fill=primary_color, outline='black', width=2)
            canvas.create_arc(20, 20, 44, 44, start=0, extent=180, outline='white', width=2)
            canvas.create_arc(20, 20, 44, 44, start=180, extent=180, outline='white', width=2)
            canvas.create_line(32, 16, 32, 48, fill='white', width=2)
        elif 'gamepad' in suggestion.elements or 'joystick' in suggestion.elements:
            # أيقونة ألعاب
            canvas.create_rectangle(16, 24, 48, 40, fill=primary_color, outline='black', width=2)
            canvas.create_oval(20, 20, 28, 28, fill=secondary_color, outline='black')
            canvas.create_oval(36, 20, 44, 28, fill=secondary_color, outline='black')
        elif 'chart' in suggestion.elements or 'graph' in suggestion.elements:
            # أيقونة تحليل البيانات
            canvas.create_rectangle(16, 16, 48, 48, fill='white', outline='black', width=2)
            canvas.create_rectangle(20, 35, 24, 44, fill=primary_color)
            canvas.create_rectangle(28, 28, 32, 44, fill=secondary_color)
            canvas.create_rectangle(36, 20, 40, 44, fill=primary_color)
        elif 'dollar' in suggestion.elements or 'coin' in suggestion.elements:
            # أيقونة مالية
            canvas.create_oval(16, 16, 48, 48, fill=primary_color, outline='black', width=2)
            canvas.create_text(32, 32, text="$", fill='white', font=('Arial', 16, 'bold'))
        elif 'book' in suggestion.elements or 'graduation' in suggestion.elements:
            # أيقونة تعليمية
            canvas.create_rectangle(20, 20, 44, 44, fill=primary_color, outline='black', width=2)
            canvas.create_line(22, 26, 42, 26, fill='white', width=1)
            canvas.create_line(22, 30, 42, 30, fill='white', width=1)
            canvas.create_line(22, 34, 42, 34, fill='white', width=1)
        else:
            # أيقونة افتراضية
            canvas.create_rectangle(20, 20, 44, 44, fill=primary_color, outline='black', width=2)
            canvas.create_text(32, 32, text="?", fill='white', font=('Arial', 12, 'bold'))

    def open_icon_editor(self):
        """فتح محرر الأيقونات"""
        editor_window = tk.Toplevel(self.root)
        editor_window.title("🎨 محرر الأيقونات التفاعلي")
        editor_window.geometry("1200x800")
        editor_window.configure(bg='#1a1a2e')

        # إنشاء محرر الأيقونات
        icon_editor = IconEditor(editor_window, callback=self.on_icon_created)

        # تمرير معلومات التحليل للمحرر إذا كانت متوفرة
        if self.current_analysis:
            # يمكن إضافة وظائف لتمرير الألوان والعناصر المقترحة
            pass

    def on_icon_created(self, icon_path):
        """عند إنشاء أيقونة جديدة"""
        self.show_notification(f"تم إنشاء الأيقونة: {os.path.basename(icon_path)}", "success")

        # تطبيق الأيقونة تلقائياً
        self.icon_path.set(icon_path)
        self.update_command_preview()

    def open_ai_image_studio(self):
        """فتح استوديو الصور الذكي"""
        converter_window = tk.Toplevel(self.root)
        converter_window.title("🎨 استوديو الصور الذكي (AI Image Studio)")
        converter_window.geometry("1200x800")
        converter_window.configure(bg='#1a1a2e')

        # إنشاء محول الصور
        ai_studio = AIImageStudio(converter_window, callback=self.on_icon_created)

        # جعل النافذة في المقدمة
        converter_window.transient(self.root)
        converter_window.grab_set()

    def open_advanced_icon_editor(self):
        """فتح محرر الأيقونات المتطور المدمج"""
        # إنشاء نافذة محرر الأيقونات المتطور
        editor_window = tk.Toplevel(self.root)
        editor_window.title("🎨 محرر الأيقونات المتطور - Python to EXE Converter Pro")
        editor_window.geometry("1400x900")
        editor_window.configure(bg=self.colors['bg_primary'])

        # تطبيق نمط عصري
        editor_window.attributes('-alpha', 0.98)

        try:
            # إنشاء محرر الأيقونات المدمج
            self.create_integrated_icon_editor(editor_window)

            self.show_notification("🎨 تم فتح محرر الأيقونات المتطور", "success")

        except Exception as e:
            self.show_notification(f"خطأ في فتح المحرر: {e}", "error")
            editor_window.destroy()

    def create_integrated_icon_editor(self, parent_window):
        """إنشاء محرر الأيقونات المدمج"""
        # إعداد المتغيرات
        self.editor_window = parent_window
        self.canvas_size = 512
        self.zoom_level = 1.0
        self.current_tool = "brush"
        self.brush_size = 20
        self.current_color = "#4169E1"

        # إنشاء مولد الأيقونات المتطور
        self.modern_generator = ModernIconGenerator(callback=self.editor_log)
        self.smart_analyzer = SmartCodeAnalyzer()

        # شريط الأدوات العلوي
        self.create_editor_toolbar(parent_window)

        # المنطقة الرئيسية
        main_frame = tk.Frame(parent_window, bg=self.colors['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # لوحة الذكاء الاصطناعي (أعلى)
        self.create_ai_generation_panel(main_frame)

        # المنطقة الوسطى
        middle_frame = tk.Frame(main_frame, bg=self.colors['bg_primary'])
        middle_frame.pack(fill='both', expand=True, pady=10)

        # لوحة المعاينة (يمين)
        self.create_preview_panel_integrated(middle_frame)

        # منطقة الرسم (وسط)
        self.create_canvas_area_integrated(middle_frame)

        # لوحة الأدوات (يسار)
        self.create_tools_panel_integrated(middle_frame)

        # شريط الحالة
        self.create_editor_status_bar(parent_window)

    def editor_log(self, message):
        """تسجيل رسائل المحرر"""
        if hasattr(self, 'editor_status_label'):
            self.editor_status_label.configure(text=message)
        print(f"Editor: {message}")

    def create_editor_toolbar(self, parent):
        """إنشاء شريط أدوات المحرر"""
        toolbar = tk.Frame(parent, bg=self.colors['bg_tertiary'], height=60)
        toolbar.pack(fill='x', padx=10, pady=5)
        toolbar.pack_propagate(False)

        # عنوان المحرر
        title_label = tk.Label(
            toolbar,
            text="🎨 محرر الأيقونات المتطور - Python to EXE Converter Pro",
            font=('Segoe UI', 12, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_tertiary']
        )
        title_label.pack(side='left', padx=20, pady=15)

        # أزرار التحكم
        controls_frame = tk.Frame(toolbar, bg=self.colors['bg_tertiary'])
        controls_frame.pack(side='right', padx=20, pady=10)

        # أزرار الملف
        file_buttons = [
            ("📂", "فتح صورة", self.editor_open_image, self.colors['accent_blue']),
            ("💾", "حفظ", self.editor_save, self.colors['accent_green']),
            ("📤", "تصدير أيقونة", self.editor_export_icon, self.colors['accent_purple']),
            ("❌", "إغلاق", lambda: self.editor_window.destroy(), self.colors['error'])
        ]

        for icon, tooltip, command, color in file_buttons:
            btn = self.create_glass_button(controls_frame, icon, command, color, tooltip)
            btn.pack(side='right', padx=3)

    def create_ai_generation_panel(self, parent):
        """إنشاء لوحة توليد الذكاء الاصطناعي"""
        ai_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], relief='solid', bd=1)
        ai_frame.pack(fill='x', pady=(0, 10))

        # عنوان اللوحة
        title_frame = tk.Frame(ai_frame, bg=self.colors['bg_secondary'])
        title_frame.pack(fill='x', padx=15, pady=10)

        title_label = tk.Label(
            title_frame,
            text="🧠 مولد الأيقونات بالذكاء الاصطناعي المتطور",
            font=('Segoe UI', 12, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        title_label.pack(side='left')

        # أزرار التحكم
        controls_frame = tk.Frame(title_frame, bg=self.colors['bg_secondary'])
        controls_frame.pack(side='right')

        generate_btn = self.create_glass_button(
            controls_frame, "🎨", self.generate_ai_icons_integrated,
            self.colors['accent_pink'], "توليد أيقونات بالذكاء الاصطناعي"
        )
        generate_btn.pack(side='left', padx=5)

        analyze_btn = self.create_glass_button(
            controls_frame, "📊", self.analyze_current_project,
            self.colors['accent_purple'], "تحليل المشروع الحالي"
        )
        analyze_btn.pack(side='left', padx=5)

        # المحتوى الرئيسي
        content_frame = tk.Frame(ai_frame, bg=self.colors['bg_secondary'])
        content_frame.pack(fill='both', expand=True, padx=15, pady=(0, 15))

        # منطقة الإدخال
        input_frame = tk.Frame(content_frame, bg=self.colors['bg_secondary'])
        input_frame.pack(fill='x', pady=(0, 10))

        # تحليل المشروع
        analysis_frame = tk.LabelFrame(
            input_frame,
            text="📊 تحليل المشروع الحالي",
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        analysis_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))

        self.editor_analysis_text = tk.Text(
            analysis_frame,
            height=4,
            font=('Segoe UI', 9),
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            relief='flat',
            wrap='word'
        )
        self.editor_analysis_text.pack(fill='both', expand=True, padx=5, pady=5)

        # إدخال مخصص
        custom_frame = tk.LabelFrame(
            input_frame,
            text="✏️ وصف الأيقونة المطلوبة",
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        custom_frame.pack(side='right', fill='both', expand=True)

        self.editor_custom_prompt = tk.Text(
            custom_frame,
            height=4,
            font=('Segoe UI', 9),
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            relief='flat',
            wrap='word'
        )
        self.editor_custom_prompt.pack(fill='both', expand=True, padx=5, pady=5)

        # إدراج نص افتراضي
        self.editor_custom_prompt.insert('1.0', "اكتب وصف الأيقونة المطلوبة هنا...\nمثال: أيقونة تطبيق تحويل ملفات Python بألوان زرقاء وخضراء")

        # منطقة النتائج
        results_frame = tk.Frame(content_frame, bg=self.colors['bg_secondary'])
        results_frame.pack(fill='both', expand=True)

        # قائمة الاقتراحات
        suggestions_frame = tk.LabelFrame(
            results_frame,
            text="💡 الأيقونات المولدة",
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        suggestions_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))

        # إطار التمرير للاقتراحات
        suggestions_scroll_frame = tk.Frame(suggestions_frame, bg=self.colors['bg_secondary'])
        suggestions_scroll_frame.pack(fill='both', expand=True, padx=5, pady=5)

        self.editor_suggestions_frame = tk.Frame(suggestions_scroll_frame, bg=self.colors['bg_tertiary'])
        self.editor_suggestions_frame.pack(fill='both', expand=True)

        # معاينة مفصلة
        preview_frame = tk.LabelFrame(
            results_frame,
            text="👁️ معاينة الأيقونة",
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        preview_frame.pack(side='right', fill='both', expand=True)

        # منطقة المعاينة
        self.editor_preview_canvas = tk.Canvas(
            preview_frame,
            width=250,
            height=250,
            bg='white',
            relief='solid',
            bd=1
        )
        self.editor_preview_canvas.pack(padx=10, pady=10)

        # معلومات المعاينة
        self.editor_preview_info = tk.Label(
            preview_frame,
            text="اختر أيقونة من القائمة لمعاينتها",
            font=('Segoe UI', 9),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_secondary'],
            wraplength=230,
            justify='center'
        )
        self.editor_preview_info.pack(pady=5)

        # أزرار المعاينة
        preview_buttons = tk.Frame(preview_frame, bg=self.colors['bg_secondary'])
        preview_buttons.pack(pady=10)

        apply_btn = self.create_glass_button(
            preview_buttons, "✅", self.apply_ai_icon,
            self.colors['success'], "تطبيق الأيقونة"
        )
        apply_btn.pack(side='left', padx=5)

        save_btn = self.create_glass_button(
            preview_buttons, "💾", self.save_ai_icon,
            self.colors['accent_purple'], "حفظ الأيقونة"
        )
        save_btn.pack(side='left', padx=5)

        # عرض تحليل المشروع الحالي
        self.display_current_project_analysis()

    def display_current_project_analysis(self):
        """عرض تحليل المشروع الحالي"""
        if hasattr(self, 'current_analysis') and self.current_analysis:
            analysis = self.current_analysis
            analysis_text = f"""🎯 نوع التطبيق: {getattr(analysis, 'app_type', 'غير محدد')}
📝 الوصف: {getattr(analysis, 'description', 'تطبيق Python')}
📊 مستوى الثقة: {int(getattr(analysis, 'confidence', 0.7) * 100)}%

🔑 الكلمات المفتاحية:
{', '.join(getattr(analysis, 'keywords', ['python', 'application'])[:8])}

📚 المكتبات:
{', '.join(getattr(analysis, 'libraries', ['tkinter'])[:6])}

🎨 الألوان المقترحة:
{', '.join(getattr(analysis, 'colors', ['#4169E1', '#32CD32']))}"""
        else:
            # تحليل افتراضي للمشروع
            project_name = os.path.basename(self.source_path.get()) if self.source_path.get() else "مشروع Python"
            analysis_text = f"""🎯 المشروع الحالي: {project_name}
📝 النوع: تطبيق تحويل Python إلى EXE
🛠️ الأداة: PyInstaller
📊 الحالة: جاهز للتحليل

🔑 الكلمات المفتاحية:
python, converter, executable, pyinstaller, gui

📚 المكتبات المتوقعة:
tkinter, pillow, pyinstaller

🎨 الألوان المقترحة:
أزرق ملكي، أخضر زمردي، برتقالي دافئ

💡 انقر "تحليل المشروع" لتحليل أعمق"""

        self.editor_analysis_text.delete('1.0', tk.END)
        self.editor_analysis_text.insert('1.0', analysis_text)

    def generate_ai_icons_integrated(self):
        """توليد أيقونات بالذكاء الاصطناعي في المحرر المدمج"""
        self.editor_log("🧠 بدء توليد الأيقونات...")

        def run_generation():
            try:
                # الحصول على الوصف المخصص
                custom_text = self.editor_custom_prompt.get('1.0', tk.END).strip()

                # إنشاء تحليل
                if custom_text and not custom_text.startswith('اكتب وصف'):
                    analysis = self.smart_analyzer.analyze_code(custom_text)
                elif hasattr(self, 'current_analysis') and self.current_analysis:
                    analysis = self.current_analysis
                else:
                    # تحليل افتراضي للمشروع الحالي
                    project_text = f"Python to EXE converter application {self.source_path.get() or 'GUI tool'}"
                    analysis = self.smart_analyzer.analyze_code(project_text)

                # توليد الأيقونات
                suggestions = self.modern_generator.generate_modern_icons(analysis, count=6)
                self.editor_icon_suggestions = suggestions

                # عرض النتائج
                self.root.after(0, self.display_ai_suggestions_integrated)
                self.root.after(0, lambda: self.editor_log(f"✅ تم توليد {len(suggestions)} أيقونة"))

            except Exception as e:
                self.root.after(0, lambda: self.editor_log(f"❌ خطأ في التوليد: {e}"))

        threading.Thread(target=run_generation, daemon=True).start()

    def analyze_current_project(self):
        """تحليل المشروع الحالي"""
        if not self.source_path.get():
            self.editor_log("❌ لا يوجد مشروع محدد")
            return

        self.editor_log("📊 تحليل المشروع...")

        def run_analysis():
            try:
                analysis = self.smart_analyzer.analyze_code(self.source_path.get())
                self.current_analysis = analysis

                # عرض النتائج
                self.root.after(0, self.display_current_project_analysis)
                self.root.after(0, lambda: self.editor_log("✅ تم تحليل المشروع"))

            except Exception as e:
                self.root.after(0, lambda: self.editor_log(f"❌ خطأ في التحليل: {e}"))

        threading.Thread(target=run_analysis, daemon=True).start()

    def display_ai_suggestions_integrated(self):
        """عرض اقتراحات الذكاء الاصطناعي"""
        # مسح الاقتراحات السابقة
        for widget in self.editor_suggestions_frame.winfo_children():
            widget.destroy()

        if not hasattr(self, 'editor_icon_suggestions') or not self.editor_icon_suggestions:
            no_suggestions = tk.Label(
                self.editor_suggestions_frame,
                text="لا توجد اقتراحات متاحة",
                font=('Segoe UI', 10),
                fg=self.colors['text_muted'],
                bg=self.colors['bg_tertiary']
            )
            no_suggestions.pack(pady=20)
            return

        # عرض الاقتراحات في شبكة
        for i, suggestion in enumerate(self.editor_icon_suggestions):
            row, col = i // 3, i % 3

            # إطار الاقتراح
            suggestion_frame = tk.Frame(
                self.editor_suggestions_frame,
                bg=self.colors['bg_card'],
                relief='solid',
                bd=1
            )
            suggestion_frame.grid(row=row, column=col, padx=5, pady=5, sticky='ew')

            # صورة مصغرة
            thumbnail = suggestion.image.resize((60, 60), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(thumbnail)

            img_label = tk.Label(
                suggestion_frame,
                image=photo,
                bg=self.colors['bg_card'],
                cursor='hand2'
            )
            img_label.pack(pady=3)
            img_label.image = photo  # حفظ مرجع

            # معلومات الاقتراح
            info_label = tk.Label(
                suggestion_frame,
                text=f"{suggestion.style[:15]}\n⭐ {suggestion.confidence:.1f}",
                font=('Segoe UI', 8),
                fg=self.colors['text_primary'],
                bg=self.colors['bg_card'],
                justify='center'
            )
            info_label.pack(pady=2)

            # ربط النقر
            def on_click(s=suggestion):
                self.preview_ai_suggestion(s)

            img_label.bind('<Button-1>', lambda e, s=suggestion: on_click(s))
            suggestion_frame.bind('<Button-1>', lambda e, s=suggestion: on_click(s))

        # تكوين الشبكة
        for i in range(3):
            self.editor_suggestions_frame.columnconfigure(i, weight=1)

    def preview_ai_suggestion(self, suggestion):
        """معاينة اقتراح الذكاء الاصطناعي"""
        # عرض الصورة الكبيرة
        preview_image = suggestion.image.resize((230, 230), Image.Resampling.LANCZOS)
        photo = ImageTk.PhotoImage(preview_image)

        self.editor_preview_canvas.delete("all")
        self.editor_preview_canvas.create_image(125, 125, image=photo)
        self.editor_preview_canvas.image = photo  # حفظ مرجع

        # عرض المعلومات
        info_text = f"""🎨 النمط: {suggestion.style}
📝 الوصف: {suggestion.description}
⭐ الثقة: {suggestion.confidence:.1f}
🎨 الألوان: {', '.join(suggestion.colors[:2])}

انقر "تطبيق" لاستخدام الأيقونة"""

        self.editor_preview_info.configure(text=info_text)
        self.selected_ai_suggestion = suggestion

    def apply_ai_icon(self):
        """تطبيق الأيقونة المولدة"""
        if hasattr(self, 'selected_ai_suggestion'):
            suggestion = self.selected_ai_suggestion

            # حفظ الأيقونة مؤقتاً
            temp_path = os.path.join(os.path.dirname(__file__), "temp_ai_icon.png")
            suggestion.image.save(temp_path)

            # تطبيق الأيقونة على المشروع
            self.icon_path.set(temp_path)
            self.update_icon_preview()

            # إغلاق المحرر
            self.editor_window.destroy()

            self.show_notification(f"✅ تم تطبيق الأيقونة: {suggestion.style}", "success")
        else:
            self.editor_log("❌ لم يتم تحديد أيقونة")

    def save_ai_icon(self):
        """حفظ الأيقونة المولدة"""
        if hasattr(self, 'selected_ai_suggestion'):
            suggestion = self.selected_ai_suggestion

            file_path = filedialog.asksaveasfilename(
                title="حفظ الأيقونة",
                defaultextension=".ico",
                filetypes=[
                    ("ICO files", "*.ico"),
                    ("PNG files", "*.png"),
                    ("All files", "*.*")
                ]
            )

            if file_path:
                try:
                    if file_path.lower().endswith('.ico'):
                        # حفظ كأيقونة ICO متعددة الأحجام
                        sizes = [(16,16), (32,32), (48,48), (64,64), (128,128), (256,256)]
                        images = []
                        for size in sizes:
                            resized = suggestion.image.resize(size, Image.Resampling.LANCZOS)
                            images.append(resized)
                        images[0].save(file_path, format='ICO', sizes=[(img.width, img.height) for img in images])
                    else:
                        suggestion.image.save(file_path)

                    self.editor_log(f"💾 تم حفظ الأيقونة: {os.path.basename(file_path)}")
                except Exception as e:
                    self.editor_log(f"❌ خطأ في الحفظ: {e}")
        else:
            self.editor_log("❌ لم يتم تحديد أيقونة")

    # وظائف أساسية للمحرر
    def create_preview_panel_integrated(self, parent):
        """إنشاء لوحة المعاينة المدمجة"""
        preview_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], width=300, relief='solid', bd=1)
        preview_frame.pack(side='right', fill='y', padx=(10, 0))
        preview_frame.pack_propagate(False)

        # عنوان اللوحة
        title_label = tk.Label(
            preview_frame,
            text="🖼️ معاينة الصورة",
            font=('Segoe UI', 12, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        title_label.pack(pady=15)

        # منطقة سحب وإفلات
        drop_frame = tk.LabelFrame(
            preview_frame,
            text="📁 سحب وإفلات الصور",
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        drop_frame.pack(fill='x', padx=15, pady=10)

        self.editor_drop_area = tk.Label(
            drop_frame,
            text="🖼️\n\nاسحب الصورة هنا\nأو انقر للتصفح\n\nPNG, JPG, JPEG, BMP",
            font=('Segoe UI', 9),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_tertiary'],
            relief='ridge',
            bd=2,
            height=6,
            cursor='hand2',
            justify='center'
        )
        self.editor_drop_area.pack(fill='x', padx=8, pady=8)
        self.editor_drop_area.bind('<Button-1>', lambda e: self.editor_open_image())

        # معاينة الصورة
        preview_img_frame = tk.LabelFrame(
            preview_frame,
            text="📷 معاينة الصورة",
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        preview_img_frame.pack(fill='x', padx=15, pady=10)

        self.editor_image_preview = tk.Label(
            preview_img_frame,
            text="لا توجد صورة",
            font=('Segoe UI', 9),
            fg=self.colors['text_muted'],
            bg=self.colors['bg_tertiary'],
            width=25,
            height=8
        )
        self.editor_image_preview.pack(padx=8, pady=8)

        # معلومات الصورة
        info_frame = tk.LabelFrame(
            preview_frame,
            text="📊 معلومات الصورة",
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        info_frame.pack(fill='x', padx=15, pady=10)

        self.editor_image_info = tk.Label(
            info_frame,
            text="لا توجد صورة محملة",
            font=('Segoe UI', 9),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_secondary'],
            justify='center'
        )
        self.editor_image_info.pack(fill='x', padx=8, pady=8)

    def create_canvas_area_integrated(self, parent):
        """إنشاء منطقة الرسم المدمجة"""
        canvas_frame = tk.Frame(parent, bg=self.colors['bg_primary'])
        canvas_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))

        # شريط أدوات الرسم
        canvas_toolbar = tk.Frame(canvas_frame, bg=self.colors['bg_tertiary'], height=50)
        canvas_toolbar.pack(fill='x', pady=(0, 10))
        canvas_toolbar.pack_propagate(False)

        # أزرار الزوم
        zoom_frame = tk.Frame(canvas_toolbar, bg=self.colors['bg_tertiary'])
        zoom_frame.pack(side='left', padx=15, pady=8)

        zoom_buttons = [
            ("🔍+", "تكبير", self.editor_zoom_in, self.colors['accent_green']),
            ("🔍-", "تصغير", self.editor_zoom_out, self.colors['accent_green']),
            ("🎯", "ملائمة", self.editor_zoom_fit, self.colors['accent_blue'])
        ]

        for icon, tooltip, command, color in zoom_buttons:
            btn = self.create_glass_button(zoom_frame, icon, command, color, tooltip)
            btn.pack(side='left', padx=2)

        # معلومات اللوحة
        info_frame = tk.Frame(canvas_toolbar, bg=self.colors['bg_tertiary'])
        info_frame.pack(side='right', padx=15, pady=8)

        self.editor_canvas_info = tk.Label(
            info_frame,
            text=f"📐 {self.canvas_size}×{self.canvas_size}px | 🔍 {int(self.zoom_level * 100)}%",
            font=('Segoe UI', 9),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_tertiary']
        )
        self.editor_canvas_info.pack()

        # منطقة الرسم
        canvas_container = tk.Frame(canvas_frame, bg=self.colors['bg_primary'], relief='solid', bd=2)
        canvas_container.pack(fill='both', expand=True)

        # Canvas للرسم
        display_size = int(self.canvas_size * self.zoom_level)
        self.editor_canvas = tk.Canvas(
            canvas_container,
            bg='white',
            width=display_size,
            height=display_size,
            relief='flat',
            highlightthickness=0
        )
        self.editor_canvas.pack(fill='both', expand=True)

    def create_tools_panel_integrated(self, parent):
        """إنشاء لوحة الأدوات المدمجة"""
        tools_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], width=250, relief='solid', bd=1)
        tools_frame.pack(side='left', fill='y')
        tools_frame.pack_propagate(False)

        # عنوان الأدوات
        title_label = tk.Label(
            tools_frame,
            text="🛠️ أدوات التصميم",
            font=('Segoe UI', 12, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        title_label.pack(pady=15)

        # أدوات الرسم
        drawing_frame = tk.LabelFrame(
            tools_frame,
            text="🎨 أدوات الرسم",
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        drawing_frame.pack(fill='x', padx=10, pady=5)

        drawing_tools = [
            ("🖌️", "brush", "فرشاة"),
            ("✏️", "pencil", "قلم"),
            ("🖍️", "eraser", "ممحاة"),
            ("🪣", "bucket", "دلو"),
            ("💧", "dropper", "قطارة"),
            ("📏", "line", "خط")
        ]

        self.editor_tool_buttons = {}
        tools_grid = tk.Frame(drawing_frame, bg=self.colors['bg_secondary'])
        tools_grid.pack(padx=5, pady=5)

        for i, (icon, tool_id, tooltip) in enumerate(drawing_tools):
            row, col = i // 3, i % 3
            btn = tk.Button(
                tools_grid,
                text=icon,
                font=('Segoe UI', 16),
                command=lambda t=tool_id: self.editor_select_tool(t),
                bg=self.colors['bg_tertiary'],
                fg=self.colors['text_primary'],
                relief='flat',
                width=3,
                height=2,
                cursor='hand2'
            )
            btn.grid(row=row, column=col, padx=2, pady=2)
            self.editor_tool_buttons[tool_id] = btn

        # إعدادات الأداة
        settings_frame = tk.LabelFrame(
            tools_frame,
            text="⚙️ إعدادات الأداة",
            font=('Segoe UI', 10, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        settings_frame.pack(fill='x', padx=10, pady=5)

        # حجم الفرشاة
        size_frame = tk.Frame(settings_frame, bg=self.colors['bg_secondary'])
        size_frame.pack(fill='x', padx=5, pady=5)

        tk.Label(
            size_frame,
            text="📏 حجم الفرشاة:",
            font=('Segoe UI', 9),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        ).pack(anchor='w')

        self.editor_brush_size_var = tk.IntVar(value=self.brush_size)
        brush_scale = tk.Scale(
            size_frame,
            from_=1,
            to=100,
            orient='horizontal',
            variable=self.editor_brush_size_var,
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            troughcolor=self.colors['bg_tertiary'],
            activebackground=self.colors['accent_blue']
        )
        brush_scale.pack(fill='x', pady=2)

        # اختيار اللون
        color_frame = tk.Frame(settings_frame, bg=self.colors['bg_secondary'])
        color_frame.pack(fill='x', padx=5, pady=10)

        tk.Label(
            color_frame,
            text="🎨 اللون الحالي:",
            font=('Segoe UI', 9),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        ).pack(anchor='w')

        self.editor_color_display = tk.Label(
            color_frame,
            bg=self.current_color,
            width=8,
            height=3,
            relief='solid',
            bd=2,
            cursor='hand2'
        )
        self.editor_color_display.pack(pady=5)
        self.editor_color_display.bind('<Button-1>', self.editor_choose_color)

        # تحديد الأداة الافتراضية
        self.editor_select_tool("brush")

    def create_editor_status_bar(self, parent):
        """إنشاء شريط حالة المحرر"""
        status_frame = tk.Frame(parent, bg=self.colors['bg_tertiary'], height=30)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)

        self.editor_status_label = tk.Label(
            status_frame,
            text="🎨 محرر الأيقونات المتطور - جاهز للتصميم",
            font=('Segoe UI', 9),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_tertiary']
        )
        self.editor_status_label.pack(side='left', padx=15, pady=5)

    # وظائف المحرر الأساسية
    def editor_select_tool(self, tool_name):
        """تحديد أداة في المحرر"""
        # إعادة تعيين ألوان الأزرار
        for btn in self.editor_tool_buttons.values():
            btn.configure(bg=self.colors['bg_tertiary'])

        # تمييز الأداة المحددة
        if tool_name in self.editor_tool_buttons:
            self.editor_tool_buttons[tool_name].configure(bg=self.colors['accent_blue'])

        self.current_tool = tool_name
        self.editor_log(f"🛠️ تم تحديد أداة: {tool_name}")

    def editor_choose_color(self, event=None):
        """اختيار لون في المحرر"""
        color = colorchooser.askcolor(title="اختر لون", color=self.current_color)[1]
        if color:
            self.current_color = color
            self.editor_color_display.configure(bg=color)
            self.editor_log(f"🎨 تم تحديد اللون: {color}")

    def editor_open_image(self):
        """فتح صورة في المحرر"""
        file_path = filedialog.askopenfilename(
            title="اختر صورة",
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.bmp *.gif"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                # تحميل الصورة
                image = Image.open(file_path)

                # تحويل لـ RGBA
                if image.mode != 'RGBA':
                    image = image.convert('RGBA')

                # تغيير الحجم إذا لزم الأمر
                if image.size != (self.canvas_size, self.canvas_size):
                    image = image.resize((self.canvas_size, self.canvas_size), Image.Resampling.LANCZOS)

                # عرض المعاينة
                preview_image = image.resize((150, 150), Image.Resampling.LANCZOS)
                photo = ImageTk.PhotoImage(preview_image)
                self.editor_image_preview.configure(image=photo, text="")
                self.editor_image_preview.image = photo

                # عرض معلومات الصورة
                width, height = image.size
                mode = image.mode
                info_text = f"""📐 الأبعاد: {width} × {height}
🎨 النمط: {mode}
📁 الملف: {os.path.basename(file_path)}"""

                self.editor_image_info.configure(text=info_text)

                # حفظ الصورة
                self.editor_current_image = image

                self.editor_log(f"📂 تم فتح الصورة: {os.path.basename(file_path)}")

            except Exception as e:
                self.editor_log(f"❌ خطأ في فتح الصورة: {e}")

    def editor_save(self):
        """حفظ في المحرر"""
        self.editor_log("💾 حفظ - قريباً")

    def editor_export_icon(self):
        """تصدير أيقونة من المحرر"""
        if hasattr(self, 'editor_current_image'):
            file_path = filedialog.asksaveasfilename(
                title="تصدير أيقونة",
                defaultextension=".ico",
                filetypes=[
                    ("ICO files", "*.ico"),
                    ("PNG files", "*.png"),
                    ("All files", "*.*")
                ]
            )

            if file_path:
                try:
                    if file_path.lower().endswith('.ico'):
                        # حفظ كأيقونة ICO متعددة الأحجام
                        sizes = [(16,16), (32,32), (48,48), (64,64), (128,128), (256,256)]
                        images = []
                        for size in sizes:
                            resized = self.editor_current_image.resize(size, Image.Resampling.LANCZOS)
                            images.append(resized)
                        images[0].save(file_path, format='ICO', sizes=[(img.width, img.height) for img in images])
                    else:
                        self.editor_current_image.save(file_path)

                    self.editor_log(f"📤 تم تصدير الأيقونة: {os.path.basename(file_path)}")
                except Exception as e:
                    self.editor_log(f"❌ خطأ في التصدير: {e}")
        else:
            self.editor_log("❌ لا توجد صورة للتصدير")

    def editor_zoom_in(self):
        """تكبير في المحرر"""
        self.zoom_level = min(self.zoom_level * 1.5, 3.0)
        self.editor_update_zoom()

    def editor_zoom_out(self):
        """تصغير في المحرر"""
        self.zoom_level = max(self.zoom_level / 1.5, 0.3)
        self.editor_update_zoom()

    def editor_zoom_fit(self):
        """ملائمة الحجم في المحرر"""
        self.zoom_level = 1.0
        self.editor_update_zoom()

    def editor_update_zoom(self):
        """تحديث الزوم في المحرر"""
        self.editor_canvas_info.configure(
            text=f"📐 {self.canvas_size}×{self.canvas_size}px | 🔍 {int(self.zoom_level * 100)}%"
        )

        display_size = int(self.canvas_size * self.zoom_level)
        self.editor_canvas.configure(width=display_size, height=display_size)

    def apply_selected_icon(self):
        """تطبيق الأيقونة المحددة"""
        selection = self.suggestions_listbox.curselection()
        if not selection or not self.icon_suggestions:
            self.show_notification("يرجى اختيار اقتراح أولاً", "warning")
            return

        index = selection[0]
        if index >= len(self.icon_suggestions):
            return

        suggestion = self.icon_suggestions[index]

        # إنشاء أيقونة بناءً على الاقتراح
        self.create_icon_from_suggestion(suggestion)

    def create_icon_from_suggestion(self, suggestion):
        """إنشاء أيقونة من الاقتراح"""
        try:
            # التحقق من نوع الاقتراح
            is_modern = hasattr(suggestion, 'modern') or (isinstance(suggestion, dict) and suggestion.get('modern', False))

            if is_modern and (hasattr(suggestion, 'image') or 'image' in suggestion):
                # أيقونة عصرية جاهزة
                image = suggestion.image if hasattr(suggestion, 'image') else suggestion['image']
                name = suggestion.name if hasattr(suggestion, 'name') else suggestion['name']
            else:
                # إنشاء أيقونة تقليدية
                image = self._create_traditional_icon(suggestion)
                name = suggestion.name if hasattr(suggestion, 'name') else suggestion['name']

            # حفظ الأيقونة
            base_path = os.path.dirname(self.source_path.get()) if self.source_path.get() else os.getcwd()
            safe_name = "".join(c for c in name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            icon_filename = f"icon_{safe_name.lower().replace(' ', '_')}.ico"
            icon_path = os.path.join(base_path, icon_filename)

            # إنشاء أحجام متعددة
            sizes = [16, 32, 48, 64, 128, 256]
            images = []
            for size in sizes:
                resized = image.copy()
                resized.thumbnail((size, size), Image.Resampling.LANCZOS)

                # إنشاء صورة مربعة
                square = Image.new('RGBA', (size, size), (0, 0, 0, 0))
                x = (size - resized.width) // 2
                y = (size - resized.height) // 2
                square.paste(resized, (x, y), resized)

                images.append(square)

            # حفظ كملف ICO
            images[0].save(icon_path, format='ICO', sizes=[(img.width, img.height) for img in images])

            # تطبيق الأيقونة
            self.icon_path.set(icon_path)
            self.update_command_preview()

            self.show_notification(f"تم إنشاء وتطبيق الأيقونة: {icon_filename}", "success")

        except Exception as e:
            self.show_notification(f"خطأ في إنشاء الأيقونة: {e}", "error")

    def _create_traditional_icon(self, suggestion):
        """إنشاء أيقونة تقليدية"""

        # إنشاء صورة 256x256
        image = Image.new('RGBA', (256, 256), (255, 255, 255, 0))
        draw = ImageDraw.Draw(image)

        # ألوان
        colors = suggestion.colors if hasattr(suggestion, 'colors') else suggestion.get('colors', [])
        primary_color = colors[0] if colors else self.colors['accent_blue']
        secondary_color = colors[1] if len(colors) > 1 else self.colors['accent_pink']

        # تحويل الألوان من hex إلى RGB
        def hex_to_rgb(hex_color):
            hex_color = hex_color.lstrip('#')
            return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

        primary_rgb = hex_to_rgb(primary_color)
        secondary_rgb = hex_to_rgb(secondary_color)

        # رسم الأيقونة بناءً على العناصر
        elements = suggestion.elements if hasattr(suggestion, 'elements') else suggestion.get('elements', [])

        if 'window' in elements:
            # نافذة تطبيق
            draw.rectangle([64, 80, 192, 176], fill=primary_rgb, outline=(0, 0, 0), width=4)
            draw.rectangle([80, 64, 176, 80], fill=secondary_rgb, outline=(0, 0, 0), width=2)
            draw.ellipse([88, 68, 100, 76], fill=(255, 255, 255))
        elif 'globe' in elements:
            # كرة أرضية
            draw.ellipse([64, 64, 192, 192], fill=primary_rgb, outline=(0, 0, 0), width=4)
            draw.arc([80, 80, 176, 176], start=0, end=180, fill=(255, 255, 255), width=4)
            draw.line([128, 64, 128, 192], fill=(255, 255, 255), width=4)
        else:
            # شكل افتراضي
            draw.rectangle([80, 80, 176, 176], fill=primary_rgb, outline=(0, 0, 0), width=4)
            draw.ellipse([112, 112, 144, 144], fill=secondary_rgb)

        return image

    # ==================== وظائف إدارة المكتبات الذكية ====================

    def dependency_callback(self, message, level="info"):
        """استقبال رسائل من نظام إدارة المكتبات"""
        self.add_to_log(message)

        # تحديث تبويب المكتبات إذا كان مفتوحاً
        if hasattr(self, 'libraries_text') and self.current_tab == "libraries":
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.libraries_text.insert(tk.END, f"[{timestamp}] {message}\n")
            self.libraries_text.see(tk.END)

    def smart_analysis(self):
        """تحليل ذكي سريع"""
        if not self.source_path.get():
            self.show_notification("يرجى اختيار ملف أو مجلد أولاً", "warning")
            return

        self.show_notification("بدء التحليل الذكي...", "info")
        self.switch_tab("libraries")
        self.analyze_dependencies()

    def quick_icon_design(self):
        """تصميم أيقونة سريع"""
        if not self.source_path.get():
            self.show_notification("يرجى اختيار ملف أو مجلد أولاً", "warning")
            return

        self.show_notification("بدء تصميم الأيقونة الذكي...", "info")
        self.switch_tab("icons")
        self.analyze_code_for_icons()

    def analyze_dependencies(self):
        """تحليل المكتبات المطلوبة"""
        if not self.source_path.get():
            self.show_notification("يرجى اختيار ملف أو مجلد أولاً", "warning")
            return

        if self.is_analyzing:
            self.show_notification("تحليل جاري بالفعل...", "warning")
            return

        self.is_analyzing = True
        self.analyze_btn.configure(text="⏳ جاري التحليل...", state='disabled')

        # تشغيل التحليل في thread منفصل
        def run_analysis():
            try:
                self.libraries_text.delete('1.0', tk.END)
                self.libraries_text.insert('1.0', "🧠 بدء التحليل الذكي للمكتبات...\n\n")

                # تحليل المشروع
                self.libraries_info = self.dependency_manager.analyze_project(self.source_path.get())

                # عرض النتائج
                self.root.after(0, self.display_libraries_results)

            except Exception as e:
                self.root.after(0, lambda: self.show_notification(f"خطأ في التحليل: {e}", "error"))
            finally:
                self.is_analyzing = False
                self.root.after(0, lambda: self.analyze_btn.configure(text="🧠 تحليل ذكي", state='normal'))

        threading.Thread(target=run_analysis, daemon=True).start()

    def display_libraries_results(self):
        """عرض نتائج تحليل المكتبات"""
        if not self.libraries_info:
            return

        self.libraries_text.delete('1.0', tk.END)

        # إحصائيات عامة
        total = len(self.libraries_info)
        installed = sum(1 for lib in self.libraries_info.values() if lib.status == "installed")
        missing = sum(1 for lib in self.libraries_info.values() if lib.status == "missing")
        outdated = sum(1 for lib in self.libraries_info.values() if lib.status == "outdated")

        self.libraries_text.insert('end', "📊 نتائج التحليل الذكي:\n")
        self.libraries_text.insert('end', "=" * 50 + "\n\n")
        self.libraries_text.insert('end', f"📦 إجمالي المكتبات: {total}\n")
        self.libraries_text.insert('end', f"✅ مثبتة: {installed}\n")
        self.libraries_text.insert('end', f"❌ مفقودة: {missing}\n")
        self.libraries_text.insert('end', f"🔄 تحتاج تحديث: {outdated}\n\n")

        # تفاصيل كل مكتبة
        self.libraries_text.insert('end', "📋 تفاصيل المكتبات:\n")
        self.libraries_text.insert('end', "-" * 50 + "\n\n")

        for lib_name, lib_info in sorted(self.libraries_info.items()):
            status_icon = {
                "installed": "✅",
                "missing": "❌",
                "outdated": "🔄",
                "error": "⚠️"
            }.get(lib_info.status, "❓")

            self.libraries_text.insert('end', f"{status_icon} {lib_name}\n")

            if lib_info.version_installed:
                self.libraries_text.insert('end', f"   📌 الإصدار المثبت: {lib_info.version_installed}\n")

            if lib_info.version_required:
                self.libraries_text.insert('end', f"   📋 الإصدار المطلوب: {lib_info.version_required}\n")

            if lib_info.source_files:
                files_str = ", ".join([os.path.basename(f) for f in lib_info.source_files[:3]])
                if len(lib_info.source_files) > 3:
                    files_str += f" و {len(lib_info.source_files) - 3} ملف آخر"
                self.libraries_text.insert('end', f"   📁 مستخدمة في: {files_str}\n")

            if lib_info.alternatives and lib_info.status == "missing":
                alts_str = ", ".join(lib_info.alternatives[:3])
                self.libraries_text.insert('end', f"   🔄 بدائل متاحة: {alts_str}\n")

            if lib_info.error_message:
                self.libraries_text.insert('end', f"   ⚠️ خطأ: {lib_info.error_message}\n")

            self.libraries_text.insert('end', "\n")

        # توصيات
        if missing > 0:
            self.libraries_text.insert('end', "💡 التوصيات:\n")
            self.libraries_text.insert('end', f"• انقر 'تثبيت المفقود' لتثبيت {missing} مكتبة مفقودة\n")
            self.libraries_text.insert('end', "• استخدم 'اختبار التشغيل' للتأكد من عمل المشروع\n")
            self.libraries_text.insert('end', "• أنشئ ملف requirements.txt للمشاركة\n")

    def install_missing(self):
        """تثبيت المكتبات المفقودة"""
        if not self.libraries_info:
            self.show_notification("يرجى تشغيل التحليل الذكي أولاً", "warning")
            return

        missing_libs = [lib for lib in self.libraries_info.values() if lib.status == "missing"]
        if not missing_libs:
            self.show_notification("جميع المكتبات مثبتة بالفعل!", "success")
            return

        self.install_btn.configure(text="⏳ جاري التثبيت...", state='disabled')
        self.show_notification(f"بدء تثبيت {len(missing_libs)} مكتبة...", "info")

        def run_installation():
            try:
                success = self.dependency_manager.install_missing_libraries()
                if success:
                    self.root.after(0, lambda: self.show_notification("تم تثبيت جميع المكتبات بنجاح!", "success"))
                else:
                    self.root.after(0, lambda: self.show_notification("فشل في تثبيت بعض المكتبات", "warning"))

                # إعادة عرض النتائج
                self.root.after(0, self.display_libraries_results)

            except Exception as e:
                self.root.after(0, lambda: self.show_notification(f"خطأ في التثبيت: {e}", "error"))
            finally:
                self.root.after(0, lambda: self.install_btn.configure(text="📦 تثبيت المفقود", state='normal'))

        threading.Thread(target=run_installation, daemon=True).start()

    def test_execution(self):
        """اختبار تشغيل المشروع"""
        if not self.source_path.get():
            self.show_notification("يرجى اختيار ملف أولاً", "warning")
            return

        self.test_btn.configure(text="⏳ جاري الاختبار...", state='disabled')
        self.show_notification("بدء اختبار تشغيل المشروع...", "info")

        def run_test():
            try:
                success = self.dependency_manager.test_project_execution(self.source_path.get())
                if success:
                    self.root.after(0, lambda: self.show_notification("المشروع يعمل بنجاح! ✅", "success"))
                else:
                    self.root.after(0, lambda: self.show_notification("فشل في تشغيل المشروع - راجع السجل", "error"))

            except Exception as e:
                self.root.after(0, lambda: self.show_notification(f"خطأ في الاختبار: {e}", "error"))
            finally:
                self.root.after(0, lambda: self.test_btn.configure(text="🧪 اختبار التشغيل", state='normal'))

        threading.Thread(target=run_test, daemon=True).start()

    def generate_requirements(self):
        """إنشاء ملف requirements.txt"""
        if not self.libraries_info:
            self.show_notification("يرجى تشغيل التحليل الذكي أولاً", "warning")
            return

        self.generate_req_btn.configure(text="⏳ جاري الإنشاء...", state='disabled')

        try:
            output_path = self.dependency_manager.generate_requirements_file()
            if output_path:
                self.show_notification(f"تم إنشاء ملف المتطلبات: {os.path.basename(output_path)}", "success")
            else:
                self.show_notification("فشل في إنشاء ملف المتطلبات", "error")
        except Exception as e:
            self.show_notification(f"خطأ في إنشاء الملف: {e}", "error")
        finally:
            self.generate_req_btn.configure(text="📝 إنشاء requirements", state='normal')

    def create_progress_section(self, parent):
        """إنشاء قسم شريط التقدم"""
        progress_frame = tk.Frame(parent, bg=self.colors['bg_tertiary'])
        progress_frame.pack(fill='x', pady=(15, 0))

        # عنوان التقدم
        progress_label = tk.Label(
            progress_frame,
            text="📊 تقدم العملية:",
            font=('Segoe UI', 11, 'bold'),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_tertiary']
        )
        progress_label.pack(anchor='w', pady=(0, 10))

        # شريط التقدم
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100,
            length=500,
            mode='determinate'
        )
        self.progress_bar.pack(fill='x', pady=(0, 10))

        # تسمية حالة التقدم
        self.progress_status_label = tk.Label(
            progress_frame,
            text="⏳ في انتظار بدء العملية...",
            font=('Segoe UI', 10),
            fg=self.colors['text_secondary'],
            bg=self.colors['bg_tertiary']
        )
        self.progress_status_label.pack(anchor='w')

    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        status_frame = tk.Frame(parent, bg=self.colors['bg_secondary'], relief='flat', bd=0)
        status_frame.pack(fill='x', pady=(20, 0))

        # محتوى شريط الحالة
        status_content = tk.Frame(status_frame, bg=self.colors['bg_secondary'])
        status_content.pack(fill='x', padx=20, pady=10)

        # أيقونة الحالة
        self.status_icon = tk.Label(
            status_content,
            text="✅",
            font=('Segoe UI', 14),
            fg=self.colors['success'],
            bg=self.colors['bg_secondary']
        )
        self.status_icon.pack(side='left', padx=(0, 10))

        # نص الحالة
        self.status_label = tk.Label(
            status_content,
            text="جاهز للاستخدام - Modern Glass UI",
            font=('Segoe UI', 11),
            fg=self.colors['text_primary'],
            bg=self.colors['bg_secondary']
        )
        self.status_label.pack(side='left')

        # معلومات إضافية على اليمين
        version_label = tk.Label(
            status_content,
            text="v2.0 | Modern Glass Theme",
            font=('Segoe UI', 9),
            fg=self.colors['text_muted'],
            bg=self.colors['bg_secondary']
        )
        version_label.pack(side='right')

    def start_background_animation(self):
        """بدء انيميشن الخلفية"""
        def animate():
            # تأثير نبضة للعنوان
            try:
                # هذا مجرد مثال بسيط للانيميشن
                self.root.after(2000, animate)
            except:
                pass

        animate()

    # ==================== وظائف التفاعل ====================

    def switch_tab(self, tab_name):
        """تبديل التبويبات"""
        self.current_tab = tab_name

        # إخفاء جميع التبويبات
        for widget in self.tabs_content.winfo_children():
            widget.pack_forget()

        # عرض التبويب المحدد
        if tab_name == "command":
            self.command_frame.pack(fill='both', expand=True)
            self.update_command_preview()
            self.update_tab_colors(self.command_tab_btn, [self.log_tab_btn, self.files_tab_btn, self.libraries_tab_btn, self.icons_tab_btn])
        elif tab_name == "log":
            self.log_frame.pack(fill='both', expand=True)
            self.update_tab_colors(self.log_tab_btn, [self.command_tab_btn, self.files_tab_btn, self.libraries_tab_btn, self.icons_tab_btn])
        elif tab_name == "files":
            self.files_frame.pack(fill='both', expand=True)
            self.update_tab_colors(self.files_tab_btn, [self.command_tab_btn, self.log_tab_btn, self.libraries_tab_btn, self.icons_tab_btn])
        elif tab_name == "libraries":
            self.libraries_frame.pack(fill='both', expand=True)
            self.update_tab_colors(self.libraries_tab_btn, [self.command_tab_btn, self.log_tab_btn, self.files_tab_btn, self.icons_tab_btn])
        elif tab_name == "icons":
            self.icons_frame.pack(fill='both', expand=True)
            self.update_tab_colors(self.icons_tab_btn, [self.command_tab_btn, self.log_tab_btn, self.files_tab_btn, self.libraries_tab_btn])

    def update_tab_colors(self, active_btn, inactive_btns):
        """تحديث ألوان أزرار التبويبات"""
        active_btn.configure(bg=self.colors['accent_blue'])
        for btn in inactive_btns:
            btn.configure(bg=self.colors['glass'])

    def quick_check(self):
        """فحص سريع"""
        self.show_notification("جاري الفحص السريع...", "info")
        self.check_requirements()

    def quick_open(self):
        """فتح سريع"""
        self.browse_file()

    def quick_help(self):
        """مساعدة سريعة"""
        help_text = """
🚀 دليل الاستخدام السريع:

1. اختر ملف Python أو مجلد
2. حدد مجلد الحفظ
3. اضبط الخيارات المتقدمة
4. انقر 'بدء التحويل'

💡 نصائح:
• استخدم 'ملف واحد' للتوزيع السهل
• فعل 'إخفاء الكونسول' للواجهات الرسومية
• استخدم 'فحص المتطلبات' قبل التحويل
        """
        messagebox.showinfo("مساعدة سريعة", help_text.strip())

    def browse_file(self):
        """تصفح ملف Python"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف Python",
            filetypes=[
                ("Python files", "*.py"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.source_path.set(file_path)
            self.add_to_log(f"📄 تم اختيار الملف: {os.path.basename(file_path)}")
            self.update_command_preview()
            self.update_status("✅ تم اختيار الملف المصدر", "success")
            self.show_notification(f"تم اختيار الملف: {os.path.basename(file_path)}", "success")

    def browse_folder(self):
        """تصفح مجلد Python"""
        folder_path = filedialog.askdirectory(title="اختر مجلد Python")
        if folder_path:
            # البحث عن ملف main.py أو __main__.py
            main_files = []
            try:
                for file in os.listdir(folder_path):
                    if file in ['main.py', '__main__.py', 'app.py']:
                        main_files.append(file)
            except (OSError, FileNotFoundError):
                # في حالة عدم إمكانية الوصول للمجلد
                self.source_path.set(folder_path)
                self.add_to_log(f"📁 تم اختيار المجلد: {os.path.basename(folder_path)}")
                self.add_to_log("⚠️ لا يمكن الوصول لمحتويات المجلد")
                self.show_notification("تم اختيار المجلد - لا يمكن فحص المحتويات", "warning")
                self.update_command_preview()
                self.update_status("✅ تم اختيار المجلد المصدر", "success")
                return

            if main_files:
                main_file = os.path.join(folder_path, main_files[0])
                self.source_path.set(main_file)
                self.add_to_log(f"📁 تم اختيار المجلد: {os.path.basename(folder_path)}")
                self.add_to_log(f"📄 الملف الرئيسي: {main_files[0]}")
                self.show_notification(f"تم اختيار المجلد مع الملف الرئيسي: {main_files[0]}", "success")
            else:
                self.source_path.set(folder_path)
                self.add_to_log(f"📁 تم اختيار المجلد: {os.path.basename(folder_path)}")
                self.add_to_log("⚠️ لم يتم العثور على ملف رئيسي، يرجى تحديد الملف يدوياً")
                self.show_notification("تم اختيار المجلد - لم يتم العثور على ملف رئيسي", "warning")

            self.update_command_preview()
            self.update_status("✅ تم اختيار المجلد المصدر", "success")

    def browse_output(self):
        """تصفح مجلد الحفظ"""
        folder_path = filedialog.askdirectory(title="اختر مجلد الحفظ")
        if folder_path:
            self.output_path.set(folder_path)
            self.add_to_log(f"💾 مجلد الحفظ: {folder_path}")
            self.update_command_preview()
            self.update_status("✅ تم اختيار مجلد الحفظ", "success")
            self.save_settings()
            self.show_notification("تم تحديد مجلد الحفظ بنجاح", "success")

    def preview_command(self):
        """معاينة أمر PyInstaller"""
        self.switch_tab("command")
        self.update_command_preview()
        self.add_to_log("👁️ تم عرض معاينة الأمر")
        self.update_status("👁️ معاينة الأمر", "info")

    def check_requirements(self):
        """فحص المتطلبات والاعتمادات"""
        self.switch_tab("log")
        self.add_to_log("🔍 بدء فحص المتطلبات...")
        self.update_status("🔍 جاري فحص المتطلبات...", "loading")

        # فحص PyInstaller
        try:
            import PyInstaller
            version = PyInstaller.__version__
            self.add_to_log(f"✅ PyInstaller مثبت - الإصدار: {version}")
        except ImportError:
            self.add_to_log("❌ PyInstaller غير مثبت")
            self.add_to_log("💡 تشغيل: pip install PyInstaller")
            self.update_status("❌ PyInstaller غير مثبت", "error")
            self.show_notification("PyInstaller غير مثبت - يرجى تثبيته أولاً", "error")
            return

        # فحص Python
        python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        self.add_to_log(f"✅ Python - الإصدار: {python_version}")

        # فحص الملف المصدر
        source = self.source_path.get()
        if source:
            if os.path.exists(source):
                self.add_to_log(f"✅ الملف المصدر موجود: {os.path.basename(source)}")

                # فحص بناء الجملة
                try:
                    with open(source, 'r', encoding='utf-8') as f:
                        code = f.read()
                    compile(code, source, 'exec')
                    self.add_to_log("✅ بناء الجملة صحيح")
                except SyntaxError as e:
                    self.add_to_log(f"❌ خطأ في بناء الجملة: {e}")
                except Exception as e:
                    self.add_to_log(f"⚠️ تحذير: {e}")
            else:
                self.add_to_log("❌ الملف المصدر غير موجود")
        else:
            self.add_to_log("⚠️ لم يتم تحديد ملف مصدر")

        # فحص مجلد الحفظ
        output = self.output_path.get()
        if output:
            if os.path.exists(output):
                self.add_to_log(f"✅ مجلد الحفظ موجود: {output}")
            else:
                self.add_to_log("⚠️ مجلد الحفظ غير موجود - سيتم إنشاؤه")
        else:
            self.add_to_log("⚠️ لم يتم تحديد مجلد حفظ")

        # فحص مساحة القرص
        if output and os.path.exists(output):
            try:
                import shutil
                total, used, free = shutil.disk_usage(output)
                free_gb = free // (1024**3)
                self.add_to_log(f"💾 المساحة المتاحة: {free_gb} جيجابايت")
                if free_gb < 1:
                    self.add_to_log("⚠️ تحذير: مساحة القرص قليلة")
            except:
                pass

        self.add_to_log("✅ انتهى فحص المتطلبات")
        self.update_status("✅ تم فحص المتطلبات", "success")
        self.show_notification("تم فحص المتطلبات بنجاح", "success")

    def start_conversion(self):
        """بدء عملية التحويل"""
        if self.is_converting:
            self.add_to_log("⚠️ عملية تحويل جارية بالفعل")
            return

        # التحقق من المتطلبات الأساسية
        if not self.source_path.get():
            messagebox.showerror("خطأ", "يرجى اختيار ملف Python أولاً")
            return

        if not os.path.exists(self.source_path.get()):
            messagebox.showerror("خطأ", "الملف المصدر غير موجود")
            return

        # التحقق من PyInstaller
        try:
            import PyInstaller
        except ImportError:
            messagebox.showerror("خطأ", "PyInstaller غير مثبت\nيرجى تشغيل: pip install PyInstaller")
            return

        # بدء التحويل في thread منفصل
        self.is_converting = True
        self.convert_btn.configure(text="⏳ جاري التحويل...", state='disabled')
        self.update_status("🚀 بدء عملية التحويل...", "loading")
        self.update_progress(0, "⏳ تحضير العملية...")

        # تبديل إلى تبويب السجل
        self.switch_tab("log")

        # بدء thread التحويل
        conversion_thread = threading.Thread(target=self.run_conversion, daemon=True)
        conversion_thread.start()

    def run_conversion(self):
        """تشغيل عملية التحويل"""
        try:
            self.add_to_log("🚀 بدء عملية التحويل...")
            self.update_progress(10, "📋 بناء الأمر...")

            # بناء الأمر
            command = self.build_pyinstaller_command()
            self.add_to_log(f"💻 الأمر: {command}")

            # إنشاء مجلد الحفظ إذا لم يكن موجوداً
            output_dir = self.output_path.get()
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
                self.add_to_log(f"📁 تم إنشاء مجلد الحفظ: {output_dir}")

            self.update_progress(20, "⚙️ تشغيل PyInstaller...")

            # تشغيل PyInstaller
            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                encoding='utf-8'
            )

            # قراءة المخرجات
            progress_steps = [30, 40, 50, 60, 70, 80, 90]
            step_index = 0

            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break

                if output:
                    # تنظيف النص وإضافته للسجل
                    clean_output = output.strip()
                    if clean_output:
                        self.add_to_log(f"📝 {clean_output}")

                        # تحديث التقدم بناءً على المخرجات
                        if step_index < len(progress_steps):
                            if any(keyword in clean_output.lower() for keyword in
                                   ['analyzing', 'building', 'collecting', 'copying', 'writing']):
                                self.update_progress(progress_steps[step_index], f"⚙️ {clean_output[:50]}...")
                                step_index += 1

            # انتظار انتهاء العملية
            return_code = process.wait()

            if return_code == 0:
                self.update_progress(100, "✅ تم التحويل بنجاح!")
                self.add_to_log("✅ تم التحويل بنجاح!")
                self.update_status("✅ تم التحويل بنجاح", "success")

                # البحث عن الملف المحول
                self.find_converted_files()

                # عرض رسالة نجاح
                self.root.after(0, lambda: self.show_notification(
                    "تم تحويل الملف بنجاح! 🎉", "success"
                ))

                self.root.after(0, lambda: messagebox.showinfo(
                    "نجح التحويل",
                    "تم تحويل الملف بنجاح!\nيمكنك العثور على الملف في مجلد الحفظ المحدد."
                ))

            else:
                self.update_progress(0, "❌ فشل التحويل")
                self.add_to_log(f"❌ فشل التحويل - رمز الخطأ: {return_code}")
                self.update_status("❌ فشل التحويل", "error")

                self.root.after(0, lambda: self.show_notification(
                    "فشل في التحويل - راجع السجل للتفاصيل", "error"
                ))

                self.root.after(0, lambda: messagebox.showerror(
                    "فشل التحويل",
                    "حدث خطأ أثناء التحويل.\nيرجى مراجعة السجل للتفاصيل."
                ))

        except Exception as e:
            self.update_progress(0, "❌ خطأ في التحويل")
            self.add_to_log(f"❌ خطأ: {str(e)}")
            self.update_status("❌ خطأ في التحويل", "error")

            self.root.after(0, lambda: self.show_notification(
                f"حدث خطأ غير متوقع: {str(e)}", "error"
            ))

            self.root.after(0, lambda: messagebox.showerror(
                "خطأ",
                f"حدث خطأ غير متوقع:\n{str(e)}"
            ))

        finally:
            # إعادة تفعيل الزر
            self.is_converting = False
            self.root.after(0, lambda: self.convert_btn.configure(
                text="🚀 بدء التحويل",
                state='normal'
            ))

    def build_pyinstaller_command(self):
        """بناء أمر PyInstaller"""
        if not self.source_path.get():
            return "❌ لم يتم تحديد ملف مصدر"

        command_parts = ["pyinstaller"]

        # الخيارات الأساسية
        if self.onefile.get():
            command_parts.append("--onefile")

        if self.noconsole.get():
            command_parts.append("--noconsole")

        if self.debug.get():
            command_parts.append("--debug=all")

        if self.optimize.get():
            command_parts.append("--optimize=2")

        # مجلد الحفظ
        if self.output_path.get():
            command_parts.append(f'--distpath="{self.output_path.get()}"')

        # الأيقونة
        if self.icon_path.get():
            command_parts.append(f'--icon="{self.icon_path.get()}"')

        # الملف المصدر
        command_parts.append(f'"{self.source_path.get()}"')

        return " ".join(command_parts)

    def find_converted_files(self):
        """البحث عن الملفات المحولة وإضافتها لقائمة الملفات"""
        try:
            output_dir = self.output_path.get() or "dist"
            source_name = os.path.splitext(os.path.basename(self.source_path.get()))[0]

            # البحث في مجلد dist
            possible_paths = [
                os.path.join(output_dir, f"{source_name}.exe"),
                os.path.join(output_dir, source_name, f"{source_name}.exe"),
                os.path.join("dist", f"{source_name}.exe"),
                os.path.join("dist", source_name, f"{source_name}.exe")
            ]

            found_files = []
            for path in possible_paths:
                if os.path.exists(path):
                    found_files.append(path)

            if found_files:
                self.files_text.delete('1.0', tk.END)
                self.files_text.insert('1.0', "📂 الملفات المحولة:\n")
                self.files_text.insert('end', "=" * 50 + "\n\n")

                for i, file_path in enumerate(found_files, 1):
                    file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                    self.files_text.insert('end', f"{i}. 📄 {os.path.basename(file_path)}\n")
                    self.files_text.insert('end', f"   📍 المسار: {file_path}\n")
                    self.files_text.insert('end', f"   📊 الحجم: {file_size:.2f} ميجابايت\n")
                    self.files_text.insert('end', f"   📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                self.add_to_log(f"📁 تم العثور على {len(found_files)} ملف محول")

                # تبديل إلى تبويب الملفات
                self.switch_tab("files")
            else:
                self.add_to_log("⚠️ لم يتم العثور على ملفات محولة في المسارات المتوقعة")

        except Exception as e:
            self.add_to_log(f"⚠️ خطأ في البحث عن الملفات: {e}")

    def update_command_preview(self):
        """تحديث معاينة الأمر"""
        if not hasattr(self, 'command_text'):
            return

        command = self.build_pyinstaller_command()

        self.command_text.delete('1.0', tk.END)
        self.command_text.insert('1.0', "💻 أمر PyInstaller الذي سيتم تنفيذه:\n")
        self.command_text.insert('end', "=" * 60 + "\n\n")
        self.command_text.insert('end', command + "\n\n")
        self.command_text.insert('end', "=" * 60 + "\n")
        self.command_text.insert('end', "📝 ملاحظات مهمة:\n\n")
        self.command_text.insert('end', "🔹 تأكد من صحة مسار الملف المصدر\n")
        self.command_text.insert('end', "🔹 تأكد من وجود مساحة كافية في مجلد الحفظ\n")
        self.command_text.insert('end', "🔹 قد تستغرق العملية عدة دقائق حسب حجم المشروع\n")
        self.command_text.insert('end', "🔹 استخدم 'فحص المتطلبات' للتأكد من جاهزية النظام\n\n")
        self.command_text.insert('end', "✨ نصائح للحصول على أفضل النتائج:\n\n")
        self.command_text.insert('end', "• فعل 'ملف واحد' لسهولة التوزيع\n")
        self.command_text.insert('end', "• استخدم 'إخفاء الكونسول' للتطبيقات ذات الواجهة الرسومية\n")
        self.command_text.insert('end', "• أضف أيقونة مخصصة لإعطاء مظهر احترافي\n")
        self.command_text.insert('end', "• استخدم 'وضع التصحيح' فقط عند وجود مشاكل\n")

    def add_to_log(self, message):
        """إضافة رسالة للسجل"""
        if not hasattr(self, 'log_text'):
            return

        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)

        # حفظ في ملف السجل
        try:
            with open('conversion_log.txt', 'a', encoding='utf-8') as f:
                f.write(log_message)
        except:
            pass

    def update_status(self, message, status_type="info"):
        """تحديث شريط الحالة"""
        # تحديث الأيقونة حسب النوع
        icons = {
            'info': '💡',
            'success': '✅',
            'warning': '⚠️',
            'error': '❌',
            'loading': '⏳'
        }

        colors = {
            'info': self.colors['accent_cyan'],
            'success': self.colors['success'],
            'warning': self.colors['warning'],
            'error': self.colors['error'],
            'loading': self.colors['accent_blue']
        }

        icon = icons.get(status_type, '💡')
        color = colors.get(status_type, self.colors['accent_cyan'])

        if hasattr(self, 'status_icon'):
            self.status_icon.configure(text=icon, fg=color)

        if hasattr(self, 'status_label'):
            self.status_label.configure(text=message)

    def update_progress(self, value, message=""):
        """تحديث شريط التقدم"""
        if hasattr(self, 'progress_var'):
            self.progress_var.set(value)

        if hasattr(self, 'progress_status_label') and message:
            self.progress_status_label.configure(text=message)

    def show_notification(self, message, type="info"):
        """عرض إشعار مؤقت"""
        # هذا مجرد محاكاة بسيطة - يمكن تطويرها لاحقاً
        print(f"[{type.upper()}] {message}")

    def run(self):
        """تشغيل التطبيق"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.save_settings()
            self.root.quit()
        except Exception as e:
            print(f"خطأ في تشغيل التطبيق: {e}")
        finally:
            self.save_settings()

def main():
    """الدالة الرئيسية"""
    try:
        app = ModernPyInstallerGUI()
        app.run()
    except Exception as e:
        print(f"خطأ في بدء التطبيق: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
