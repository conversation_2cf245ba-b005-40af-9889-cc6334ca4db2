#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python to EXE Converter Pro v3.0 - Custom Exceptions
استثناءات مخصصة للتطبيق

يحتوي هذا الملف على جميع الاستثناءات المخصصة المستخدمة في التطبيق
لتوفير معالجة أفضل للأخطاء ورسائل خطأ واضحة للمستخدم
"""

class ConverterError(Exception):
    """استثناء عام لأخطاء التحويل"""
    def __init__(self, message, error_code=None, details=None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)
    
    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message

class ConfigError(ConverterError):
    """استثناء لأخطاء الإعدادات"""
    def __init__(self, message, config_key=None, config_value=None):
        self.config_key = config_key
        self.config_value = config_value
        details = {
            'config_key': config_key,
            'config_value': config_value
        }
        super().__init__(message, "CONFIG_ERROR", details)

class DependencyError(ConverterError):
    """استثناء لأخطاء التبعيات والمكتبات"""
    def __init__(self, message, dependency_name=None, required_version=None):
        self.dependency_name = dependency_name
        self.required_version = required_version
        details = {
            'dependency_name': dependency_name,
            'required_version': required_version
        }
        super().__init__(message, "DEPENDENCY_ERROR", details)

class ValidationError(ConverterError):
    """استثناء لأخطاء التحقق من صحة البيانات"""
    def __init__(self, message, field_name=None, field_value=None):
        self.field_name = field_name
        self.field_value = field_value
        details = {
            'field_name': field_name,
            'field_value': field_value
        }
        super().__init__(message, "VALIDATION_ERROR", details)

class FileError(ConverterError):
    """استثناء لأخطاء الملفات"""
    def __init__(self, message, file_path=None, operation=None):
        self.file_path = file_path
        self.operation = operation
        details = {
            'file_path': file_path,
            'operation': operation
        }
        super().__init__(message, "FILE_ERROR", details)

class ProcessError(ConverterError):
    """استثناء لأخطاء العمليات والمعالجة"""
    def __init__(self, message, process_name=None, exit_code=None, stderr=None):
        self.process_name = process_name
        self.exit_code = exit_code
        self.stderr = stderr
        details = {
            'process_name': process_name,
            'exit_code': exit_code,
            'stderr': stderr
        }
        super().__init__(message, "PROCESS_ERROR", details)

class PluginError(ConverterError):
    """استثناء لأخطاء الإضافات"""
    def __init__(self, message, plugin_name=None, plugin_version=None):
        self.plugin_name = plugin_name
        self.plugin_version = plugin_version
        details = {
            'plugin_name': plugin_name,
            'plugin_version': plugin_version
        }
        super().__init__(message, "PLUGIN_ERROR", details)

class AIServiceError(ConverterError):
    """استثناء لأخطاء خدمات الذكاء الاصطناعي"""
    def __init__(self, message, service_name=None, api_error=None):
        self.service_name = service_name
        self.api_error = api_error
        details = {
            'service_name': service_name,
            'api_error': api_error
        }
        super().__init__(message, "AI_SERVICE_ERROR", details)

class SecurityError(ConverterError):
    """استثناء لأخطاء الأمان"""
    def __init__(self, message, security_check=None, threat_level=None):
        self.security_check = security_check
        self.threat_level = threat_level
        details = {
            'security_check': security_check,
            'threat_level': threat_level
        }
        super().__init__(message, "SECURITY_ERROR", details)

# دالة مساعدة لمعالجة الاستثناءات
def handle_exception(exception, logger=None):
    """معالجة الاستثناءات وتسجيلها"""
    if isinstance(exception, ConverterError):
        error_msg = f"خطأ في التطبيق: {exception.message}"
        if exception.error_code:
            error_msg = f"[{exception.error_code}] {error_msg}"
        
        if logger:
            logger.error(error_msg, extra=exception.details)
        
        return {
            'type': 'converter_error',
            'message': exception.message,
            'code': exception.error_code,
            'details': exception.details
        }
    else:
        error_msg = f"خطأ غير متوقع: {str(exception)}"
        if logger:
            logger.error(error_msg)
        
        return {
            'type': 'unexpected_error',
            'message': str(exception),
            'code': 'UNEXPECTED_ERROR',
            'details': {}
        }

# قاموس رسائل الخطأ المترجمة
ERROR_MESSAGES = {
    'ar': {
        'CONFIG_ERROR': 'خطأ في الإعدادات',
        'DEPENDENCY_ERROR': 'خطأ في التبعيات',
        'VALIDATION_ERROR': 'خطأ في التحقق من البيانات',
        'FILE_ERROR': 'خطأ في الملف',
        'PROCESS_ERROR': 'خطأ في العملية',
        'PLUGIN_ERROR': 'خطأ في الإضافة',
        'AI_SERVICE_ERROR': 'خطأ في خدمة الذكاء الاصطناعي',
        'SECURITY_ERROR': 'خطأ أمني'
    },
    'en': {
        'CONFIG_ERROR': 'Configuration Error',
        'DEPENDENCY_ERROR': 'Dependency Error',
        'VALIDATION_ERROR': 'Validation Error',
        'FILE_ERROR': 'File Error',
        'PROCESS_ERROR': 'Process Error',
        'PLUGIN_ERROR': 'Plugin Error',
        'AI_SERVICE_ERROR': 'AI Service Error',
        'SECURITY_ERROR': 'Security Error'
    }
}

def get_error_message(error_code, language='ar'):
    """الحصول على رسالة خطأ مترجمة"""
    return ERROR_MESSAGES.get(language, {}).get(error_code, error_code)
