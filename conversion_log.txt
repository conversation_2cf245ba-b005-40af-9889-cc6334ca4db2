[16:47:32] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0
[16:47:32] 📝 جاهز لبدء التحويل...
[16:47:32] 🔍 بدء فحص المتطلبات...
[16:47:32] ✅ PyInstaller مثبت - الإصدار: 6.14.2
[16:47:32] ✅ Python - الإصدار: 3.13.5
[16:47:32] ⚠️ لم يتم تحديد ملف مصدر
[16:47:32] ⚠️ لم يتم تحديد مجلد حفظ
[16:47:32] ✅ انتهى فحص المتطلبات
[16:50:29] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0
[16:50:29] 📝 جاهز لبدء التحويل...
[16:50:29] 🔍 بدء فحص المتطلبات...
[16:50:29] ✅ PyInstaller مثبت - الإصدار: 6.14.2
[16:50:29] ✅ Python - الإصدار: 3.11.0
[16:50:29] ⚠️ لم يتم تحديد ملف مصدر
[16:50:29] ⚠️ لم يتم تحديد مجلد حفظ
[16:50:29] ✅ انتهى فحص المتطلبات
[16:55:03] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0
[16:55:03] 📝 جاهز لبدء التحويل...
[16:55:03] 🔍 بدء فحص المتطلبات...
[16:55:03] ✅ PyInstaller مثبت - الإصدار: 6.14.2
[16:55:03] ✅ Python - الإصدار: 3.13.5
[16:55:03] ⚠️ لم يتم تحديد ملف مصدر
[16:55:03] ⚠️ لم يتم تحديد مجلد حفظ
[16:55:03] ✅ انتهى فحص المتطلبات
[16:55:21] 📁 تم اختيار المجلد: Python to EXE Converter Pro
[16:55:21] ⚠️ لم يتم العثور على ملف رئيسي، يرجى تحديد الملف يدوياً
[16:55:26] 💾 مجلد الحفظ: C:/Users/<USER>/Desktop/Python to EXE Converter Pro
[16:55:49] 🎨 تم اختيار الأيقونة: favicon.ico
[16:55:52] 🔍 بدء فحص المتطلبات...
[16:55:52] ✅ PyInstaller مثبت - الإصدار: 6.14.2
[16:55:52] ✅ Python - الإصدار: 3.13.5
[16:55:52] ✅ الملف المصدر موجود: Python to EXE Converter Pro
[16:55:52] ⚠️ تحذير: [Errno 13] Permission denied: 'C:/Users/<USER>/Desktop/Python to EXE Converter Pro'
[16:55:52] ✅ مجلد الحفظ موجود: C:/Users/<USER>/Desktop/Python to EXE Converter Pro
[16:55:52] 💾 المساحة المتاحة: 19 جيجابايت
[16:55:52] ✅ انتهى فحص المتطلبات
[16:55:54] 👁️ تم عرض معاينة الأمر
[16:55:58] 🚀 بدء عملية التحويل...
[16:55:58] 💻 الأمر: pyinstaller --onefile --distpath="C:/Users/<USER>/Desktop/Python to EXE Converter Pro" --icon="C:/Users/<USER>/Desktop/Python to EXE Converter Pro/favicon.ico" "C:/Users/<USER>/Desktop/Python to EXE Converter Pro"
[16:55:59] 📝 256 INFO: PyInstaller: 6.14.2, contrib hooks: 2025.5
[16:55:59] 📝 256 INFO: Python: 3.11.0
[16:55:59] 📝 263 INFO: Platform: Windows-10-10.0.26100-SP0
[16:55:59] 📝 263 INFO: Python environment: C:\Users\<USER>\AppData\Local\Programs\Python\Python311
[16:55:59] 📝 ERROR: Script file 'C:/Users/<USER>/Desktop/Python to EXE Converter Pro' does not exist.
[16:55:59] ❌ فشل التحويل - رمز الخطأ: 1
[17:15:19] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[17:15:19] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[17:15:19] 📝 جاهز لبدء التحويل...
[17:15:41] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[17:15:41] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[17:15:41] 📝 جاهز لبدء التحويل...
[17:15:49] 📄 تم اختيار الملف: pyinstaller_gui.py
[17:15:56] 🔍 بدء فحص المتطلبات...
[17:15:56] ✅ PyInstaller مثبت - الإصدار: 6.14.2
[17:15:56] ✅ Python - الإصدار: 3.11.0
[17:15:56] ✅ الملف المصدر موجود: pyinstaller_gui.py
[17:15:56] ✅ بناء الجملة صحيح
[17:15:56] ✅ مجلد الحفظ موجود: C:/Users/<USER>/Desktop/Python to EXE Converter Pro
[17:15:56] 💾 المساحة المتاحة: 19 جيجابايت
[17:15:56] ✅ انتهى فحص المتطلبات
[17:16:01] 👁️ تم عرض معاينة الأمر
[17:16:06] 🚀 بدء عملية التحويل...
[17:16:06] 💻 الأمر: pyinstaller --onefile --distpath="C:/Users/<USER>/Desktop/Python to EXE Converter Pro" "C:/Users/<USER>/Desktop/Python to EXE Converter Pro/pyinstaller_gui.py"
[17:16:06] 📝 141 INFO: PyInstaller: 6.14.2, contrib hooks: 2025.5
[17:16:06] 📝 141 INFO: Python: 3.11.0
[17:16:06] 📝 149 INFO: Platform: Windows-10-10.0.26100-SP0
[17:16:06] 📝 149 INFO: Python environment: C:\Users\<USER>\AppData\Local\Programs\Python\Python311
[17:16:06] 📝 149 INFO: wrote C:\Users\<USER>\Desktop\Python to EXE Converter Pro\pyinstaller_gui.spec
[17:16:06] 📝 153 INFO: Module search paths (PYTHONPATH):
[17:16:06] 📝 ['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\pyinstaller.exe',
[17:16:06] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.zip',
[17:16:06] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib',
[17:16:06] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs',
[17:16:06] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311',
[17:16:06] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages',
[17:16:06] 📝 'C:\\Users\\<USER>\\Desktop\\Python to EXE Converter Pro']
[17:16:06] 📝 441 INFO: checking Analysis
[17:16:06] 📝 441 INFO: Building Analysis because Analysis-00.toc is non existent
[17:16:06] 📝 441 INFO: Running Analysis Analysis-00.toc
[17:16:06] 📝 441 INFO: Target bytecode optimization level: 0
[17:16:06] 📝 441 INFO: Initializing module dependency graph...
[17:16:06] 📝 442 INFO: Initializing module graph hook caches...
[17:16:06] 📝 487 INFO: Analyzing modules for base_library.zip ...
[17:16:07] 📝 1159 INFO: Processing standard module hook 'hook-encodings.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:16:08] 📝 1582 INFO: Processing standard module hook 'hook-heapq.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:16:09] 📝 2835 INFO: Processing standard module hook 'hook-pickle.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:16:11] 📝 4801 INFO: Caching module dependency graph...
[17:16:11] 📝 4828 INFO: Looking for Python shared library...
[17:16:11] 📝 4833 INFO: Using Python shared library: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python311.dll
[17:16:11] 📝 4833 INFO: Analyzing C:\Users\<USER>\Desktop\Python to EXE Converter Pro\pyinstaller_gui.py
[17:16:11] 📝 4875 INFO: Processing pre-find-module-path hook 'hook-tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
[17:16:11] 📝 4876 INFO: TclTkInfo: initializing cached Tcl/Tk info...
[17:16:11] 📝 5054 INFO: Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:16:11] 📝 5262 INFO: Processing standard module hook 'hook-platform.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:16:11] 📝 5418 INFO: Processing standard module hook 'hook-sysconfig.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:16:12] 📝 5753 INFO: Processing pre-safe-import-module hook 'hook-importlib_metadata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
[17:16:12] 📝 5756 INFO: SetuptoolsInfo: initializing cached setuptools info...
[17:16:12] 📝 6170 INFO: Processing pre-safe-import-module hook 'hook-packaging.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
[17:16:12] 📝 6232 INFO: Processing standard module hook 'hook-win32ctypes.core.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:16:13] 📝 6611 INFO: Processing module hooks (post-graph stage)...
[17:16:13] 📝 6695 INFO: Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:16:13] 📝 6701 INFO: Performing binary vs. data reclassification (923 entries)
[17:16:16] 📝 10276 INFO: Looking for ctypes DLLs
[17:16:16] 📝 10297 INFO: Analyzing run-time hooks ...
[17:16:16] 📝 10298 INFO: Including run-time hook 'pyi_rth_inspect.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks'
[17:16:16] 📝 10307 INFO: Including run-time hook 'pyi_rth_pkgutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks'
[17:16:16] 📝 10315 INFO: Including run-time hook 'pyi_rth__tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks'
[17:16:16] 📝 10337 INFO: Creating base_library.zip...
[17:16:16] 📝 10370 INFO: Looking for dynamic libraries
[17:16:16] 📝 10497 INFO: Extra DLL search directories (AddDllDirectory): []
[17:16:16] 📝 10497 INFO: Extra DLL search directories (PATH): []
[17:16:17] 📝 10643 INFO: Warnings written to C:\Users\<USER>\Desktop\Python to EXE Converter Pro\build\pyinstaller_gui\warn-pyinstaller_gui.txt
[17:16:17] 📝 10667 INFO: Graph cross-reference written to C:\Users\<USER>\Desktop\Python to EXE Converter Pro\build\pyinstaller_gui\xref-pyinstaller_gui.html
[17:16:17] 📝 10721 INFO: checking PYZ
[17:16:17] 📝 10721 INFO: Building PYZ because PYZ-00.toc is non existent
[17:16:17] 📝 10721 INFO: Building PYZ (ZlibArchive) C:\Users\<USER>\Desktop\Python to EXE Converter Pro\build\pyinstaller_gui\PYZ-00.pyz
[17:16:17] 📝 10964 INFO: Building PYZ (ZlibArchive) C:\Users\<USER>\Desktop\Python to EXE Converter Pro\build\pyinstaller_gui\PYZ-00.pyz completed successfully.
[17:16:17] 📝 10980 INFO: checking PKG
[17:16:17] 📝 10980 INFO: Building PKG because PKG-00.toc is non existent
[17:16:17] 📝 10980 INFO: Building PKG (CArchive) pyinstaller_gui.pkg
[17:16:19] 📝 13051 INFO: Building PKG (CArchive) pyinstaller_gui.pkg completed successfully.
[17:16:19] 📝 13066 INFO: Bootloader C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\PyInstaller\bootloader\Windows-64bit-intel\run.exe
[17:16:19] 📝 13066 INFO: checking EXE
[17:16:19] 📝 13066 INFO: Building EXE because EXE-00.toc is non existent
[17:16:19] 📝 13066 INFO: Building EXE from EXE-00.toc
[17:16:19] 📝 13066 INFO: Copying bootloader EXE to C:\Users\<USER>\Desktop\Python to EXE Converter Pro\pyinstaller_gui.exe
[17:16:19] 📝 13131 INFO: Copying icon to EXE
[17:16:19] 📝 13142 INFO: Copying 0 resources to EXE
[17:16:19] 📝 13142 INFO: Embedding manifest in EXE
[17:16:19] 📝 13145 INFO: Appending PKG archive to EXE
[17:16:19] 📝 13154 INFO: Fixing EXE headers
[17:16:19] 📝 13221 INFO: Building EXE from EXE-00.toc completed successfully.
[17:16:19] 📝 13234 INFO: Build complete! The results are available in: C:\Users\<USER>\Desktop\Python to EXE Converter Pro
[17:16:19] ✅ تم التحويل بنجاح!
[17:16:19] 📁 تم العثور على 1 ملف محول
[17:16:54] 💾 مجلد الحفظ: C:/Users/<USER>/Desktop
[17:16:57] 🔍 بدء فحص المتطلبات...
[17:16:57] ✅ PyInstaller مثبت - الإصدار: 6.14.2
[17:16:57] ✅ Python - الإصدار: 3.11.0
[17:16:57] ✅ الملف المصدر موجود: pyinstaller_gui.py
[17:16:57] ✅ بناء الجملة صحيح
[17:16:57] ✅ مجلد الحفظ موجود: C:/Users/<USER>/Desktop
[17:16:57] 💾 المساحة المتاحة: 19 جيجابايت
[17:16:57] ✅ انتهى فحص المتطلبات
[17:16:58] 👁️ تم عرض معاينة الأمر
[17:17:08] 🚀 بدء عملية التحويل...
[17:17:08] 💻 الأمر: pyinstaller --onefile --distpath="C:/Users/<USER>/Desktop" "C:/Users/<USER>/Desktop/Python to EXE Converter Pro/pyinstaller_gui.py"
[17:17:08] 📝 144 INFO: PyInstaller: 6.14.2, contrib hooks: 2025.5
[17:17:08] 📝 144 INFO: Python: 3.11.0
[17:17:08] 📝 152 INFO: Platform: Windows-10-10.0.26100-SP0
[17:17:08] 📝 152 INFO: Python environment: C:\Users\<USER>\AppData\Local\Programs\Python\Python311
[17:17:08] 📝 152 INFO: wrote C:\Users\<USER>\Desktop\Python to EXE Converter Pro\pyinstaller_gui.spec
[17:17:08] 📝 156 INFO: Module search paths (PYTHONPATH):
[17:17:08] 📝 ['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\pyinstaller.exe',
[17:17:08] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.zip',
[17:17:08] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib',
[17:17:08] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs',
[17:17:08] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311',
[17:17:08] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages',
[17:17:08] 📝 'C:\\Users\\<USER>\\Desktop\\Python to EXE Converter Pro']
[17:17:08] 📝 405 INFO: checking Analysis
[17:17:08] 📝 405 INFO: Building Analysis because Analysis-00.toc is non existent
[17:17:08] 📝 405 INFO: Running Analysis Analysis-00.toc
[17:17:08] 📝 405 INFO: Target bytecode optimization level: 0
[17:17:08] 📝 405 INFO: Initializing module dependency graph...
[17:17:08] 📝 407 INFO: Initializing module graph hook caches...
[17:17:08] 📝 417 INFO: Analyzing modules for base_library.zip ...
[17:17:09] 📝 1181 INFO: Processing standard module hook 'hook-heapq.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:17:09] 📝 1234 INFO: Processing standard module hook 'hook-encodings.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:17:10] 📝 2142 INFO: Processing standard module hook 'hook-pickle.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:17:11] 📝 3013 INFO: Caching module dependency graph...
[17:17:11] 📝 3037 INFO: Looking for Python shared library...
[17:17:11] 📝 3040 INFO: Using Python shared library: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python311.dll
[17:17:11] 📝 3040 INFO: Analyzing C:\Users\<USER>\Desktop\Python to EXE Converter Pro\pyinstaller_gui.py
[17:17:11] 📝 3071 INFO: Processing pre-find-module-path hook 'hook-tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
[17:17:11] 📝 3072 INFO: TclTkInfo: initializing cached Tcl/Tk info...
[17:17:11] 📝 3226 INFO: Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:17:11] 📝 3339 INFO: Processing standard module hook 'hook-platform.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:17:11] 📝 3451 INFO: Processing standard module hook 'hook-sysconfig.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:17:11] 📝 3608 INFO: Processing pre-safe-import-module hook 'hook-importlib_metadata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
[17:17:11] 📝 3609 INFO: SetuptoolsInfo: initializing cached setuptools info...
[17:17:12] 📝 3911 INFO: Processing pre-safe-import-module hook 'hook-packaging.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
[17:17:12] 📝 3926 INFO: Processing standard module hook 'hook-win32ctypes.core.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:17:12] 📝 4264 INFO: Processing module hooks (post-graph stage)...
[17:17:12] 📝 4285 INFO: Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:17:12] 📝 4291 INFO: Performing binary vs. data reclassification (923 entries)
[17:17:12] 📝 4341 INFO: Looking for ctypes DLLs
[17:17:12] 📝 4357 INFO: Analyzing run-time hooks ...
[17:17:12] 📝 4357 INFO: Including run-time hook 'pyi_rth_inspect.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks'
[17:17:12] 📝 4359 INFO: Including run-time hook 'pyi_rth_pkgutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks'
[17:17:12] 📝 4360 INFO: Including run-time hook 'pyi_rth__tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks'
[17:17:12] 📝 4377 INFO: Creating base_library.zip...
[17:17:12] 📝 4419 INFO: Looking for dynamic libraries
[17:17:12] 📝 4546 INFO: Extra DLL search directories (AddDllDirectory): []
[17:17:12] 📝 4546 INFO: Extra DLL search directories (PATH): []
[17:17:12] 📝 4663 INFO: Warnings written to C:\Users\<USER>\Desktop\Python to EXE Converter Pro\build\pyinstaller_gui\warn-pyinstaller_gui.txt
[17:17:12] 📝 4683 INFO: Graph cross-reference written to C:\Users\<USER>\Desktop\Python to EXE Converter Pro\build\pyinstaller_gui\xref-pyinstaller_gui.html
[17:17:12] 📝 4716 INFO: checking PYZ
[17:17:12] 📝 4716 INFO: Building PYZ because PYZ-00.toc is non existent
[17:17:13] 📝 4716 INFO: Building PYZ (ZlibArchive) C:\Users\<USER>\Desktop\Python to EXE Converter Pro\build\pyinstaller_gui\PYZ-00.pyz
[17:17:13] 📝 4965 INFO: Building PYZ (ZlibArchive) C:\Users\<USER>\Desktop\Python to EXE Converter Pro\build\pyinstaller_gui\PYZ-00.pyz completed successfully.
[17:17:13] 📝 4980 INFO: checking PKG
[17:17:13] 📝 4980 INFO: Building PKG because PKG-00.toc is non existent
[17:17:13] 📝 4980 INFO: Building PKG (CArchive) pyinstaller_gui.pkg
[17:17:15] 📝 6982 INFO: Building PKG (CArchive) pyinstaller_gui.pkg completed successfully.
[17:17:15] 📝 6995 INFO: Bootloader C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\PyInstaller\bootloader\Windows-64bit-intel\run.exe
[17:17:15] 📝 6995 INFO: checking EXE
[17:17:15] 📝 6995 INFO: Building EXE because EXE-00.toc is non existent
[17:17:15] 📝 6995 INFO: Building EXE from EXE-00.toc
[17:17:15] 📝 6995 INFO: Copying bootloader EXE to C:\Users\<USER>\Desktop\pyinstaller_gui.exe
[17:17:15] 📝 6997 INFO: Copying icon to EXE
[17:17:15] 📝 6999 INFO: Copying 0 resources to EXE
[17:17:15] 📝 6999 INFO: Embedding manifest in EXE
[17:17:15] 📝 7002 INFO: Appending PKG archive to EXE
[17:17:15] 📝 7008 INFO: Fixing EXE headers
[17:17:15] 📝 7065 INFO: Building EXE from EXE-00.toc completed successfully.
[17:17:15] 📝 7077 INFO: Build complete! The results are available in: C:\Users\<USER>\Desktop
[17:17:15] ✅ تم التحويل بنجاح!
[17:17:15] 📁 تم العثور على 1 ملف محول
[17:17:24] 🔍 بدء فحص المتطلبات...
[17:17:24] ✅ PyInstaller مثبت - الإصدار: 6.14.2
[17:17:24] ✅ Python - الإصدار: 3.11.0
[17:17:24] ✅ الملف المصدر موجود: pyinstaller_gui.py
[17:17:24] ✅ بناء الجملة صحيح
[17:17:24] ✅ مجلد الحفظ موجود: C:/Users/<USER>/Desktop
[17:17:24] 💾 المساحة المتاحة: 18 جيجابايت
[17:17:24] ✅ انتهى فحص المتطلبات
[17:30:08] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[17:30:08] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[17:30:08] 📝 جاهز لبدء التحويل...
[17:36:09] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[17:36:09] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[17:36:09] 📝 جاهز لبدء التحويل...
[17:36:37] 🔍 بدء فحص المتطلبات...
[17:36:38] ✅ PyInstaller مثبت - الإصدار: 6.14.2
[17:36:38] ✅ Python - الإصدار: 3.11.0
[17:36:38] ⚠️ لم يتم تحديد ملف مصدر
[17:36:38] ✅ مجلد الحفظ موجود: C:/Users/<USER>/Desktop
[17:36:38] 💾 المساحة المتاحة: 17 جيجابايت
[17:36:38] ✅ انتهى فحص المتطلبات
[17:36:59] 📄 تم اختيار الملف: 4.py
[17:37:01] 🔍 بدء فحص المتطلبات...
[17:37:01] ✅ PyInstaller مثبت - الإصدار: 6.14.2
[17:37:01] ✅ Python - الإصدار: 3.11.0
[17:37:01] ✅ الملف المصدر موجود: 4.py
[17:37:01] ✅ بناء الجملة صحيح
[17:37:01] ✅ مجلد الحفظ موجود: C:/Users/<USER>/Desktop
[17:37:01] 💾 المساحة المتاحة: 17 جيجابايت
[17:37:01] ✅ انتهى فحص المتطلبات
[17:37:09] 🔍 بدء تحليل المشروع الذكي...
[17:37:09] 📁 تم العثور على 1 ملف Python
[17:37:09] 📋 تحليل requirements.txt
[17:37:09] 🔍 فحص حالة المكتبات...
[17:37:09] 📊 النتائج: 1 مثبتة، 1 مفقودة، 0 تحتاج تحديث
[17:37:09] 🔍 البحث عن البدائل...
[17:37:09] ✅ تم تحليل 1 ملف و 2 مكتبة
[17:37:27] 🔍 بدء فحص المتطلبات...
[17:37:27] ✅ PyInstaller مثبت - الإصدار: 6.14.2
[17:37:27] ✅ Python - الإصدار: 3.11.0
[17:37:27] ✅ الملف المصدر موجود: 4.py
[17:37:27] ✅ بناء الجملة صحيح
[17:37:27] ✅ مجلد الحفظ موجود: C:/Users/<USER>/Desktop
[17:37:27] 💾 المساحة المتاحة: 17 جيجابايت
[17:37:27] ✅ انتهى فحص المتطلبات
[17:37:28] 🔍 بدء فحص المتطلبات...
[17:37:28] ✅ PyInstaller مثبت - الإصدار: 6.14.2
[17:37:28] ✅ Python - الإصدار: 3.11.0
[17:37:28] ✅ الملف المصدر موجود: 4.py
[17:37:28] ✅ بناء الجملة صحيح
[17:37:28] ✅ مجلد الحفظ موجود: C:/Users/<USER>/Desktop
[17:37:28] 💾 المساحة المتاحة: 17 جيجابايت
[17:37:28] ✅ انتهى فحص المتطلبات
[17:37:34] 👁️ تم عرض معاينة الأمر
[17:37:36] 🔍 بدء فحص المتطلبات...
[17:37:36] ✅ PyInstaller مثبت - الإصدار: 6.14.2
[17:37:36] ✅ Python - الإصدار: 3.11.0
[17:37:36] ✅ الملف المصدر موجود: 4.py
[17:37:36] ✅ بناء الجملة صحيح
[17:37:36] ✅ مجلد الحفظ موجود: C:/Users/<USER>/Desktop
[17:37:36] 💾 المساحة المتاحة: 17 جيجابايت
[17:37:36] ✅ انتهى فحص المتطلبات
[17:37:51] 🚀 بدء عملية التحويل...
[17:37:51] 💻 الأمر: pyinstaller --onefile --distpath="C:/Users/<USER>/Desktop" "C:/Users/<USER>/Desktop/تحويل ملفا py  الى  exe/4.py"
[17:37:51] 📝 174 INFO: PyInstaller: 6.14.2, contrib hooks: 2025.5
[17:37:51] 📝 174 INFO: Python: 3.11.0
[17:37:51] 📝 174 INFO: Platform: Windows-10-10.0.26100-SP0
[17:37:51] 📝 174 INFO: Python environment: C:\Users\<USER>\AppData\Local\Programs\Python\Python311
[17:37:51] 📝 183 INFO: wrote C:\Users\<USER>\Desktop\Python to EXE Converter Pro\4.spec
[17:37:51] 📝 183 INFO: Module search paths (PYTHONPATH):
[17:37:51] 📝 ['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\pyinstaller.exe',
[17:37:51] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.zip',
[17:37:51] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib',
[17:37:51] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs',
[17:37:51] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311',
[17:37:51] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages',
[17:37:51] 📝 'C:\\Users\\<USER>\\Desktop\\\u062a\u062d\u0648\u064a\u0644 \u0645\u0644\u0641\u0627 py  \u0627\u0644\u0649  exe']
[17:37:51] 📝 423 INFO: checking Analysis
[17:37:51] 📝 423 INFO: Building Analysis because Analysis-00.toc is non existent
[17:37:51] 📝 423 INFO: Running Analysis Analysis-00.toc
[17:37:51] 📝 423 INFO: Target bytecode optimization level: 0
[17:37:51] 📝 423 INFO: Initializing module dependency graph...
[17:37:51] 📝 423 INFO: Initializing module graph hook caches...
[17:37:51] 📝 427 INFO: Analyzing modules for base_library.zip ...
[17:37:52] 📝 1083 INFO: Processing standard module hook 'hook-heapq.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:37:52] 📝 1103 INFO: Processing standard module hook 'hook-encodings.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:37:53] 📝 2142 INFO: Processing standard module hook 'hook-pickle.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:37:54] 📝 3002 INFO: Caching module dependency graph...
[17:37:54] 📝 3033 INFO: Looking for Python shared library...
[17:37:54] 📝 3037 INFO: Using Python shared library: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python311.dll
[17:37:54] 📝 3037 INFO: Analyzing C:\Users\<USER>\Desktop\\u062a\u062d\u0648\u064a\u0644 \u0645\u0644\u0641\u0627 py  \u0627\u0644\u0649  exe\4.py
[17:37:54] 📝 3043 INFO: Processing pre-find-module-path hook 'hook-tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
[17:37:54] 📝 3045 INFO: TclTkInfo: initializing cached Tcl/Tk info...
[17:37:54] 📝 3191 INFO: Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:37:54] 📝 3261 INFO: Processing standard module hook 'hook-platform.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:37:54] 📝 3365 INFO: Processing standard module hook 'hook-sysconfig.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:37:54] 📝 3524 INFO: Processing pre-safe-import-module hook 'hook-importlib_metadata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
[17:37:54] 📝 3524 INFO: SetuptoolsInfo: initializing cached setuptools info...
[17:37:55] 📝 3838 INFO: Processing pre-safe-import-module hook 'hook-packaging.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
[17:37:55] 📝 3854 INFO: Processing standard module hook 'hook-win32ctypes.core.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:37:55] 📝 4313 INFO: Processing standard module hook 'hook-xml.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:37:56] 📝 5136 INFO: Processing standard module hook 'hook-pkg_resources.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:37:57] 📝 5890 INFO: Processing standard module hook 'hook-PIL.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:37:57] 📝 6005 INFO: Processing standard module hook 'hook-PIL.Image.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:37:57] 📝 6282 INFO: Processing pre-safe-import-module hook 'hook-typing_extensions.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
[17:37:57] 📝 6499 INFO: Processing standard module hook 'hook-multiprocessing.util.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:37:58] 📝 7352 INFO: Processing standard module hook 'hook-xml.etree.cElementTree.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:37:58] 📝 7552 INFO: Processing standard module hook 'hook-PIL.ImageFilter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:37:59] 📝 7906 INFO: Processing standard module hook 'hook-xml.dom.domreg.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:37:59] 📝 8160 INFO: Processing pre-safe-import-module hook 'hook-gi.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
[17:37:59] 📝 8185 INFO: Processing module hooks (post-graph stage)...
[17:37:59] 📝 8627 INFO: Processing standard module hook 'hook-PIL.SpiderImagePlugin.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:38:00] 📝 8729 INFO: Processing pre-safe-import-module hook 'hook-win32com.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\pre_safe_import_module'
[17:38:01] 📝 9726 INFO: Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[17:38:01] 📝 9741 INFO: Performing binary vs. data reclassification (923 entries)
[17:38:01] 📝 9789 INFO: Looking for ctypes DLLs
[17:38:01] 📝 9836 INFO: Analyzing run-time hooks ...
[17:38:01] 📝 9836 INFO: Including run-time hook 'pyi_rth_inspect.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks'
[17:38:01] 📝 9852 INFO: Including run-time hook 'pyi_rth_pkgutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks'
[17:38:01] 📝 9852 INFO: Including run-time hook 'pyi_rth_pkgres.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks'
[17:38:01] 📝 9852 INFO: Including run-time hook 'pyi_rth_multiprocessing.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks'
[17:38:01] 📝 9868 INFO: Including run-time hook 'pyi_rth__tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks'
[17:38:01] 📝 9884 INFO: Creating base_library.zip...
[17:38:01] 📝 9931 INFO: Looking for dynamic libraries
[17:38:01] 📝 10231 INFO: Extra DLL search directories (AddDllDirectory): []
[17:38:01] 📝 10231 INFO: Extra DLL search directories (PATH): []
[17:38:01] 📝 10499 INFO: Warnings written to C:\Users\<USER>\Desktop\Python to EXE Converter Pro\build\4\warn-4.txt
[17:38:01] 📝 10537 INFO: Graph cross-reference written to C:\Users\<USER>\Desktop\Python to EXE Converter Pro\build\4\xref-4.html
[17:38:01] 📝 10572 INFO: checking PYZ
[17:38:01] 📝 10572 INFO: Building PYZ because PYZ-00.toc is non existent
[17:38:01] 📝 10572 INFO: Building PYZ (ZlibArchive) C:\Users\<USER>\Desktop\Python to EXE Converter Pro\build\4\PYZ-00.pyz
[17:38:02] 📝 11132 INFO: Building PYZ (ZlibArchive) C:\Users\<USER>\Desktop\Python to EXE Converter Pro\build\4\PYZ-00.pyz completed successfully.
[17:38:02] 📝 11157 INFO: checking PKG
[17:38:02] 📝 11157 INFO: Building PKG because PKG-00.toc is non existent
[17:38:02] 📝 11157 INFO: Building PKG (CArchive) 4.pkg
[17:38:05] 📝 13912 INFO: Building PKG (CArchive) 4.pkg completed successfully.
[17:38:05] 📝 13930 INFO: Bootloader C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\PyInstaller\bootloader\Windows-64bit-intel\run.exe
[17:38:05] 📝 13930 INFO: checking EXE
[17:38:05] 📝 13930 INFO: Building EXE because EXE-00.toc is non existent
[17:38:05] 📝 13930 INFO: Building EXE from EXE-00.toc
[17:38:05] 📝 13930 INFO: Copying bootloader EXE to C:\Users\<USER>\Desktop\4.exe
[17:38:05] 📝 13943 INFO: Copying icon to EXE
[17:38:05] 📝 13943 INFO: Copying 0 resources to EXE
[17:38:05] 📝 13943 INFO: Embedding manifest in EXE
[17:38:05] 📝 13943 INFO: Appending PKG archive to EXE
[17:38:05] 📝 13957 INFO: Fixing EXE headers
[17:38:05] 📝 14038 INFO: Building EXE from EXE-00.toc completed successfully.
[17:38:05] 📝 14056 INFO: Build complete! The results are available in: C:\Users\<USER>\Desktop
[17:38:05] ✅ تم التحويل بنجاح!
[17:38:05] 📁 تم العثور على 1 ملف محول
[17:52:04] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[17:52:04] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[17:52:04] 📝 جاهز لبدء التحويل...
[17:52:16] 📄 تم اختيار الملف: 4.py
[17:52:27] 🧠 بدء تحليل الكود...
[17:52:27] 📊 نوع التطبيق: GUI
[17:52:27] 🎯 المجال: general
[17:52:27] 🔑 الكلمات المفتاحية: option, browse_file, added, run, center
[17:52:27] ✨ تم توليد 5 اقتراح
[17:55:02] 🔍 بدء تحليل المشروع الذكي...
[17:55:02] 📁 تم العثور على 1 ملف Python
[17:55:02] 📋 تحليل requirements.txt
[17:55:02] 🔍 فحص حالة المكتبات...
[17:55:02] 📊 النتائج: 1 مثبتة، 1 مفقودة، 0 تحتاج تحديث
[17:55:02] 🔍 البحث عن البدائل...
[17:55:02] ✅ تم تحليل 1 ملف و 2 مكتبة
[17:55:05] 📦 بدء تثبيت 1 مكتبة...
[17:55:05] 📦 محاولة تثبيت Pillow...
[17:55:06] ✅ تم تثبيت Pillow بنجاح
[17:55:06] ✅ تم تثبيت 1/1 مكتبة بنجاح
[17:55:06] 🔍 فحص حالة المكتبات...
[17:55:06] 📊 النتائج: 1 مثبتة، 1 مفقودة، 0 تحتاج تحديث
[17:55:08] 🧪 اختبار تشغيل 4.py...
[17:55:09] ✅ تم تشغيل المشروع بنجاح!
[17:55:11] 📝 إنشاء ملف المتطلبات: C:/Users/<USER>/Desktop/تحويل ملفا py  الى  exe\requirements_generated.txt
[17:55:11] ✅ تم إنشاء ملف المتطلبات: C:/Users/<USER>/Desktop/تحويل ملفا py  الى  exe\requirements_generated.txt
[17:55:25] 🚀 بدء عملية التحويل...
[17:55:25] 💻 الأمر: pyinstaller --onefile --distpath="C:/Users/<USER>/Desktop" --icon="C:/Users/<USER>/Desktop/تحويل ملفا py  الى  exe\icon_desktop_app.ico" "C:/Users/<USER>/Desktop/تحويل ملفا py  الى  exe/4.py"
[17:55:25] 📝 134 INFO: PyInstaller: 6.14.2, contrib hooks: 2025.5
[17:55:25] 📝 134 INFO: Python: 3.11.0
[17:55:25] 📝 147 INFO: Platform: Windows-10-10.0.26100-SP0
[17:55:25] 📝 147 INFO: Python environment: C:\Users\<USER>\AppData\Local\Programs\Python\Python311
[17:55:25] 📝 147 INFO: wrote C:\Users\<USER>\Desktop\Python to EXE Converter Pro\4.spec
[17:55:25] 📝 147 INFO: Module search paths (PYTHONPATH):
[17:55:25] 📝 ['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\pyinstaller.exe',
[17:55:25] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.zip',
[17:55:25] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib',
[17:55:25] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs',
[17:55:25] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311',
[17:55:25] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages',
[17:55:25] 📝 'C:\\Users\\<USER>\\Desktop\\\u062a\u062d\u0648\u064a\u0644 \u0645\u0644\u0641\u0627 py  \u0627\u0644\u0649  exe']
[17:55:25] 📝 387 INFO: checking Analysis
[17:55:25] 📝 427 INFO: checking PYZ
[17:55:25] 📝 449 INFO: checking PKG
[17:55:25] 📝 467 INFO: Bootloader C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\PyInstaller\bootloader\Windows-64bit-intel\run.exe
[17:55:25] 📝 467 INFO: checking EXE
[17:55:25] 📝 467 INFO: Building because icon changed
[17:55:25] 📝 467 INFO: Building EXE from EXE-00.toc
[17:55:25] 📝 467 INFO: Copying bootloader EXE to C:\Users\<USER>\Desktop\4.exe
[17:55:26] 📝 480 INFO: Copying icon to EXE
[17:55:26] 📝 480 INFO: Copying 0 resources to EXE
[17:55:26] 📝 480 INFO: Embedding manifest in EXE
[17:55:26] 📝 480 INFO: Appending PKG archive to EXE
[17:55:26] 📝 494 INFO: Fixing EXE headers
[17:55:26] 📝 576 INFO: Building EXE from EXE-00.toc completed successfully.
[17:55:26] 📝 599 INFO: Build complete! The results are available in: C:\Users\<USER>\Desktop
[17:55:26] ✅ تم التحويل بنجاح!
[17:55:26] 📁 تم العثور على 1 ملف محول
[18:05:16] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[18:05:16] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[18:05:16] 📝 جاهز لبدء التحويل...
[18:25:48] 📄 تم اختيار الملف: 4.py
[18:25:58] 👁️ تم عرض معاينة الأمر
[18:26:05] 🔍 بدء تحليل المشروع الذكي...
[18:26:05] 📁 تم العثور على 1 ملف Python
[18:26:05] 📋 تحليل requirements.txt
[18:26:05] 🔍 فحص حالة المكتبات...
[18:26:05] 📊 النتائج: 1 مثبتة، 1 مفقودة، 0 تحتاج تحديث
[18:26:05] 🔍 البحث عن البدائل...
[18:26:05] ✅ تم تحليل 1 ملف و 2 مكتبة
[18:26:07] 📦 بدء تثبيت 1 مكتبة...
[18:26:07] 📦 محاولة تثبيت Pillow...
[18:26:07] ✅ تم تثبيت Pillow بنجاح
[18:26:07] ✅ تم تثبيت 1/1 مكتبة بنجاح
[18:26:07] 🔍 فحص حالة المكتبات...
[18:26:07] 📊 النتائج: 1 مثبتة، 1 مفقودة، 0 تحتاج تحديث
[18:26:08] 🧪 اختبار تشغيل 4.py...
[18:26:10] ✅ تم تشغيل المشروع بنجاح!
[18:26:17] 🧠 بدء تحليل الكود...
[18:26:17] 📊 نوع التطبيق: GUI
[18:26:17] 🎯 المجال: general
[18:26:17] 🔑 الكلمات المفتاحية: len, create_step1, executable, installation, flat
[18:26:17] ✨ تم توليد 5 اقتراح
[18:29:20] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[18:29:20] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[18:29:20] 📝 جاهز لبدء التحويل...
[18:29:25] 📄 تم اختيار الملف: 4.py
[18:29:33] 🧠 بدء تحليل الكود...
[18:29:33] 📊 نوع التطبيق: GUI
[18:29:33] 🎯 المجال: general
[18:29:33] 🔑 الكلمات المفتاحية: empty, else, pack, step, function
[18:29:33] ✨ تم توليد 5 اقتراح
[18:29:36] 🎨 بدء توليد الأيقونات العصرية...
[18:29:36] ✨ تم توليد 8 أيقونة عصرية
[18:30:45] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[18:30:45] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[18:30:45] 📝 جاهز لبدء التحويل...
[18:31:20] 📄 تم اختيار الملف: 4.py
[18:31:21] 🧠 بدء تحليل الكود...
[18:31:21] 📊 نوع التطبيق: GUI
[18:31:21] 🎯 المجال: general
[18:31:21] 🔑 الكلمات المفتاحية: progressbar, activeforeground, onefile, destroy, effect
[18:31:21] ✨ تم توليد 5 اقتراح
[18:31:23] 🎨 بدء توليد الأيقونات العصرية...
[18:31:24] ✨ تم توليد 8 أيقونة عصرية
[18:56:56] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[18:56:56] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[18:56:56] 📝 جاهز لبدء التحويل...
[18:58:22] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[18:58:22] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[18:58:22] 📝 جاهز لبدء التحويل...
[18:58:46] 📄 تم اختيار الملف: gui_app.py
[18:58:49] 🧠 بدء تحليل الكود...
[18:58:49] 📊 نوع التطبيق: GUI
[18:58:49] 🎯 المجال: social
[18:58:49] 🔑 الكلمات المفتاحية: codes, len, progress, indent, windows
[18:58:49] ✨ تم توليد 5 اقتراح
[19:01:52] 🎨 بدء توليد الصور بالذكاء الاصطناعي...
[19:01:52] 🖼️ توليد صورة 1/6: minimalist flat design
[19:01:52] 🖼️ توليد صورة 2/6: modern 3D rendered
[19:01:52] 🖼️ توليد صورة 3/6: professional corporate style
[19:01:52] 🖼️ توليد صورة 4/6: vibrant colorful design
[19:01:52] 🖼️ توليد صورة 5/6: elegant sophisticated
[19:01:52] 🖼️ توليد صورة 6/6: playful cartoon style
[19:01:52] ✅ تم توليد 6 صورة بنجاح
[19:01:52] 🎨 بدء توليد الأيقونات العصرية...
[19:01:52] ✨ تم توليد 4 أيقونة عصرية
[19:17:29] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[19:17:29] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[19:17:29] 📝 جاهز لبدء التحويل...
[19:20:22] 📄 تم اختيار الملف: gui_app.py
[19:20:29] 🔍 بدء فحص المتطلبات...
[19:20:29] ✅ PyInstaller مثبت - الإصدار: 6.14.2
[19:20:29] ✅ Python - الإصدار: 3.11.0
[19:20:29] ✅ الملف المصدر موجود: gui_app.py
[19:20:29] ✅ بناء الجملة صحيح
[19:20:29] ✅ مجلد الحفظ موجود: C:/Users/<USER>/Desktop
[19:20:29] 💾 المساحة المتاحة: 16 جيجابايت
[19:20:29] ✅ انتهى فحص المتطلبات
[19:20:35] 👁️ تم عرض معاينة الأمر
[19:20:39] 🔍 بدء تحليل المشروع الذكي...
[19:20:39] 📁 تم العثور على 1 ملف Python
[19:20:39] 🔍 فحص حالة المكتبات...
[19:20:39] 📊 النتائج: 3 مثبتة، 4 مفقودة، 0 تحتاج تحديث
[19:20:39] 🔍 البحث عن البدائل...
[19:20:39] ✅ تم تحليل 1 ملف و 7 مكتبة
[19:20:41] 📦 بدء تثبيت 4 مكتبة...
[19:20:41] 📦 محاولة تثبيت customtkinter...
[19:20:44] ✅ تم تثبيت customtkinter بنجاح
[19:20:44] 📦 محاولة تثبيت CTkMessagebox...
[19:20:45] ✅ تم تثبيت CTkMessagebox بنجاح
[19:20:45] 📦 محاولة تثبيت dateutil...
[19:20:46] ❌ فشل تثبيت dateutil: ERROR: Could not find a version that satisfies the requirement dateutil (from versions: none)
ERROR: No matching distribution found for dateutil

[19:20:46] 📦 محاولة تثبيت rich...
[19:20:48] ✅ تم تثبيت rich بنجاح
[19:20:48] ✅ تم تثبيت 3/4 مكتبة بنجاح
[19:20:48] 🔍 فحص حالة المكتبات...
[19:20:48] 📊 النتائج: 6 مثبتة، 1 مفقودة، 0 تحتاج تحديث
[19:20:52] 🧪 اختبار تشغيل gui_app.py...
[19:20:52] ❌ فشل تشغيل المشروع: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\New folder (2)\iptv-checker-main\iptv-checker-main\gui_app.py", line 16, in <module>
    from dateutil.parser import parse as parse_datetime
ModuleNotFoundError: No module named 'dateutil'

[19:20:52] 🔍 تحليل الأخطاء...
[19:20:52] 🔧 محاولة إصلاح المكتبة المفقودة: dateutil
[19:20:52] 📦 محاولة تثبيت dateutil...
[19:20:53] ❌ فشل تثبيت dateutil: ERROR: Could not find a version that satisfies the requirement dateutil (from versions: none)
ERROR: No matching distribution found for dateutil

[19:20:59] 📝 إنشاء ملف المتطلبات: C:/Users/<USER>/Desktop/New folder (2)/iptv-checker-main/iptv-checker-main\requirements_generated.txt
[19:20:59] ✅ تم إنشاء ملف المتطلبات: C:/Users/<USER>/Desktop/New folder (2)/iptv-checker-main/iptv-checker-main\requirements_generated.txt
[19:21:02] 🧪 اختبار تشغيل gui_app.py...
[19:21:02] ❌ فشل تشغيل المشروع: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\New folder (2)\iptv-checker-main\iptv-checker-main\gui_app.py", line 16, in <module>
    from dateutil.parser import parse as parse_datetime
ModuleNotFoundError: No module named 'dateutil'

[19:21:02] 🔍 تحليل الأخطاء...
[19:21:02] 🔧 محاولة إصلاح المكتبة المفقودة: dateutil
[19:21:02] 📦 محاولة تثبيت dateutil...
[19:21:03] ❌ فشل تثبيت dateutil: ERROR: Could not find a version that satisfies the requirement dateutil (from versions: none)
ERROR: No matching distribution found for dateutil

[19:21:05] 📦 بدء تثبيت 1 مكتبة...
[19:21:05] 📦 محاولة تثبيت dateutil...
[19:21:06] ❌ فشل تثبيت dateutil: ERROR: Could not find a version that satisfies the requirement dateutil (from versions: none)
ERROR: No matching distribution found for dateutil

[19:21:06] ✅ تم تثبيت 0/1 مكتبة بنجاح
[19:21:06] 🔍 فحص حالة المكتبات...
[19:21:06] 📊 النتائج: 6 مثبتة، 1 مفقودة، 0 تحتاج تحديث
[19:21:23] 🧪 اختبار تشغيل gui_app.py...
[19:21:23] ❌ فشل تشغيل المشروع: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\New folder (2)\iptv-checker-main\iptv-checker-main\gui_app.py", line 16, in <module>
    from dateutil.parser import parse as parse_datetime
ModuleNotFoundError: No module named 'dateutil'

[19:21:23] 🔍 تحليل الأخطاء...
[19:21:23] 🔧 محاولة إصلاح المكتبة المفقودة: dateutil
[19:21:23] 📦 محاولة تثبيت dateutil...
[19:21:23] ❌ فشل تثبيت dateutil: ERROR: Could not find a version that satisfies the requirement dateutil (from versions: none)
ERROR: No matching distribution found for dateutil

[19:21:33] 🧠 بدء تحليل الكود...
[19:21:33] 📊 نوع التطبيق: GUI
[19:21:33] 🎯 المجال: social
[19:21:33] 🔑 الكلمات المفتاحية: parse_m3u_simplified, width, php, red, ctkframe
[19:21:33] ✨ تم توليد 5 اقتراح
[19:21:35] 🎨 بدء توليد الصور بالذكاء الاصطناعي...
[19:21:37] 🎨 بدء توليد الصور بالذكاء الاصطناعي...
[19:21:37] 🎨 بدء توليد الصور بالذكاء الاصطناعي...
[19:56:06] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[19:56:06] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[19:56:06] 📝 جاهز لبدء التحويل...
[19:58:13] 📄 تم اختيار الملف: gui_app.py
[20:03:05] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[20:03:05] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[20:03:05] 📝 جاهز لبدء التحويل...
[20:03:31] 📄 تم اختيار الملف: gui_app.py
[20:03:37] 🔍 بدء تحليل المشروع الذكي...
[20:03:37] 📁 تم العثور على 1 ملف Python
[20:03:37] 🔍 فحص حالة المكتبات...
[20:03:37] 📊 النتائج: 6 مثبتة، 1 مفقودة، 0 تحتاج تحديث
[20:03:37] 🔍 البحث عن البدائل...
[20:03:37] ✅ تم تحليل 1 ملف و 7 مكتبة
[20:03:38] 📦 بدء تثبيت 1 مكتبة...
[20:03:38] 📦 محاولة تثبيت dateutil...
[20:03:39] ❌ فشل تثبيت dateutil: ERROR: Could not find a version that satisfies the requirement dateutil (from versions: none)
ERROR: No matching distribution found for dateutil

[20:03:39] ✅ تم تثبيت 0/1 مكتبة بنجاح
[20:03:39] 🔍 فحص حالة المكتبات...
[20:03:39] 📊 النتائج: 6 مثبتة، 1 مفقودة، 0 تحتاج تحديث
[20:03:46] 🧪 اختبار تشغيل gui_app.py...
[20:03:46] ❌ فشل تشغيل المشروع: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\New folder (2)\iptv-checker-main\iptv-checker-main\gui_app.py", line 16, in <module>
    from dateutil.parser import parse as parse_datetime
ModuleNotFoundError: No module named 'dateutil'

[20:03:46] 🔍 تحليل الأخطاء...
[20:03:46] 🔧 محاولة إصلاح المكتبة المفقودة: dateutil
[20:03:46] 📦 محاولة تثبيت dateutil...
[20:03:48] ❌ فشل تثبيت dateutil: ERROR: Could not find a version that satisfies the requirement dateutil (from versions: none)
ERROR: No matching distribution found for dateutil

[20:04:06] 🧠 بدء تحليل الكود...
[20:04:06] 📊 نوع التطبيق: GUI
[20:04:06] 🎯 المجال: social
[20:04:06] 🔑 الكلمات المفتاحية: logger, insert, true, gzip, append
[20:04:06] ✨ تم توليد 5 اقتراح
[20:04:07] 🧠 بدء تحليل الكود...
[20:04:07] 📊 نوع التطبيق: GUI
[20:04:07] 🎯 المجال: social
[20:04:07] 🔑 الكلمات المفتاحية: logger, insert, true, gzip, append
[20:04:07] ✨ تم توليد 5 اقتراح
[20:04:08] 🎨 بدء توليد الصور بالذكاء الاصطناعي...
[20:29:36] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[20:29:36] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[20:29:36] 📝 جاهز لبدء التحويل...
[20:29:46] 📄 تم اختيار الملف: gui_app.py
[20:29:50] 🧠 بدء تحليل الكود...
[20:29:50] 📊 نوع التطبيق: GUI
[20:29:50] 🎯 المجال: social
[20:29:50] 🔑 الكلمات المفتاحية: php, threading, check_status_completion, channelinfo, select_channel
[20:29:50] ✨ تم توليد 5 اقتراح
[20:29:50] 🧠 بدء تحليل الكود...
[20:29:50] 📊 نوع التطبيق: GUI
[20:29:50] 🎯 المجال: social
[20:29:50] 🔑 الكلمات المفتاحية: php, threading, check_status_completion, channelinfo, select_channel
[20:29:50] ✨ تم توليد 5 اقتراح
[20:29:51] 🧠 بدء تحليل الكود...
[20:29:51] 📊 نوع التطبيق: GUI
[20:29:51] 🎯 المجال: social
[20:29:51] 🔑 الكلمات المفتاحية: php, threading, check_status_completion, channelinfo, select_channel
[20:29:51] ✨ تم توليد 5 اقتراح
[20:29:51] 🧠 بدء تحليل الكود...
[20:29:51] 📊 نوع التطبيق: GUI
[20:29:51] 🎯 المجال: social
[20:29:51] 🔑 الكلمات المفتاحية: php, threading, check_status_completion, channelinfo, select_channel
[20:29:51] ✨ تم توليد 5 اقتراح
[20:29:52] 🎨 بدء توليد الصور بالذكاء الاصطناعي...
[20:34:58] 💾 مجلد الحفظ: C:/Users/<USER>/Desktop
[20:35:00] 👁️ تم عرض معاينة الأمر
[20:35:11] 🔍 بدء تحليل المشروع الذكي...
[20:35:11] 📁 تم العثور على 1 ملف Python
[20:35:11] 🔍 فحص حالة المكتبات...
[20:35:11] 📊 النتائج: 7 مثبتة، 0 مفقودة، 0 تحتاج تحديث
[20:35:11] 🔍 البحث عن البدائل...
[20:35:11] ✅ تم تحليل 1 ملف و 7 مكتبة
[20:35:16] 🧪 اختبار تشغيل gui_app.py...
[20:35:31] ✅ تم تشغيل المشروع بنجاح!
[20:35:32] 📝 إنشاء ملف المتطلبات: C:/Users/<USER>/Desktop/New folder (2)/iptv-checker-main/iptv-checker-main\requirements_generated.txt
[20:35:32] ✅ تم إنشاء ملف المتطلبات: C:/Users/<USER>/Desktop/New folder (2)/iptv-checker-main/iptv-checker-main\requirements_generated.txt
[20:35:43] 🚀 بدء عملية التحويل...
[20:35:43] 💻 الأمر: pyinstaller --onefile --distpath="C:/Users/<USER>/Desktop" "C:/Users/<USER>/Desktop/New folder (2)/iptv-checker-main/iptv-checker-main/gui_app.py"
[20:35:43] 📝 217 INFO: PyInstaller: 6.14.2, contrib hooks: 2025.5
[20:35:43] 📝 217 INFO: Python: 3.11.0
[20:35:43] 📝 224 INFO: Platform: Windows-10-10.0.26100-SP0
[20:35:43] 📝 224 INFO: Python environment: C:\Users\<USER>\AppData\Local\Programs\Python\Python311
[20:35:43] 📝 225 INFO: wrote C:\Users\<USER>\Desktop\Python to EXE Converter Pro2\gui_app.spec
[20:35:43] 📝 228 INFO: Module search paths (PYTHONPATH):
[20:35:43] 📝 ['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Scripts\\pyinstaller.exe',
[20:35:43] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.zip',
[20:35:43] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib',
[20:35:43] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs',
[20:35:43] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311',
[20:35:43] 📝 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages',
[20:35:43] 📝 'C:\\Users\\<USER>\\Desktop\\New folder '
[20:35:43] 📝 '(2)\\iptv-checker-main\\iptv-checker-main']
[20:35:44] 📝 476 INFO: checking Analysis
[20:35:44] 📝 476 INFO: Building Analysis because Analysis-00.toc is non existent
[20:35:44] 📝 476 INFO: Running Analysis Analysis-00.toc
[20:35:44] 📝 476 INFO: Target bytecode optimization level: 0
[20:35:44] 📝 476 INFO: Initializing module dependency graph...
[20:35:44] 📝 477 INFO: Initializing module graph hook caches...
[20:35:44] 📝 511 INFO: Analyzing modules for base_library.zip ...
[20:35:44] 📝 1348 INFO: Processing standard module hook 'hook-heapq.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[20:35:44] 📝 1409 INFO: Processing standard module hook 'hook-encodings.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[20:35:46] 📝 2492 INFO: Processing standard module hook 'hook-pickle.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[20:35:46] 📝 3384 INFO: Caching module dependency graph...
[20:35:46] 📝 3418 INFO: Looking for Python shared library...
[20:35:46] 📝 3423 INFO: Using Python shared library: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python311.dll
[20:35:47] 📝 3423 INFO: Analyzing C:\Users\<USER>\Desktop\New folder (2)\iptv-checker-main\iptv-checker-main\gui_app.py
[20:35:47] 📝 3454 INFO: Processing standard module hook 'hook-customtkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
[20:35:47] 📝 3487 INFO: Processing pre-find-module-path hook 'hook-tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
[20:35:47] 📝 3488 INFO: TclTkInfo: initializing cached Tcl/Tk info...
[20:35:47] 📝 3674 INFO: Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[20:35:47] 📝 3734 INFO: Processing standard module hook 'hook-platform.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[20:35:47] 📝 3749 INFO: Processing pre-safe-import-module hook 'hook-packaging.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
[20:35:47] 📝 3751 INFO: SetuptoolsInfo: initializing cached setuptools info...
[20:35:47] 📝 4300 INFO: Processing pre-safe-import-module hook 'hook-typing_extensions.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
[20:35:48] 📝 4456 INFO: Processing standard module hook 'hook-multiprocessing.util.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[20:35:48] 📝 4576 INFO: Processing standard module hook 'hook-xml.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[20:35:48] 📝 5051 INFO: Processing standard module hook 'hook-PIL.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[20:35:48] 📝 5120 INFO: Processing standard module hook 'hook-PIL.Image.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[20:35:48] 📝 5409 INFO: Processing standard module hook 'hook-xml.etree.cElementTree.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[20:35:49] 📝 5541 INFO: Processing standard module hook 'hook-PIL.ImageFilter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[20:35:49] 📝 5877 INFO: Processing standard module hook 'hook-CTkMessagebox.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
[20:35:49] 📝 5907 INFO: Processing standard module hook 'hook-urllib3.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
[20:35:50] 📝 6770 INFO: Processing standard module hook 'hook-charset_normalizer.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
[20:35:50] 📝 6951 INFO: Processing standard module hook 'hook-certifi.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
[20:35:50] 📝 7455 INFO: Processing standard module hook 'hook-pygments.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[20:35:52] 📝 8962 INFO: Processing standard module hook 'hook-sysconfig.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[20:35:52] 📝 9135 INFO: Processing module hooks (post-graph stage)...
[20:35:52] 📝 9333 INFO: Processing standard module hook 'hook-PIL.SpiderImagePlugin.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[20:35:56] 📝 13159 INFO: Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks'
[20:35:56] 📝 13180 INFO: Performing binary vs. data reclassification (939 entries)
[20:35:56] 📝 13406 INFO: Looking for ctypes DLLs
[20:35:56] 📝 13417 WARNING: Ignoring AppKit.framework/AppKit imported from C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\darkdetect\_mac_detect.py - only basenames are supported with ctypes imports!
[20:35:57] 📝 13438 INFO: Analyzing run-time hooks ...
[20:35:57] 📝 13442 INFO: Including run-time hook 'pyi_rth_inspect.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks'
[20:35:57] 📝 13445 INFO: Including run-time hook 'pyi_rth_pkgutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks'
[20:35:57] 📝 13447 INFO: Including run-time hook 'pyi_rth_multiprocessing.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks'
[20:35:57] 📝 13448 INFO: Including run-time hook 'pyi_rth__tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks'
[20:35:57] 📝 13475 INFO: Creating base_library.zip...
[20:35:57] 📝 13511 INFO: Looking for dynamic libraries
[20:35:57] 📝 13867 INFO: Extra DLL search directories (AddDllDirectory): []
[20:35:57] 📝 13867 INFO: Extra DLL search directories (PATH): []
[20:35:57] 📝 14100 INFO: Warnings written to C:\Users\<USER>\Desktop\Python to EXE Converter Pro2\build\gui_app\warn-gui_app.txt
[20:35:57] 📝 14152 INFO: Graph cross-reference written to C:\Users\<USER>\Desktop\Python to EXE Converter Pro2\build\gui_app\xref-gui_app.html
[20:35:57] 📝 14191 INFO: checking PYZ
[20:35:57] 📝 14191 INFO: Building PYZ because PYZ-00.toc is non existent
[20:35:57] 📝 14191 INFO: Building PYZ (ZlibArchive) C:\Users\<USER>\Desktop\Python to EXE Converter Pro2\build\gui_app\PYZ-00.pyz
[20:35:58] 📝 14896 INFO: Building PYZ (ZlibArchive) C:\Users\<USER>\Desktop\Python to EXE Converter Pro2\build\gui_app\PYZ-00.pyz completed successfully.
[20:35:58] 📝 14928 INFO: checking PKG
[20:35:58] 📝 14928 INFO: Building PKG because PKG-00.toc is non existent
[20:35:58] 📝 14928 INFO: Building PKG (CArchive) gui_app.pkg
[20:36:01] 📝 17981 INFO: Building PKG (CArchive) gui_app.pkg completed successfully.
[20:36:01] 📝 17995 INFO: Bootloader C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\PyInstaller\bootloader\Windows-64bit-intel\run.exe
[20:36:01] 📝 17995 INFO: checking EXE
[20:36:01] 📝 17995 INFO: Building EXE because EXE-00.toc is non existent
[20:36:01] 📝 17995 INFO: Building EXE from EXE-00.toc
[20:36:01] 📝 17995 INFO: Copying bootloader EXE to C:\Users\<USER>\Desktop\gui_app.exe
[20:36:01] 📝 18000 INFO: Copying icon to EXE
[20:36:01] 📝 18003 INFO: Copying 0 resources to EXE
[20:36:01] 📝 18003 INFO: Embedding manifest in EXE
[20:36:01] 📝 18005 INFO: Appending PKG archive to EXE
[20:36:01] 📝 18020 INFO: Fixing EXE headers
[20:36:01] 📝 18123 INFO: Building EXE from EXE-00.toc completed successfully.
[20:36:01] 📝 18137 INFO: Build complete! The results are available in: C:\Users\<USER>\Desktop
[20:36:01] ✅ تم التحويل بنجاح!
[20:36:01] 📁 تم العثور على 1 ملف محول
[20:38:43] 📁 تم اختيار المجلد: iptv-checker-main
[20:38:43] ⚠️ لم يتم العثور على ملف رئيسي، يرجى تحديد الملف يدوياً
[20:38:47] 🔍 بدء تحليل المشروع الذكي...
[20:38:47] 📁 تم العثور على 6 ملف Python
[20:38:47] 🔍 فحص حالة المكتبات...
[20:38:47] 📊 النتائج: 8 مثبتة، 0 مفقودة، 0 تحتاج تحديث
[20:38:47] 🔍 البحث عن البدائل...
[20:38:47] ✅ تم تحليل 6 ملف و 8 مكتبة
[20:38:49] 🧪 اختبار تشغيل iptv-checker-main...
[20:38:49] ❌ فشل تشغيل المشروع: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe: can't find '__main__' module in 'C:\\Users\\<USER>\\Desktop\\New folder (2)\\iptv-checker-main'

[20:38:49] 🔍 تحليل الأخطاء...
[20:38:56] 🧪 اختبار تشغيل iptv-checker-main...
[20:38:56] ❌ فشل تشغيل المشروع: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe: can't find '__main__' module in 'C:\\Users\\<USER>\\Desktop\\New folder (2)\\iptv-checker-main'

[20:38:56] 🔍 تحليل الأخطاء...
[20:38:58] 📝 إنشاء ملف المتطلبات: C:/Users/<USER>/Desktop/New folder (2)/iptv-checker-main\requirements_generated.txt
[20:38:58] ✅ تم إنشاء ملف المتطلبات: C:/Users/<USER>/Desktop/New folder (2)/iptv-checker-main\requirements_generated.txt
[20:39:02] 🧪 اختبار تشغيل iptv-checker-main...
[20:39:02] ❌ فشل تشغيل المشروع: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe: can't find '__main__' module in 'C:\\Users\\<USER>\\Desktop\\New folder (2)\\iptv-checker-main'

[20:39:02] 🔍 تحليل الأخطاء...
[20:39:11] 🧪 اختبار تشغيل iptv-checker-main...
[20:39:11] ❌ فشل تشغيل المشروع: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe: can't find '__main__' module in 'C:\\Users\\<USER>\\Desktop\\New folder (2)\\iptv-checker-main'

[20:39:11] 🔍 تحليل الأخطاء...
[20:39:16] 🚀 بدء عملية التحويل...
[20:39:16] 💻 الأمر: pyinstaller --onefile --distpath="C:/Users/<USER>/Desktop" "C:/Users/<USER>/Desktop/New folder (2)/iptv-checker-main"
[20:39:16] 📝 138 INFO: PyInstaller: 6.14.2, contrib hooks: 2025.5
[20:39:16] 📝 138 INFO: Python: 3.11.0
[20:39:16] 📝 146 INFO: Platform: Windows-10-10.0.26100-SP0
[20:39:16] 📝 146 INFO: Python environment: C:\Users\<USER>\AppData\Local\Programs\Python\Python311
[20:39:16] 📝 ERROR: Script file 'C:/Users/<USER>/Desktop/New folder (2)/iptv-checker-main' does not exist.
[20:39:16] ❌ فشل التحويل - رمز الخطأ: 1
[04:00:57] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[04:00:57] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[04:00:57] 📝 جاهز لبدء التحويل...
[04:01:18] 📁 تم اختيار المجلد: Python to EXE Converter Pro3
[04:01:18] ⚠️ لم يتم العثور على ملف رئيسي، يرجى تحديد الملف يدوياً
[04:01:21] 👁️ تم عرض معاينة الأمر
[04:01:25] 🔍 بدء فحص المتطلبات...
[04:01:25] ✅ PyInstaller مثبت - الإصدار: 6.14.2
[04:01:25] ✅ Python - الإصدار: 3.11.0
[04:01:25] ✅ الملف المصدر موجود: Python to EXE Converter Pro3
[04:01:25] ⚠️ تحذير: [Errno 13] Permission denied: 'C:/Users/<USER>/Desktop/Python to EXE Converter Pro3'
[04:01:25] ✅ مجلد الحفظ موجود: C:/Users/<USER>/Desktop
[04:01:25] 💾 المساحة المتاحة: 12 جيجابايت
[04:01:25] ✅ انتهى فحص المتطلبات
[04:01:30] 🔍 بدء تحليل المشروع الذكي...
[04:01:30] 📁 تم العثور على 17 ملف Python
[04:01:31] 📋 تحليل requirements.txt
[04:01:31] 🔍 فحص حالة المكتبات...
[04:01:31] 📊 النتائج: 22 مثبتة، 6 مفقودة، 0 تحتاج تحديث
[04:01:31] 🔍 البحث عن البدائل...
[04:01:31] ✅ تم تحليل 17 ملف و 28 مكتبة
[04:01:34] 📦 بدء تثبيت 6 مكتبة...
[04:01:34] 📦 محاولة تثبيت numpy...
[04:01:44] 🧪 اختبار تشغيل Python to EXE Converter Pro3...
[04:01:45] ❌ فشل تشغيل المشروع: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe: can't find '__main__' module in 'C:\\Users\\<USER>\\Desktop\\Python to EXE Converter Pro3'

[04:01:45] 🔍 تحليل الأخطاء...
[04:01:52] ✅ تم تثبيت numpy بنجاح
[04:01:52] 📦 محاولة تثبيت rembg...
[04:02:01] 📝 إنشاء ملف المتطلبات: C:/Users/<USER>/Desktop/Python to EXE Converter Pro3\requirements_generated.txt
[04:02:01] ✅ تم إنشاء ملف المتطلبات: C:/Users/<USER>/Desktop/Python to EXE Converter Pro3\requirements_generated.txt
[04:02:03] 🧪 اختبار تشغيل Python to EXE Converter Pro3...
[04:02:03] ❌ فشل تشغيل المشروع: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe: can't find '__main__' module in 'C:\\Users\\<USER>\\Desktop\\Python to EXE Converter Pro3'

[04:02:03] 🔍 تحليل الأخطاء...
[04:02:16] 🧠 بدء تحليل الكود...
[04:02:16] 📊 نوع التطبيق: File
[04:02:16] 🎯 المجال: media
[04:02:16] 🔑 الكلمات المفتاحية: _find_main_file, Tool, lxml, frame, pyinstaller
[04:02:16] ✨ تم توليد 3 اقتراح
[04:02:17] 🧠 بدء تحليل الكود...
[04:02:18] 📊 نوع التطبيق: File
[04:02:18] 🎯 المجال: media
[04:02:18] 🔑 الكلمات المفتاحية: _find_main_file, Tool, lxml, frame, pyinstaller
[04:02:18] ✨ تم توليد 3 اقتراح
[04:02:18] 🧠 بدء تحليل الكود...
[04:02:18] 📊 نوع التطبيق: File
[04:02:18] 🎯 المجال: media
[04:02:18] 🔑 الكلمات المفتاحية: _find_main_file, Tool, lxml, frame, pyinstaller
[04:02:18] ✨ تم توليد 3 اقتراح
[04:02:19] 🧠 بدء تحليل الكود...
[04:02:19] 📊 نوع التطبيق: File
[04:02:19] 🎯 المجال: media
[04:02:19] 🔑 الكلمات المفتاحية: _find_main_file, Tool, lxml, frame, pyinstaller
[04:02:19] ✨ تم توليد 3 اقتراح
[04:05:38] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[04:05:38] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[04:05:38] 📝 جاهز لبدء التحويل...
[04:08:11] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[04:08:11] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[04:08:11] 📝 جاهز لبدء التحويل...
[04:10:14] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[04:10:14] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[04:10:14] 📝 جاهز لبدء التحويل...
[04:10:29] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[04:10:29] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[04:10:29] 📝 جاهز لبدء التحويل...
[04:10:33] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[04:10:33] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[04:10:33] 📝 جاهز لبدء التحويل...
[04:10:35] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[04:10:35] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[04:10:35] 📝 جاهز لبدء التحويل...
[04:17:50] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[04:17:50] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[04:17:50] 📝 جاهز لبدء التحويل...
[04:47:27] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[04:47:27] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[04:47:27] 📝 جاهز لبدء التحويل...
[04:53:39] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[04:53:39] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[04:53:39] 📝 جاهز لبدء التحويل...
[05:00:25] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[05:00:25] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[05:00:25] 📝 جاهز لبدء التحويل...
[05:05:48] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[05:05:48] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[05:05:49] 📝 جاهز لبدء التحويل...
[05:13:32] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[05:13:32] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[05:13:32] 📝 جاهز لبدء التحويل...
[05:13:46] 📄 تم اختيار الملف: 4.py
[05:21:25] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[05:21:25] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[05:21:25] 📝 جاهز لبدء التحويل...
[05:21:45] 📄 تم اختيار الملف: 2.py
[05:21:50] 🔍 بدء تحليل المشروع الذكي...
[05:21:50] 📁 تم العثور على 1 ملف Python
[05:21:50] 📋 تحليل requirements.txt
[05:21:50] 🔍 فحص حالة المكتبات...
[05:21:50] 📊 النتائج: 1 مثبتة، 1 مفقودة، 0 تحتاج تحديث
[05:21:50] 🔍 البحث عن البدائل...
[05:21:50] ✅ تم تحليل 1 ملف و 2 مكتبة
[05:38:49] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[05:38:49] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[05:38:49] 📝 جاهز لبدء التحويل...
[05:38:57] 📄 تم اختيار الملف: 2.py
[13:55:55] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[13:55:55] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[13:55:55] 📝 جاهز لبدء التحويل...
[13:55:55] 🔍 بدء فحص المتطلبات...
[13:55:56] ✅ PyInstaller مثبت - الإصدار: 6.14.2
[13:55:56] ✅ Python - الإصدار: 3.11.0
[13:55:56] ⚠️ لم يتم تحديد ملف مصدر
[13:55:56] ✅ مجلد الحفظ موجود: C:/Users/<USER>/Desktop
[13:55:56] 💾 المساحة المتاحة: 9 جيجابايت
[13:55:56] ✅ انتهى فحص المتطلبات
[13:57:59] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[13:57:59] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[13:57:59] 📝 جاهز لبدء التحويل...
[13:57:59] 🔍 بدء فحص المتطلبات...
[13:57:59] ✅ PyInstaller مثبت - الإصدار: 6.14.2
[13:57:59] ✅ Python - الإصدار: 3.11.0
[13:57:59] ⚠️ لم يتم تحديد ملف مصدر
[13:57:59] ✅ مجلد الحفظ موجود: C:/Users/<USER>/Desktop
[13:57:59] 💾 المساحة المتاحة: 9 جيجابايت
[13:57:59] ✅ انتهى فحص المتطلبات
[13:58:26] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[13:58:26] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[13:58:26] 📝 جاهز لبدء التحويل...
[13:58:26] 🔍 بدء فحص المتطلبات...
[13:58:26] ✅ PyInstaller مثبت - الإصدار: 6.14.2
[13:58:26] ✅ Python - الإصدار: 3.11.0
[13:58:26] ⚠️ لم يتم تحديد ملف مصدر
[13:58:26] ✅ مجلد الحفظ موجود: C:/Users/<USER>/Desktop
[13:58:26] 💾 المساحة المتاحة: 9 جيجابايت
[13:58:26] ✅ انتهى فحص المتطلبات
[14:00:04] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[14:00:04] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[14:00:04] 📝 جاهز لبدء التحويل...
[14:00:04] 🔍 بدء فحص المتطلبات...
[14:00:04] ✅ PyInstaller مثبت - الإصدار: 6.14.2
[14:00:04] ✅ Python - الإصدار: 3.11.0
[14:00:04] ⚠️ لم يتم تحديد ملف مصدر
[14:00:04] ✅ مجلد الحفظ موجود: C:/Users/<USER>/Desktop
[14:00:04] 💾 المساحة المتاحة: 9 جيجابايت
[14:00:04] ✅ انتهى فحص المتطلبات
[15:59:54] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[15:59:54] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[15:59:54] 📝 جاهز لبدء التحويل...
[06:57:33] 🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI
[06:57:33] ✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة
[06:57:33] 📝 جاهز لبدء التحويل...
