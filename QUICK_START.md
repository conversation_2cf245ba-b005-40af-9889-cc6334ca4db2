# 🚀 دليل البدء السريع - Python to EXE Converter Pro v2.0

## 📋 المتطلبات الأساسية

### 1. Python
- **الإصدار المطلوب**: Python 3.7 أو أحدث
- **التحميل**: [python.org](https://python.org)
- **التحقق**: `python --version`

### 2. PyInstaller
- **التثبيت التلقائي**: سيتم تثبيته عند أول تشغيل
- **التثبيت اليدوي**: `pip install PyInstaller`

## 🚀 طرق التشغيل

### Windows
```bash
# الطريقة الأسهل
run.bat

# أو
python pyinstaller_gui.py
```

### Linux/macOS
```bash
# إعطاء صلاحية التنفيذ
chmod +x run.sh

# التشغيل
./run.sh

# أو
python3 pyinstaller_gui.py
```

### تثبيت المتطلبات
```bash
# تثبيت تلقائي
python install_requirements.py

# تثبيت يدوي
pip install -r requirements.txt
```

## 📖 كيفية الاستخدام

### الخطوات الأساسية

1. **🔴 اختيار الملف المصدر**
   - انقر على "📄 ملف" لاختيار ملف Python واحد
   - أو "📁 مجلد" لاختيار مجلد يحتوي على مشروع Python

2. **🟡 تحديد مجلد الحفظ**
   - انقر على "📁 تصفح" لاختيار مكان حفظ الملف التنفيذي

3. **🟢 ضبط الخيارات** (اختياري)
   - **📦 ملف واحد**: إنشاء ملف exe واحد فقط
   - **🖥️ إخفاء الكونسول**: إخفاء نافذة سطر الأوامر
   - **🐛 وضع التصحيح**: للمساعدة في حل المشاكل
   - **⚡ تحسين الحجم**: تقليل حجم الملف النهائي
   - **🗜️ ضغط UPX**: ضغط إضافي (يتطلب UPX)
   - **🎨 أيقونة**: إضافة أيقونة مخصصة

4. **🔵 بدء التحويل**
   - انقر على "🚀 بدء التحويل"
   - راقب التقدم في شريط التقدم والسجل

### الأدوات المساعدة

- **👁️ معاينة الأمر**: لرؤية أمر PyInstaller الذي سيتم تنفيذه
- **🔍 فحص المتطلبات**: للتأكد من جاهزية النظام

## 📊 التبويبات

### 💻 معاينة الأمر
- عرض أمر PyInstaller المفصل
- ملاحظات وتوجيهات مفيدة

### 📋 سجل العمليات
- متابعة مباشرة لعملية التحويل
- رسائل الحالة والأخطاء
- حفظ تلقائي في `conversion_log.txt`

### 📁 الملفات المحولة
- قائمة بالملفات المحولة بنجاح
- معلومات الحجم والمسار
- تاريخ التحويل

## ⚠️ نصائح مهمة

### قبل التحويل
- ✅ تأكد من عدم وجود أخطاء في الكود
- ✅ استخدم "🔍 فحص المتطلبات" للتحقق
- ✅ تأكد من وجود مساحة كافية (1 جيجابايت على الأقل)

### اختيار الخيارات
- 📦 **ملف واحد**: مناسب للتوزيع السهل
- 🖥️ **إخفاء الكونسول**: للتطبيقات ذات الواجهة الرسومية
- 🐛 **وضع التصحيح**: فقط عند وجود مشاكل
- ⚡ **تحسين الحجم**: يقلل الحجم لكن يزيد وقت التحويل

### بعد التحويل
- 📁 ابحث عن الملف في مجلد الحفظ المحدد
- 🧪 اختبر الملف التنفيذي قبل التوزيع
- 📋 راجع السجل في حالة وجود مشاكل

## 🐛 حل المشاكل الشائعة

### "PyInstaller غير مثبت"
```bash
pip install PyInstaller
```

### "خطأ في بناء الجملة"
- تحقق من صحة كود Python
- استخدم "🔍 فحص المتطلبات"

### "مساحة القرص غير كافية"
- احذف الملفات المؤقتة
- تأكد من وجود 1 جيجابايت على الأقل

### "فشل التحويل"
- راجع سجل العمليات للتفاصيل
- تأكد من عدم وجود مكتبات مفقودة
- جرب وضع التصحيح

### "الملف التنفيذي لا يعمل"
- تأكد من تثبيت جميع المكتبات المطلوبة
- جرب تشغيل الملف من سطر الأوامر لرؤية الأخطاء
- استخدم وضع التصحيح

## 📁 هيكل الملفات

```
Python to EXE Converter Pro/
├── pyinstaller_gui.py      # الملف الرئيسي
├── glass_theme.py          # ملف التصميم الزجاجي
├── config.py               # ملف الإعدادات
├── requirements.txt        # قائمة المتطلبات
├── settings.json           # إعدادات المستخدم
├── run.bat                 # ملف تشغيل Windows
├── run.sh                  # ملف تشغيل Linux/macOS
├── install_requirements.py # مثبت المتطلبات
├── conversion_log.txt      # سجل العمليات
└── README.md               # الدليل الكامل
```

## 🔗 روابط مفيدة

- **PyInstaller**: [pyinstaller.readthedocs.io](https://pyinstaller.readthedocs.io)
- **Python**: [python.org](https://python.org)
- **UPX**: [upx.github.io](https://upx.github.io)

## 📞 الدعم

في حالة وجود مشاكل:
1. راجع سجل العمليات
2. تحقق من رسائل الخطأ
3. استخدم وضع التصحيح
4. راجع الدليل الكامل في README.md

---

**🎉 استمتع بتحويل تطبيقات Python إلى ملفات تنفيذية بسهولة!**
