# 🚀 Python to EXE Converter Pro v3.0

<div align="center">

![Python to EXE Converter Pro](https://img.shields.io/badge/Python%20to%20EXE-Converter%20Pro-blue?style=for-the-badge&logo=python)
![Version](https://img.shields.io/badge/Version-3.0.0-green?style=for-the-badge)
![License](https://img.shields.io/badge/License-MIT-yellow?style=for-the-badge)

**محول متقدم وذكي لتحويل ملفات Python إلى تطبيقات تنفيذية**

[العربية](#العربية) | [English](#english)

</div>

---

## العربية

### 🌟 الميزات الجديدة في v3.0

#### 🏗️ معمارية محسنة
- **هيكل معماري جديد** - كود منظم وقابل للصيانة
- **نظام إضافات متقدم** - قابلية توسع لا محدودة
- **دعم أطر عمل متعددة** - Tkinter, PyQt, واجهة ويب

#### 🎨 واجهة مستخدم عصرية
- **تصميم عصري ومتجاوب** - تجربة مستخدم محسنة
- **ثيمات متعددة** - داكن، فاتح، زجاجي، نيون
- **مكونات ذكية** - عناصر واجهة قابلة لإعادة الاستخدام

#### 🤖 ذكاء اصطناعي متقدم
- **محلل كود ذكي** - تحليل التبعيات تلقائياً
- **مولد أيقونات AI** - إنشاء أيقونات مخصصة
- **مساعد ذكي** - إرشادات وحلول تلقائية

#### ⚡ أداء محسن
- **معالجة متوازية** - تحويل أسرع وأكثر كفاءة
- **تخزين مؤقت ذكي** - تسريع العمليات المتكررة
- **تحسين الذاكرة** - استخدام أمثل للموارد

#### 🔒 أمان متقدم
- **تشفير الإعدادات** - حماية البيانات الحساسة
- **فحص الأمان** - كشف التهديدات المحتملة
- **نسخ احتياطية** - حماية من فقدان البيانات

### 📦 التثبيت

#### المتطلبات
- Python 3.7 أو أحدث
- نظام التشغيل: Windows, macOS, Linux

#### التثبيت السريع
```bash
# استنساخ المشروع
git clone https://github.com/your-repo/python-to-exe-converter-pro-v3.git
cd python-to-exe-converter-pro-v3

# تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل التطبيق
python main.py
```

#### التثبيت المتقدم
```bash
# إنشاء بيئة افتراضية
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate     # Windows

# تثبيت المتطلبات
pip install -r requirements.txt

# تثبيت المتطلبات الاختيارية
pip install -r requirements-optional.txt
```

### 🚀 الاستخدام

#### الواجهة الرسومية
```bash
# تشغيل الواجهة الافتراضية (Tkinter)
python main.py

# تشغيل واجهة PyQt
python main.py --ui pyqt --theme glass

# تشغيل واجهة الويب
python main.py --ui web --theme neon
```

#### سطر الأوامر
```bash
# تحويل بسيط
python main.py --cli myapp.py

# تحويل متقدم
python main.py --cli myapp.py --output dist --onefile --windowed --icon icon.ico

# مع خيارات التصحيح
python main.py --cli myapp.py --debug --log-level DEBUG
```

### 📚 الدليل السريع

#### 1. اختيار الملف
- انقر على "تصفح" بجانب "ملف Python"
- اختر ملف `.py` الذي تريد تحويله

#### 2. تكوين الخيارات
- **نمط التحويل**: ملف واحد أو مجلد
- **الواجهة**: نافذة أو وحدة تحكم
- **الأيقونة**: اختياري لتخصيص الأيقونة

#### 3. بدء التحويل
- انقر على "🚀 بدء التحويل"
- راقب التقدم في شريط التقدم
- تحقق من السجل للتفاصيل

#### 4. النتيجة
- ستجد الملف المحول في مجلد الإخراج
- اختبر التطبيق للتأكد من عمله

### 🔧 الإعدادات المتقدمة

#### ملف الإعدادات
```json
{
  "app": {
    "language": "ar",
    "theme": "dark",
    "auto_save": true
  },
  "conversion": {
    "default_mode": "onefile",
    "default_optimization": 1,
    "clean_build": true
  },
  "ui": {
    "window_width": 1400,
    "window_height": 900,
    "show_advanced_options": false
  }
}
```

#### متغيرات البيئة
```bash
export PYTOEXE_CONFIG_DIR="/path/to/config"
export PYTOEXE_LOG_LEVEL="DEBUG"
export PYTOEXE_THEME="glass"
```

### 🔌 نظام الإضافات

#### إنشاء إضافة
```python
from plugins import Plugin

class MyPlugin(Plugin):
    def __init__(self):
        super().__init__("my_plugin", "1.0.0")
    
    def initialize(self):
        # كود التهيئة
        pass
    
    def process(self, data):
        # معالجة البيانات
        return data
```

#### تثبيت إضافة
```bash
# نسخ الإضافة إلى مجلد plugins
cp my_plugin.py plugins/

# إعادة تشغيل التطبيق
python main.py
```

### 🧪 الاختبارات

```bash
# تشغيل جميع الاختبارات
pytest

# اختبارات مع التغطية
pytest --cov=core --cov=ui --cov=plugins

# اختبارات محددة
pytest tests/test_converter.py -v
```

### 📊 الأداء

#### معايير الأداء
- **سرعة التحويل**: تحسن بنسبة 40% عن v2.0
- **استخدام الذاكرة**: انخفاض بنسبة 25%
- **حجم الملف الناتج**: تحسن بنسبة 15%

#### نصائح التحسين
1. **استخدم وضع onefile** للتطبيقات الصغيرة
2. **فعل UPX compression** لتقليل الحجم
3. **استبعد المكتبات غير المستخدمة** يدوياً
4. **استخدم virtual environment** نظيف

### 🐛 استكشاف الأخطاء

#### مشاكل شائعة

**المشكلة**: "PyInstaller not found"
```bash
# الحل
pip install PyInstaller>=6.0.0
```

**المشكلة**: "Module not found in executable"
```bash
# الحل: إضافة المكتبة يدوياً
python main.py --cli myapp.py --hidden-import missing_module
```

**المشكلة**: "Icon file not supported"
```bash
# الحل: تحويل الأيقونة إلى .ico
pip install Pillow
python -c "from PIL import Image; Image.open('icon.png').save('icon.ico')"
```

### 🤝 المساهمة

نرحب بمساهماتكم! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) للتفاصيل.

#### خطوات المساهمة
1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. فتح Pull Request

### 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

### 🙏 شكر وتقدير

- فريق PyInstaller لأداة التحويل الأساسية
- مجتمع Python للدعم المستمر
- جميع المساهمين في المشروع

### 📞 الدعم

- **GitHub Issues**: [رابط المشاكل](https://github.com/your-repo/issues)
- **البريد الإلكتروني**: <EMAIL>
- **التوثيق**: [رابط التوثيق](https://docs.pytoexeconverter.com)

---

## English

### 🌟 New Features in v3.0

#### 🏗️ Enhanced Architecture
- **New architectural structure** - Organized and maintainable code
- **Advanced plugin system** - Unlimited extensibility
- **Multiple framework support** - Tkinter, PyQt, Web interface

#### 🎨 Modern User Interface
- **Modern and responsive design** - Enhanced user experience
- **Multiple themes** - Dark, light, glass, neon
- **Smart components** - Reusable UI elements

#### 🤖 Advanced AI
- **Smart code analyzer** - Automatic dependency analysis
- **AI icon generator** - Custom icon creation
- **Smart assistant** - Automatic guidance and solutions

### 📦 Installation

```bash
# Clone the project
git clone https://github.com/your-repo/python-to-exe-converter-pro-v3.git
cd python-to-exe-converter-pro-v3

# Install requirements
pip install -r requirements.txt

# Run the application
python main.py
```

### 🚀 Usage

#### GUI Mode
```bash
python main.py --ui tkinter --theme dark
```

#### CLI Mode
```bash
python main.py --cli myapp.py --onefile --windowed
```

### 📚 Documentation

Full documentation available at: [docs.pytoexeconverter.com](https://docs.pytoexeconverter.com)

### 🤝 Contributing

Contributions are welcome! Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details.

### 📄 License

This project is licensed under the MIT License - see [LICENSE](LICENSE) file for details.

---

<div align="center">

**Made with ❤️ by Python to EXE Converter Pro Team**

[⬆ Back to top](#-python-to-exe-converter-pro-v30)

</div>
