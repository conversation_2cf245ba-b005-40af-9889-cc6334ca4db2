#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محرر الأيقونات التفاعلي - Interactive Icon Editor
تصميم وتحرير الأيقونات مع معاينة مباشرة
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser, simpledialog
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
from PIL.ImageTk import PhotoImage
import os
import json

class IconEditor:
    """محرر الأيقونات التفاعلي"""
    
    def __init__(self, parent, callback=None):
        self.parent = parent
        self.callback = callback
        self.current_icon = None
        self.canvas_size = 256
        self.elements = []  # عناصر الأيقونة
        self.selected_element = None
        self.current_color = "#6366f1"
        
        self.setup_editor()
    
    def setup_editor(self):
        """إعداد محرر الأيقونات"""
        # إطار المحرر
        self.editor_frame = tk.Frame(self.parent, bg='#1a1a2e')
        self.editor_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # العنوان
        title_label = tk.Label(
            self.editor_frame,
            text="🎨 محرر الأيقونات التفاعلي",
            font=('Segoe UI', 14, 'bold'),
            fg='#ffffff',
            bg='#1a1a2e'
        )
        title_label.pack(pady=(0, 10))
        
        # إطار الأدوات والمعاينة
        main_frame = tk.Frame(self.editor_frame, bg='#1a1a2e')
        main_frame.pack(fill='both', expand=True)
        
        # لوحة الأدوات (يسار)
        self.create_tools_panel(main_frame)
        
        # منطقة المعاينة (وسط)
        self.create_preview_area(main_frame)
        
        # لوحة الخصائص (يمين)
        self.create_properties_panel(main_frame)
    
    def create_tools_panel(self, parent):
        """إنشاء لوحة الأدوات"""
        tools_frame = tk.Frame(parent, bg='#16213e', width=200)
        tools_frame.pack(side='left', fill='y', padx=(0, 10))
        tools_frame.pack_propagate(False)
        
        # عنوان الأدوات
        tools_title = tk.Label(
            tools_frame,
            text="🛠️ الأدوات",
            font=('Segoe UI', 12, 'bold'),
            fg='#ffffff',
            bg='#16213e'
        )
        tools_title.pack(pady=10)
        
        # أزرار الأدوات
        tools = [
            ("🎨 ألوان", self.choose_color),
            ("⭕ دائرة", lambda: self.add_shape("circle")),
            ("⬜ مربع", lambda: self.add_shape("rectangle")),
            ("🔺 مثلث", lambda: self.add_shape("triangle")),
            ("⭐ نجمة", lambda: self.add_shape("star")),
            ("📝 نص", self.add_text),
            ("🖼️ صورة", self.add_image),
            ("🎭 قناع", self.add_gradient),
            ("🗑️ حذف", self.delete_selected),
            ("📋 نسخ", self.copy_element),
            ("📄 لصق", self.paste_element),
            ("💾 حفظ", self.save_icon),
            ("📂 فتح", self.load_icon),
            ("🔄 إعادة تعيين", self.reset_canvas)
        ]
        
        for tool_name, command in tools:
            btn = tk.Button(
                tools_frame,
                text=tool_name,
                command=command,
                font=('Segoe UI', 9),
                fg='#ffffff',
                bg='#6366f1',
                activebackground='#8b8cf8',
                relief='flat',
                bd=0,
                width=15,
                pady=3
            )
            btn.pack(pady=1, padx=10, fill='x')
    
    def create_preview_area(self, parent):
        """إنشاء منطقة المعاينة"""
        preview_frame = tk.Frame(parent, bg='#16213e')
        preview_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        # عنوان المعاينة
        preview_title = tk.Label(
            preview_frame,
            text="👁️ معاينة الأيقونة",
            font=('Segoe UI', 12, 'bold'),
            fg='#ffffff',
            bg='#16213e'
        )
        preview_title.pack(pady=10)
        
        # إطار Canvas مع شبكة
        canvas_frame = tk.Frame(preview_frame, bg='#16213e')
        canvas_frame.pack(pady=10)
        
        # Canvas للرسم
        self.canvas = tk.Canvas(
            canvas_frame,
            width=self.canvas_size,
            height=self.canvas_size,
            bg='white',
            relief='solid',
            bd=2,
            highlightthickness=0
        )
        self.canvas.pack()
        
        # رسم الشبكة
        self.draw_grid()
        
        # ربط الأحداث
        self.canvas.bind("<Button-1>", self.on_canvas_click)
        self.canvas.bind("<B1-Motion>", self.on_canvas_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_canvas_release)
        self.canvas.bind("<Double-Button-1>", self.on_canvas_double_click)
        
        # أزرار التحكم السريع
        controls_frame = tk.Frame(preview_frame, bg='#16213e')
        controls_frame.pack(pady=10)
        
        control_buttons = [
            ("🔍 تكبير", self.zoom_in),
            ("🔍 تصغير", self.zoom_out),
            ("🔄 تدوير", self.rotate_selected),
            ("↔️ انعكاس أفقي", self.flip_horizontal),
            ("↕️ انعكاس عمودي", self.flip_vertical)
        ]
        
        for btn_text, command in control_buttons:
            btn = tk.Button(
                controls_frame,
                text=btn_text,
                command=command,
                font=('Segoe UI', 8),
                fg='#ffffff',
                bg='#ec4899',
                activebackground='#f472b6',
                relief='flat',
                bd=0,
                padx=5
            )
            btn.pack(side='left', padx=2)
        
        # معاينة بأحجام مختلفة
        sizes_frame = tk.Frame(preview_frame, bg='#16213e')
        sizes_frame.pack(pady=10)
        
        sizes_label = tk.Label(
            sizes_frame,
            text="📏 معاينة الأحجام:",
            font=('Segoe UI', 10),
            fg='#ffffff',
            bg='#16213e'
        )
        sizes_label.pack()
        
        # إطار الأحجام المختلفة
        self.sizes_frame = tk.Frame(sizes_frame, bg='#16213e')
        self.sizes_frame.pack(pady=5)
        
        self.update_size_previews()
    
    def create_properties_panel(self, parent):
        """إنشاء لوحة الخصائص"""
        props_frame = tk.Frame(parent, bg='#16213e', width=220)
        props_frame.pack(side='right', fill='y')
        props_frame.pack_propagate(False)
        
        # عنوان الخصائص
        props_title = tk.Label(
            props_frame,
            text="⚙️ الخصائص",
            font=('Segoe UI', 12, 'bold'),
            fg='#ffffff',
            bg='#16213e'
        )
        props_title.pack(pady=10)
        
        # خصائص اللون
        color_frame = tk.LabelFrame(
            props_frame,
            text="🎨 الألوان",
            font=('Segoe UI', 9),
            fg='#ffffff',
            bg='#16213e',
            bd=1,
            relief='solid'
        )
        color_frame.pack(fill='x', padx=10, pady=5)
        
        self.color_display = tk.Label(
            color_frame,
            text="اللون الحالي",
            bg=self.current_color,
            width=15,
            height=2,
            relief='solid',
            bd=1
        )
        self.color_display.pack(pady=5)
        
        # ألوان سريعة
        quick_colors_frame = tk.Frame(color_frame, bg='#16213e')
        quick_colors_frame.pack(pady=5)
        
        quick_colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff', '#ffffff', '#000000']
        for i, color in enumerate(quick_colors):
            btn = tk.Button(
                quick_colors_frame,
                bg=color,
                width=2,
                height=1,
                relief='solid',
                bd=1,
                command=lambda c=color: self.set_color(c)
            )
            btn.grid(row=i//4, column=i%4, padx=1, pady=1)
        
        # خصائص الحجم
        size_frame = tk.LabelFrame(
            props_frame,
            text="📏 الحجم",
            font=('Segoe UI', 9),
            fg='#ffffff',
            bg='#16213e',
            bd=1,
            relief='solid'
        )
        size_frame.pack(fill='x', padx=10, pady=5)
        
        self.size_var = tk.IntVar(value=50)
        size_scale = tk.Scale(
            size_frame,
            from_=10,
            to=150,
            orient='horizontal',
            variable=self.size_var,
            bg='#16213e',
            fg='#ffffff',
            highlightthickness=0,
            command=self.on_size_change
        )
        size_scale.pack(fill='x', padx=5, pady=5)
        
        # خصائص الشفافية
        opacity_frame = tk.LabelFrame(
            props_frame,
            text="👻 الشفافية",
            font=('Segoe UI', 9),
            fg='#ffffff',
            bg='#16213e',
            bd=1,
            relief='solid'
        )
        opacity_frame.pack(fill='x', padx=10, pady=5)
        
        self.opacity_var = tk.IntVar(value=100)
        opacity_scale = tk.Scale(
            opacity_frame,
            from_=10,
            to=100,
            orient='horizontal',
            variable=self.opacity_var,
            bg='#16213e',
            fg='#ffffff',
            highlightthickness=0,
            command=self.on_opacity_change
        )
        opacity_scale.pack(fill='x', padx=5, pady=5)
        
        # قائمة العناصر
        elements_frame = tk.LabelFrame(
            props_frame,
            text="📋 العناصر",
            font=('Segoe UI', 9),
            fg='#ffffff',
            bg='#16213e',
            bd=1,
            relief='solid'
        )
        elements_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # إطار القائمة مع شريط التمرير
        list_frame = tk.Frame(elements_frame, bg='#16213e')
        list_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        self.elements_listbox = tk.Listbox(
            list_frame,
            bg='#2a2a3e',
            fg='#ffffff',
            selectbackground='#6366f1',
            height=6,
            relief='flat',
            bd=0,
            highlightthickness=0
        )
        
        scrollbar = tk.Scrollbar(list_frame, orient='vertical', command=self.elements_listbox.yview)
        self.elements_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.elements_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        self.elements_listbox.bind('<Double-Button-1>', self.edit_element)
        self.elements_listbox.bind('<<ListboxSelect>>', self.on_element_select)
        
        # أزرار إدارة العناصر
        elements_buttons_frame = tk.Frame(elements_frame, bg='#16213e')
        elements_buttons_frame.pack(fill='x', padx=5, pady=5)
        
        element_buttons = [
            ("⬆️", self.move_element_up),
            ("⬇️", self.move_element_down),
            ("👁️", self.toggle_element_visibility),
            ("🔒", self.lock_element)
        ]
        
        for btn_text, command in element_buttons:
            btn = tk.Button(
                elements_buttons_frame,
                text=btn_text,
                command=command,
                font=('Segoe UI', 8),
                fg='#ffffff',
                bg='#06b6d4',
                activebackground='#22d3ee',
                relief='flat',
                bd=0,
                width=3
            )
            btn.pack(side='left', padx=1)
    
    def draw_grid(self):
        """رسم الشبكة"""
        grid_size = 16
        for i in range(0, self.canvas_size, grid_size):
            self.canvas.create_line(i, 0, i, self.canvas_size, fill='#e0e0e0', width=1, tags="grid")
            self.canvas.create_line(0, i, self.canvas_size, i, fill='#e0e0e0', width=1, tags="grid")
    
    def choose_color(self):
        """اختيار لون"""
        color = colorchooser.askcolor(title="اختر لون")[1]
        if color:
            self.set_color(color)
    
    def set_color(self, color):
        """تعيين اللون"""
        self.current_color = color
        self.color_display.configure(bg=color)
        
        # تطبيق اللون على العنصر المحدد
        if self.selected_element:
            self.update_element_color(self.selected_element, color)

    def add_shape(self, shape_type):
        """إضافة شكل"""
        size = self.size_var.get()
        x = self.canvas_size // 2
        y = self.canvas_size // 2

        if shape_type == "circle":
            item_id = self.canvas.create_oval(
                x - size//2, y - size//2,
                x + size//2, y + size//2,
                fill=self.current_color,
                outline='black',
                width=2,
                tags="element"
            )
        elif shape_type == "rectangle":
            item_id = self.canvas.create_rectangle(
                x - size//2, y - size//2,
                x + size//2, y + size//2,
                fill=self.current_color,
                outline='black',
                width=2,
                tags="element"
            )
        elif shape_type == "triangle":
            item_id = self.canvas.create_polygon(
                x, y - size//2,
                x - size//2, y + size//2,
                x + size//2, y + size//2,
                fill=self.current_color,
                outline='black',
                width=2,
                tags="element"
            )
        elif shape_type == "star":
            points = self.create_star_points(x, y, size//2, size//4)
            item_id = self.canvas.create_polygon(
                points,
                fill=self.current_color,
                outline='black',
                width=2,
                tags="element"
            )

        # إضافة للقائمة
        element_info = {
            'id': item_id,
            'type': shape_type,
            'color': self.current_color,
            'size': size,
            'x': x,
            'y': y,
            'visible': True,
            'locked': False,
            'opacity': self.opacity_var.get()
        }
        self.elements.append(element_info)
        self.update_elements_list()
        self.update_size_previews()

    def create_star_points(self, cx, cy, outer_radius, inner_radius):
        """إنشاء نقاط النجمة"""
        import math
        points = []
        for i in range(10):
            angle = math.pi * i / 5
            if i % 2 == 0:
                radius = outer_radius
            else:
                radius = inner_radius
            x = cx + radius * math.cos(angle - math.pi/2)
            y = cy + radius * math.sin(angle - math.pi/2)
            points.extend([x, y])
        return points

    def add_text(self):
        """إضافة نص"""
        text = simpledialog.askstring("إضافة نص", "أدخل النص:")
        if text:
            x = self.canvas_size // 2
            y = self.canvas_size // 2
            font_size = max(8, self.size_var.get()//4)

            item_id = self.canvas.create_text(
                x, y,
                text=text,
                fill=self.current_color,
                font=('Arial', font_size, 'bold'),
                tags="element"
            )

            element_info = {
                'id': item_id,
                'type': 'text',
                'text': text,
                'color': self.current_color,
                'size': font_size,
                'x': x,
                'y': y,
                'visible': True,
                'locked': False,
                'opacity': self.opacity_var.get()
            }
            self.elements.append(element_info)
            self.update_elements_list()
            self.update_size_previews()

    def add_image(self):
        """إضافة صورة"""
        file_path = filedialog.askopenfilename(
            title="اختر صورة",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.bmp *.ico")]
        )
        if file_path:
            try:
                image = Image.open(file_path)
                size = self.size_var.get()

                # تحويل إلى RGBA إذا لزم الأمر
                if image.mode != 'RGBA':
                    image = image.convert('RGBA')

                # تغيير الحجم مع الحفاظ على النسبة
                image.thumbnail((size, size), Image.Resampling.LANCZOS)
                photo = PhotoImage(image)

                x = self.canvas_size // 2
                y = self.canvas_size // 2

                item_id = self.canvas.create_image(x, y, image=photo, tags="element")

                element_info = {
                    'id': item_id,
                    'type': 'image',
                    'image': photo,  # حفظ المرجع
                    'original_image': image,
                    'path': file_path,
                    'size': size,
                    'x': x,
                    'y': y,
                    'visible': True,
                    'locked': False,
                    'opacity': self.opacity_var.get()
                }
                self.elements.append(element_info)
                self.update_elements_list()
                self.update_size_previews()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحميل الصورة: {e}")

    def add_gradient(self):
        """إضافة تدرج لوني"""
        # اختيار لونين للتدرج
        color1 = colorchooser.askcolor(title="اختر اللون الأول")[1]
        if not color1:
            return

        color2 = colorchooser.askcolor(title="اختر اللون الثاني")[1]
        if not color2:
            return

        # إنشاء تدرج
        size = self.size_var.get()
        gradient_image = self.create_gradient_image(size, size, color1, color2)
        photo = PhotoImage(gradient_image)

        x = self.canvas_size // 2
        y = self.canvas_size // 2

        item_id = self.canvas.create_image(x, y, image=photo, tags="element")

        element_info = {
            'id': item_id,
            'type': 'gradient',
            'image': photo,
            'color1': color1,
            'color2': color2,
            'size': size,
            'x': x,
            'y': y,
            'visible': True,
            'locked': False,
            'opacity': self.opacity_var.get()
        }
        self.elements.append(element_info)
        self.update_elements_list()
        self.update_size_previews()

    def create_gradient_image(self, width, height, color1, color2):
        """إنشاء صورة تدرج"""
        image = Image.new('RGBA', (width, height))
        draw = ImageDraw.Draw(image)

        # تحويل الألوان إلى RGB
        r1, g1, b1 = tuple(int(color1[i:i+2], 16) for i in (1, 3, 5))
        r2, g2, b2 = tuple(int(color2[i:i+2], 16) for i in (1, 3, 5))

        for y in range(height):
            ratio = y / height
            r = int(r1 + (r2 - r1) * ratio)
            g = int(g1 + (g2 - g1) * ratio)
            b = int(b1 + (b2 - b1) * ratio)
            color = (r, g, b, 255)
            draw.line([(0, y), (width, y)], fill=color)

        return image

    def delete_selected(self):
        """حذف العنصر المحدد"""
        selection = self.elements_listbox.curselection()
        if selection:
            index = selection[0]
            element = self.elements[index]
            if not element.get('locked', False):
                self.canvas.delete(element['id'])
                del self.elements[index]
                self.selected_element = None
                self.update_elements_list()
                self.update_size_previews()
            else:
                messagebox.showwarning("تحذير", "العنصر مقفل ولا يمكن حذفه")

    def copy_element(self):
        """نسخ العنصر المحدد"""
        selection = self.elements_listbox.curselection()
        if selection:
            index = selection[0]
            self.copied_element = self.elements[index].copy()
            messagebox.showinfo("نسخ", "تم نسخ العنصر")

    def paste_element(self):
        """لصق العنصر المنسوخ"""
        if hasattr(self, 'copied_element') and self.copied_element:
            element = self.copied_element.copy()

            # إنشاء عنصر جديد بناءً على النوع
            if element['type'] == 'circle':
                item_id = self.canvas.create_oval(
                    element['x'] - element['size']//2 + 20,
                    element['y'] - element['size']//2 + 20,
                    element['x'] + element['size']//2 + 20,
                    element['y'] + element['size']//2 + 20,
                    fill=element['color'],
                    outline='black',
                    width=2,
                    tags="element"
                )
            elif element['type'] == 'rectangle':
                item_id = self.canvas.create_rectangle(
                    element['x'] - element['size']//2 + 20,
                    element['y'] - element['size']//2 + 20,
                    element['x'] + element['size']//2 + 20,
                    element['y'] + element['size']//2 + 20,
                    fill=element['color'],
                    outline='black',
                    width=2,
                    tags="element"
                )
            elif element['type'] == 'text':
                item_id = self.canvas.create_text(
                    element['x'] + 20,
                    element['y'] + 20,
                    text=element['text'],
                    fill=element['color'],
                    font=('Arial', element['size'], 'bold'),
                    tags="element"
                )

            # تحديث معلومات العنصر
            element['id'] = item_id
            element['x'] += 20
            element['y'] += 20
            element['locked'] = False

            self.elements.append(element)
            self.update_elements_list()
            self.update_size_previews()
        else:
            messagebox.showwarning("تحذير", "لا يوجد عنصر منسوخ")

    def update_elements_list(self):
        """تحديث قائمة العناصر"""
        self.elements_listbox.delete(0, tk.END)
        for i, element in enumerate(self.elements):
            visibility = "👁️" if element.get('visible', True) else "🙈"
            lock = "🔒" if element.get('locked', False) else "🔓"

            if element['type'] == 'text':
                display_text = f"{visibility}{lock} {element['type']}: {element['text'][:10]}"
            elif element['type'] == 'image':
                filename = os.path.basename(element.get('path', 'image'))
                display_text = f"{visibility}{lock} {element['type']}: {filename[:10]}"
            else:
                display_text = f"{visibility}{lock} {element['type']}: {element['color']}"

            self.elements_listbox.insert(tk.END, display_text)

    def on_element_select(self, event):
        """عند تحديد عنصر من القائمة"""
        selection = self.elements_listbox.curselection()
        if selection:
            index = selection[0]
            self.selected_element = self.elements[index]

            # تحديث الخصائص
            if 'color' in self.selected_element:
                self.set_color(self.selected_element['color'])

            self.size_var.set(self.selected_element.get('size', 50))
            self.opacity_var.set(self.selected_element.get('opacity', 100))

    def edit_element(self, event):
        """تحرير عنصر"""
        selection = self.elements_listbox.curselection()
        if selection:
            index = selection[0]
            element = self.elements[index]

            if element.get('locked', False):
                messagebox.showwarning("تحذير", "العنصر مقفل ولا يمكن تحريره")
                return

            if element['type'] == 'text':
                new_text = simpledialog.askstring("تحرير النص", "النص الجديد:", initialvalue=element['text'])
                if new_text:
                    element['text'] = new_text
                    self.canvas.itemconfig(element['id'], text=new_text)
                    self.update_elements_list()
                    self.update_size_previews()

    def move_element_up(self):
        """تحريك العنصر لأعلى في الطبقات"""
        selection = self.elements_listbox.curselection()
        if selection and selection[0] > 0:
            index = selection[0]
            element = self.elements[index]

            # تبديل في القائمة
            self.elements[index], self.elements[index-1] = self.elements[index-1], self.elements[index]

            # تحديث Canvas
            self.canvas.tag_raise(element['id'])

            self.update_elements_list()
            self.elements_listbox.selection_set(index-1)

    def move_element_down(self):
        """تحريك العنصر لأسفل في الطبقات"""
        selection = self.elements_listbox.curselection()
        if selection and selection[0] < len(self.elements) - 1:
            index = selection[0]
            element = self.elements[index]

            # تبديل في القائمة
            self.elements[index], self.elements[index+1] = self.elements[index+1], self.elements[index]

            # تحديث Canvas
            self.canvas.tag_lower(element['id'])

            self.update_elements_list()
            self.elements_listbox.selection_set(index+1)

    def toggle_element_visibility(self):
        """تبديل رؤية العنصر"""
        selection = self.elements_listbox.curselection()
        if selection:
            index = selection[0]
            element = self.elements[index]

            element['visible'] = not element.get('visible', True)

            if element['visible']:
                self.canvas.itemconfig(element['id'], state='normal')
            else:
                self.canvas.itemconfig(element['id'], state='hidden')

            self.update_elements_list()

    def lock_element(self):
        """قفل/إلغاء قفل العنصر"""
        selection = self.elements_listbox.curselection()
        if selection:
            index = selection[0]
            element = self.elements[index]

            element['locked'] = not element.get('locked', False)
            self.update_elements_list()

    def on_size_change(self, value):
        """عند تغيير الحجم"""
        if self.selected_element and not self.selected_element.get('locked', False):
            new_size = int(value)
            self.selected_element['size'] = new_size
            self.update_selected_element()

    def on_opacity_change(self, value):
        """عند تغيير الشفافية"""
        if self.selected_element and not self.selected_element.get('locked', False):
            new_opacity = int(value)
            self.selected_element['opacity'] = new_opacity
            # تطبيق الشفافية (يحتاج تطوير أكثر)

    def update_element_color(self, element, color):
        """تحديث لون العنصر"""
        if not element.get('locked', False):
            element['color'] = color
            if element['type'] in ['circle', 'rectangle', 'triangle', 'star']:
                self.canvas.itemconfig(element['id'], fill=color)
            elif element['type'] == 'text':
                self.canvas.itemconfig(element['id'], fill=color)
            self.update_size_previews()

    def update_selected_element(self):
        """تحديث العنصر المحدد"""
        if not self.selected_element:
            return

        element = self.selected_element
        if element.get('locked', False):
            return

        # تحديث الحجم
        if element['type'] in ['circle', 'rectangle']:
            size = element['size']
            x, y = element['x'], element['y']
            coords = [x - size//2, y - size//2, x + size//2, y + size//2]
            self.canvas.coords(element['id'], *coords)
        elif element['type'] == 'text':
            font_size = max(8, element['size']//4)
            self.canvas.itemconfig(element['id'], font=('Arial', font_size, 'bold'))

        self.update_size_previews()

    def zoom_in(self):
        """تكبير"""
        if self.selected_element and not self.selected_element.get('locked', False):
            self.selected_element['size'] = min(200, self.selected_element['size'] + 10)
            self.size_var.set(self.selected_element['size'])
            self.update_selected_element()

    def zoom_out(self):
        """تصغير"""
        if self.selected_element and not self.selected_element.get('locked', False):
            self.selected_element['size'] = max(10, self.selected_element['size'] - 10)
            self.size_var.set(self.selected_element['size'])
            self.update_selected_element()

    def rotate_selected(self):
        """تدوير العنصر المحدد"""
        if self.selected_element and not self.selected_element.get('locked', False):
            # تدوير بسيط (يحتاج تطوير أكثر للأشكال المعقدة)
            messagebox.showinfo("تدوير", "ميزة التدوير قيد التطوير")

    def flip_horizontal(self):
        """انعكاس أفقي"""
        if self.selected_element and not self.selected_element.get('locked', False):
            messagebox.showinfo("انعكاس", "ميزة الانعكاس قيد التطوير")

    def flip_vertical(self):
        """انعكاس عمودي"""
        if self.selected_element and not self.selected_element.get('locked', False):
            messagebox.showinfo("انعكاس", "ميزة الانعكاس قيد التطوير")

    def update_size_previews(self):
        """تحديث معاينة الأحجام"""
        # مسح المعاينات السابقة
        for widget in self.sizes_frame.winfo_children():
            widget.destroy()

        # إنشاء معاينات بأحجام مختلفة
        sizes = [16, 32, 48, 64]
        for size in sizes:
            try:
                # إنشاء صورة مصغرة
                preview_image = self.create_icon_image(size)
                if preview_image:
                    photo = PhotoImage(preview_image)

                    frame = tk.Frame(self.sizes_frame, bg='#16213e')
                    frame.pack(side='left', padx=5)

                    label = tk.Label(frame, image=photo, bg='white', relief='solid', bd=1)
                    label.image = photo  # حفظ المرجع
                    label.pack()

                    size_label = tk.Label(
                        frame,
                        text=f"{size}x{size}",
                        font=('Segoe UI', 8),
                        fg='#ffffff',
                        bg='#16213e'
                    )
                    size_label.pack()
            except Exception as e:
                print(f"خطأ في معاينة الحجم {size}: {e}")

    def create_icon_image(self, size):
        """إنشاء صورة الأيقونة"""
        try:
            # إنشاء صورة فارغة
            image = Image.new('RGBA', (self.canvas_size, self.canvas_size), (255, 255, 255, 0))
            draw = ImageDraw.Draw(image)

            # رسم العناصر المرئية فقط
            for element in self.elements:
                if not element.get('visible', True):
                    continue

                if element['type'] == 'circle':
                    x, y = element['x'], element['y']
                    r = element['size'] // 2
                    draw.ellipse([x-r, y-r, x+r, y+r], fill=element['color'])
                elif element['type'] == 'rectangle':
                    x, y = element['x'], element['y']
                    s = element['size'] // 2
                    draw.rectangle([x-s, y-s, x+s, y+s], fill=element['color'])
                elif element['type'] == 'triangle':
                    x, y = element['x'], element['y']
                    s = element['size'] // 2
                    points = [(x, y-s), (x-s, y+s), (x+s, y+s)]
                    draw.polygon(points, fill=element['color'])
                elif element['type'] == 'star':
                    x, y = element['x'], element['y']
                    points = self.create_star_points(x, y, element['size']//2, element['size']//4)
                    # تحويل إلى قائمة من tuples
                    star_points = [(points[i], points[i+1]) for i in range(0, len(points), 2)]
                    draw.polygon(star_points, fill=element['color'])
                elif element['type'] == 'text':
                    try:
                        font = ImageFont.truetype("arial.ttf", element['size'])
                    except:
                        font = ImageFont.load_default()

                    # حساب موقع النص
                    bbox = draw.textbbox((0, 0), element['text'], font=font)
                    text_width = bbox[2] - bbox[0]
                    text_height = bbox[3] - bbox[1]

                    x = element['x'] - text_width // 2
                    y = element['y'] - text_height // 2

                    draw.text((x, y), element['text'], fill=element['color'], font=font)

            # تغيير الحجم
            image = image.resize((size, size), Image.Resampling.LANCZOS)
            return image
        except Exception as e:
            print(f"خطأ في إنشاء الأيقونة: {e}")
            return None

    def save_icon(self):
        """حفظ الأيقونة"""
        if not self.elements:
            messagebox.showwarning("تحذير", "لا توجد عناصر للحفظ")
            return

        file_path = filedialog.asksaveasfilename(
            title="حفظ الأيقونة",
            defaultextension=".ico",
            filetypes=[
                ("Icon files", "*.ico"),
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg"),
                ("Project files", "*.json"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                if file_path.endswith('.json'):
                    # حفظ المشروع
                    self.save_project(file_path)
                else:
                    # حفظ الأيقونة
                    self.save_icon_image(file_path)

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ الأيقونة: {e}")

    def save_icon_image(self, file_path):
        """حفظ صورة الأيقونة"""
        if file_path.endswith('.ico'):
            # إنشاء الأيقونة بأحجام متعددة
            sizes = [16, 32, 48, 64, 128, 256]
            images = []

            for size in sizes:
                img = self.create_icon_image(size)
                if img:
                    images.append(img)

            if images:
                # حفظ كملف ICO
                images[0].save(file_path, format='ICO', sizes=[(img.width, img.height) for img in images])
                messagebox.showinfo("نجح الحفظ", f"تم حفظ الأيقونة في:\n{file_path}")

                if self.callback:
                    self.callback(file_path)
            else:
                messagebox.showwarning("تحذير", "فشل في إنشاء الأيقونة")
        else:
            # حفظ كصورة عادية
            size = 256  # حجم افتراضي
            image = self.create_icon_image(size)
            if image:
                if file_path.endswith('.jpg') or file_path.endswith('.jpeg'):
                    # تحويل إلى RGB للـ JPEG
                    rgb_image = Image.new('RGB', image.size, (255, 255, 255))
                    rgb_image.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                    rgb_image.save(file_path, format='JPEG', quality=95)
                else:
                    image.save(file_path, format='PNG')

                messagebox.showinfo("نجح الحفظ", f"تم حفظ الصورة في:\n{file_path}")

                if self.callback:
                    self.callback(file_path)
            else:
                messagebox.showwarning("تحذير", "فشل في إنشاء الصورة")

    def save_project(self, file_path):
        """حفظ المشروع"""
        project_data = {
            'version': '1.0',
            'canvas_size': self.canvas_size,
            'elements': []
        }

        for element in self.elements:
            element_data = element.copy()
            # إزالة المراجع التي لا يمكن تسلسلها
            if 'image' in element_data:
                del element_data['image']
            if 'original_image' in element_data:
                del element_data['original_image']

            project_data['elements'].append(element_data)

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(project_data, f, indent=2, ensure_ascii=False)

        messagebox.showinfo("نجح الحفظ", f"تم حفظ المشروع في:\n{file_path}")

    def load_icon(self):
        """تحميل أيقونة أو مشروع"""
        file_path = filedialog.askopenfilename(
            title="تحميل أيقونة أو مشروع",
            filetypes=[
                ("Project files", "*.json"),
                ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp *.ico"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                if file_path.endswith('.json'):
                    self.load_project(file_path)
                else:
                    self.load_image_as_element(file_path)
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحميل الملف: {e}")

    def load_project(self, file_path):
        """تحميل مشروع"""
        with open(file_path, 'r', encoding='utf-8') as f:
            project_data = json.load(f)

        # مسح العناصر الحالية
        self.reset_canvas()

        # تحميل العناصر
        for element_data in project_data.get('elements', []):
            if element_data['type'] in ['circle', 'rectangle', 'triangle', 'star']:
                self.recreate_shape_element(element_data)
            elif element_data['type'] == 'text':
                self.recreate_text_element(element_data)
            elif element_data['type'] == 'image':
                self.recreate_image_element(element_data)

        self.update_elements_list()
        self.update_size_previews()
        messagebox.showinfo("نجح التحميل", f"تم تحميل المشروع من:\n{file_path}")

    def recreate_shape_element(self, element_data):
        """إعادة إنشاء عنصر شكل"""
        x, y = element_data['x'], element_data['y']
        size = element_data['size']
        color = element_data['color']
        shape_type = element_data['type']

        if shape_type == "circle":
            item_id = self.canvas.create_oval(
                x - size//2, y - size//2, x + size//2, y + size//2,
                fill=color, outline='black', width=2, tags="element"
            )
        elif shape_type == "rectangle":
            item_id = self.canvas.create_rectangle(
                x - size//2, y - size//2, x + size//2, y + size//2,
                fill=color, outline='black', width=2, tags="element"
            )
        elif shape_type == "triangle":
            item_id = self.canvas.create_polygon(
                x, y - size//2, x - size//2, y + size//2, x + size//2, y + size//2,
                fill=color, outline='black', width=2, tags="element"
            )
        elif shape_type == "star":
            points = self.create_star_points(x, y, size//2, size//4)
            item_id = self.canvas.create_polygon(
                points, fill=color, outline='black', width=2, tags="element"
            )

        element_data['id'] = item_id
        self.elements.append(element_data)

    def recreate_text_element(self, element_data):
        """إعادة إنشاء عنصر نص"""
        x, y = element_data['x'], element_data['y']
        text = element_data['text']
        color = element_data['color']
        size = element_data['size']

        item_id = self.canvas.create_text(
            x, y, text=text, fill=color,
            font=('Arial', size, 'bold'), tags="element"
        )

        element_data['id'] = item_id
        self.elements.append(element_data)

    def recreate_image_element(self, element_data):
        """إعادة إنشاء عنصر صورة"""
        if 'path' in element_data and os.path.exists(element_data['path']):
            try:
                image = Image.open(element_data['path'])
                size = element_data['size']

                if image.mode != 'RGBA':
                    image = image.convert('RGBA')

                image.thumbnail((size, size), Image.Resampling.LANCZOS)
                photo = PhotoImage(image)

                item_id = self.canvas.create_image(
                    element_data['x'], element_data['y'],
                    image=photo, tags="element"
                )

                element_data['id'] = item_id
                element_data['image'] = photo
                element_data['original_image'] = image
                self.elements.append(element_data)

            except Exception as e:
                print(f"فشل في تحميل الصورة {element_data['path']}: {e}")

    def load_image_as_element(self, file_path):
        """تحميل صورة كعنصر جديد"""
        try:
            image = Image.open(file_path)
            size = min(100, self.canvas_size // 3)  # حجم افتراضي

            if image.mode != 'RGBA':
                image = image.convert('RGBA')

            image.thumbnail((size, size), Image.Resampling.LANCZOS)
            photo = PhotoImage(image)

            x = self.canvas_size // 2
            y = self.canvas_size // 2

            item_id = self.canvas.create_image(x, y, image=photo, tags="element")

            element_info = {
                'id': item_id,
                'type': 'image',
                'image': photo,
                'original_image': image,
                'path': file_path,
                'size': size,
                'x': x,
                'y': y,
                'visible': True,
                'locked': False,
                'opacity': 100
            }
            self.elements.append(element_info)
            self.update_elements_list()
            self.update_size_previews()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الصورة: {e}")

    def reset_canvas(self):
        """إعادة تعيين اللوحة"""
        self.canvas.delete("element")  # حذف العناصر فقط، الاحتفاظ بالشبكة
        self.elements.clear()
        self.selected_element = None
        self.update_elements_list()
        self.update_size_previews()

    def on_canvas_click(self, event):
        """النقر على اللوحة"""
        self.drag_data = {"x": event.x, "y": event.y}
        # العثور على العنصر المنقور عليه
        clicked_items = self.canvas.find_overlapping(event.x-5, event.y-5, event.x+5, event.y+5)

        # البحث عن عنصر من النوع "element"
        self.selected_item = None
        for item in clicked_items:
            if "element" in self.canvas.gettags(item):
                self.selected_item = item
                break

        # تحديد العنصر في القائمة
        if self.selected_item:
            for i, element in enumerate(self.elements):
                if element['id'] == self.selected_item:
                    self.elements_listbox.selection_clear(0, tk.END)
                    self.elements_listbox.selection_set(i)
                    self.selected_element = element
                    break

    def on_canvas_drag(self, event):
        """سحب على اللوحة"""
        if hasattr(self, 'selected_item') and self.selected_item:
            # التحقق من أن العنصر غير مقفل
            for element in self.elements:
                if element['id'] == self.selected_item and element.get('locked', False):
                    return

            dx = event.x - self.drag_data["x"]
            dy = event.y - self.drag_data["y"]
            self.canvas.move(self.selected_item, dx, dy)

            # تحديث موقع العنصر
            for element in self.elements:
                if element['id'] == self.selected_item:
                    element['x'] += dx
                    element['y'] += dy
                    break

            self.drag_data["x"] = event.x
            self.drag_data["y"] = event.y

    def on_canvas_release(self, event):
        """تحرير الماوس"""
        self.selected_item = None
        self.update_size_previews()

    def on_canvas_double_click(self, event):
        """النقر المزدوج على اللوحة"""
        if self.selected_element and self.selected_element['type'] == 'text':
            self.edit_element(None)
