import tkinter as tk
from tkinter import filedialog, messagebox
from tkinter import font as tkfont
import os
import threading

class CodeEditor(tk.Toplevel):
    def __init__(self, master=None, file_path=None, rtl=True):
        super().__init__(master)
        self.title("📝 محرر الكود المتقدم")
        self.geometry("900x700+120+80")
        self.minsize(700, 500)
        self.configure(bg="#181824")
        self.rtl = rtl
        self.file_path = file_path
        self._setup_fonts()
        self._setup_widgets()
        self._setup_menu()
        self.status_var = tk.StringVar(value="جاهز.")
        self._setup_statusbar()
        self.after(100, self._bring_to_front)
        if file_path:
            self.open_file(file_path)
        self.protocol("WM_DELETE_WINDOW", self.on_close)

    def _bring_to_front(self):
        self.deiconify()
        self.lift()
        self.focus_force()
        self.attributes('-topmost', True)
        self.after(800, lambda: self.attributes('-topmost', False))

    def _setup_statusbar(self):
        statusbar = tk.Label(self, textvariable=self.status_var, anchor='w', bg="#23263a", fg="#f59e0b", font=("Cairo", 10))
        statusbar.pack(fill="x", side="bottom")

    def _setup_fonts(self):
        self.font_main = tkfont.Font(family="Cairo", size=13)
        self.font_bold = tkfont.Font(family="Cairo", size=13, weight="bold")
        self.font_code = tkfont.Font(family="Consolas", size=12)

    def _setup_widgets(self):
        # شريط الأدوات العلوي
        toolbar = tk.Frame(self, bg="#23263a")
        toolbar.pack(fill="x", padx=0, pady=(0, 2))
        
        btn_open = tk.Button(toolbar, text="📂 فتح", font=self.font_main, bg="#6366f1", fg="white", bd=0, padx=16, pady=4, cursor="hand2", command=self.open_file_dialog)
        btn_open.pack(side="right" if self.rtl else "left", padx=4)
        btn_save = tk.Button(toolbar, text="💾 حفظ", font=self.font_main, bg="#10b981", fg="white", bd=0, padx=16, pady=4, cursor="hand2", command=self.save_file)
        btn_save.pack(side="right" if self.rtl else "left", padx=4)
        btn_saveas = tk.Button(toolbar, text="📝 حفظ باسم", font=self.font_main, bg="#f59e0b", fg="white", bd=0, padx=16, pady=4, cursor="hand2", command=self.save_file_as)
        btn_saveas.pack(side="right" if self.rtl else "left", padx=4)
        btn_search = tk.Button(toolbar, text="🔍 بحث", font=self.font_main, bg="#06b6d4", fg="white", bd=0, padx=16, pady=4, cursor="hand2", command=self.open_search_dialog)
        btn_search.pack(side="right" if self.rtl else "left", padx=4)
        btn_undo = tk.Button(toolbar, text="↩️ تراجع", font=self.font_main, bg="#4a4a5e", fg="white", bd=0, padx=12, pady=4, cursor="hand2", command=lambda: self.text_area.event_generate('<<Undo>>'))
        btn_undo.pack(side="right" if self.rtl else "left", padx=2)
        btn_redo = tk.Button(toolbar, text="↪️ إعادة", font=self.font_main, bg="#4a4a5e", fg="white", bd=0, padx=12, pady=4, cursor="hand2", command=lambda: self.text_area.event_generate('<<Redo>>'))
        btn_redo.pack(side="right" if self.rtl else "left", padx=2)
        
        # زر شرح الكود الذكي
        btn_explain = tk.Button(toolbar, text="🤖 شرح ذكي", font=self.font_main, bg="#f59e0b", fg="white", bd=0, padx=16, pady=4, cursor="hand2", command=self.explain_code_smart)
        btn_explain.pack(side="right" if self.rtl else "left", padx=4)
        
        # إطار النص مع أرقام الأسطر
        text_frame = tk.Frame(self, bg="#23263a")
        text_frame.pack(fill="both", expand=True, padx=8, pady=8)
        
        self.line_numbers = tk.Text(text_frame, width=4, padx=6, takefocus=0, border=0, background="#23263a", foreground="#8b8cf8", state="disabled", font=self.font_code)
        self.line_numbers.pack(side="right" if self.rtl else "left", fill="y")
        
        self.text_area = tk.Text(text_frame, wrap="none", font=self.font_code, bg="#181824", fg="#fff", insertbackground="#8b8cf8", undo=True, border=0, relief="flat")
        self.text_area.pack(side="right" if self.rtl else "left", fill="both", expand=True)
        self.text_area.bind("<KeyRelease>", self._on_text_change)
        self.text_area.bind("<Button-1>", self._on_text_change)
        self.text_area.bind("<MouseWheel>", self._on_text_change)
        self.text_area.bind("<Control-f>", lambda e: self.open_search_dialog())
        self.text_area.bind("<Control-s>", lambda e: self.save_file())
        self.text_area.bind("<Control-o>", lambda e: self.open_file_dialog())
        self.text_area.bind("<Control-z>", lambda e: self.text_area.event_generate('<<Undo>>'))
        self.text_area.bind("<Control-y>", lambda e: self.text_area.event_generate('<<Redo>>'))
        self.text_area.focus_set()
        
        # شريط تمرير رأسي
        yscroll = tk.Scrollbar(text_frame, orient="vertical", command=self._on_scroll)
        yscroll.pack(side="right" if self.rtl else "left", fill="y")
        self.text_area.config(yscrollcommand=yscroll.set)
        self.line_numbers.config(yscrollcommand=yscroll.set)
        
        # دعم RTL
        if self.rtl:
                    else:
                    self._on_text_change()

    def _setup_menu(self):
        menubar = tk.Menu(self)
        filemenu = tk.Menu(menubar, tearoff=0)
        filemenu.add_command(label="فتح", command=self.open_file_dialog)
        filemenu.add_command(label="حفظ", command=self.save_file)
        filemenu.add_command(label="حفظ باسم", command=self.save_file_as)
        filemenu.add_separator()
        filemenu.add_command(label="خروج", command=self.on_close)
        menubar.add_cascade(label="ملف", menu=filemenu)
        self.config(menu=menubar)

    def _on_scroll(self, *args):
        self.text_area.yview(*args)
        self.line_numbers.yview(*args)

    def _on_text_change(self, event=None):
        self._update_line_numbers()

    def _update_line_numbers(self):
        self.line_numbers.config(state="normal")
        self.line_numbers.delete("1.0", "end")
        line_count = self.text_area.index("end-1c").split(".")[0]
        lines = "\n".join(str(i) for i in range(1, int(line_count)))
        self.line_numbers.insert("1.0", lines)
        self.line_numbers.config(state="disabled")

    def open_file_dialog(self):
        file_path = filedialog.askopenfilename(filetypes=[("ملفات بايثون", "*.py"), ("كل الملفات", "*.*")])
        if file_path:
            self.open_file(file_path)

    def open_file(self, file_path):
        def load_file():
            try:
                self.status_var.set("جاري تحميل الملف...")
                self.config(cursor="watch")
                self.text_area.config(cursor="watch")
                self.update_idletasks()
                with open(file_path, "r", encoding="utf-8") as f:
                    code = f.read()
                def update_ui():
                    self.text_area.delete("1.0", "end")
                    self.text_area.insert("1.0", code)
                    self.file_path = file_path
                    self.title(f"📝 محرر الكود - {os.path.basename(file_path)}")
                    self._on_text_change()
                    self.status_var.set(f"تم تحميل الملف: {os.path.basename(file_path)}")
                    self.config(cursor="")
                    self.text_area.config(cursor="xterm")
                    self.text_area.focus_set()
                self.after(0, update_ui)
            except Exception as e:
                def show_error():
                    self.status_var.set("خطأ في فتح الملف!")
                    messagebox.showerror("خطأ في فتح الملف", str(e))
                    self.config(cursor="")
                    self.text_area.config(cursor="xterm")
                    self.text_area.focus_set()
                self.after(0, show_error)
        threading.Thread(target=load_file, daemon=True).start()

    def save_file(self):
        if not self.file_path:
            self.save_file_as()
            return
        try:
            self.status_var.set("جاري الحفظ...")
            self.update_idletasks()
            self.config(cursor="watch")
            self.text_area.config(cursor="watch")
            with open(self.file_path, "w", encoding="utf-8") as f:
                f.write(self.text_area.get("1.0", "end-1c"))
            self.status_var.set("تم حفظ الملف بنجاح.")
            messagebox.showinfo("تم الحفظ", "تم حفظ الملف بنجاح.")
        except Exception as e:
            self.status_var.set("خطأ في الحفظ!")
            messagebox.showerror("خطأ في الحفظ", str(e))
        finally:
            self.config(cursor="")
            self.text_area.config(cursor="xterm")
            self.text_area.focus_set()

    def save_file_as(self):
        file_path = filedialog.asksaveasfilename(defaultextension=".py", filetypes=[("ملفات بايثون", "*.py"), ("كل الملفات", "*.*")])
        if file_path:
            self.file_path = file_path
            self.save_file()

    def open_search_dialog(self):
        SearchDialog(self, self.text_area, rtl=self.rtl)

    def explain_code_smart(self):
        try:
            from ai_code_explainer import AICodeExplainer
            code = self.text_area.get("1.0", "end-1c").strip()
            if not code:
                self.status_var.set("لا يوجد كود لشرحه.")
                return
            self.status_var.set("جاري شرح الكود بالذكاء الصناعي...")
            self.update_idletasks()
            self.config(cursor="watch")
            self.text_area.config(cursor="watch")
            def run_explain():
                explainer = AICodeExplainer()
                explanation = explainer.explain_code(code)
                def insert_explanation():
                    self.text_area.delete("1.0", "end")
                    self.text_area.insert("1.0", explanation)
                    self.status_var.set("تم شرح الكود بنجاح.")
                    self.config(cursor="")
                    self.text_area.config(cursor="xterm")
                    self.text_area.focus_set()
                self.after(0, insert_explanation)
            import threading
            threading.Thread(target=run_explain, daemon=True).start()
        except Exception as e:
            self.status_var.set("خطأ في شرح الكود!")
            messagebox.showerror("خطأ في شرح الكود", str(e))
            self.config(cursor="")
            self.text_area.config(cursor="xterm")
            self.text_area.focus_set()

    def on_close(self):
        self.destroy()

class SearchDialog(tk.Toplevel):
    def __init__(self, master, text_widget, rtl=True):
        super().__init__(master)
        self.title("🔍 بحث في الكود")
        self.geometry("400x100+200+200")
        self.configure(bg="#23263a")
        self.text_widget = text_widget
        self.rtl = rtl
        self._setup_widgets()
        self.transient(master)
        self.grab_set()
        self.entry.focus_set()

    def _setup_widgets(self):
        frame = tk.Frame(self, bg="#23263a")
        frame.pack(fill="both", expand=True, padx=12, pady=12)
        lbl = tk.Label(frame, text="كلمة البحث:", font=("Cairo", 12), bg="#23263a", fg="#fff")
        lbl.pack(side="right" if self.rtl else "left")
        self.entry = tk.Entry(frame, font=("Cairo", 12), justify="right" if self.rtl else "left")
        self.entry.pack(side="right" if self.rtl else "left", fill="x", expand=True, padx=8)
        btn = tk.Button(frame, text="بحث", font=("Cairo", 11), bg="#6366f1", fg="white", bd=0, padx=16, pady=4, cursor="hand2", command=self.search)
        btn.pack(side="right" if self.rtl else "left", padx=4)
        self.entry.bind("<Return>", lambda e: self.search())

    def search(self):
        target = self.entry.get()
        self.text_widget.tag_remove('search', '1.0', tk.END)
        if not target:
            return
        start = '1.0'
        while True:
            pos = self.text_widget.search(target, start, stopindex=tk.END, nocase=True)
            if not pos:
                break
            end = f"{pos}+{len(target)}c"
            self.text_widget.tag_add('search', pos, end)
            self.text_widget.tag_config('search', background='#f59e0b', foreground='white')
            start = end
        self.entry.focus_set()
