#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محول الصور إلى أيقونات - Image to Icon Converter
سحب وإفلات الصور وتحويلها لأيقونات عصرية
"""

import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageDraw, ImageFilter, ImageEnhance, ImageOps
from PIL.ImageTk import PhotoImage
try:
    import numpy as np
    HAS_NUMPY = True
except ImportError:
    HAS_NUMPY = False
    np = None
from typing import Optional, Tuple, List
from rembg import remove
import io

class ImageToIconConverter:
    """محول الصور إلى أيقونات مع سحب وإفلات"""
    
    def __init__(self, parent, callback=None):
        self.parent = parent
        self.callback = callback
        self.current_image = None
        self.processed_image = None
        self.setup_converter()
    
    def setup_converter(self):
        """إعداد واجهة المحول"""
        # إطار المحول
        self.converter_frame = tk.Frame(self.parent, bg='#1a1a2e')
        self.converter_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # العنوان
        title_label = tk.Label(
            self.converter_frame,
            text="🖼️ محول الصور إلى أيقونات",
            font=('Segoe UI', 14, 'bold'),
            fg='#ffffff',
            bg='#1a1a2e'
        )
        title_label.pack(pady=(0, 10))
        
        # إطار المحتوى الرئيسي
        main_frame = tk.Frame(self.converter_frame, bg='#1a1a2e')
        main_frame.pack(fill='both', expand=True)
        
        # منطقة السحب والإفلات (يسار)
        self.create_drop_zone(main_frame)
        
        # أدوات التحرير (وسط)
        self.create_editing_tools(main_frame)
        
        # معاينة النتيجة (يمين)
        self.create_preview_area(main_frame)
    
    def create_drop_zone(self, parent):
        """إنشاء منطقة السحب والإفلات"""
        drop_frame = tk.LabelFrame(
            parent,
            text="📁 سحب وإفلات الصورة",
            font=('Segoe UI', 10, 'bold'),
            fg='#ffffff',
            bg='#16213e',
            bd=2,
            relief='solid'
        )
        drop_frame.pack(side='left', fill='both', expand=True, padx=(0, 5))
        
        # منطقة الإفلات
        self.drop_area = tk.Frame(
            drop_frame,
            bg='#2a2a3e',
            relief='solid',
            bd=2,
            width=300,
            height=300
        )
        self.drop_area.pack(fill='both', expand=True, padx=10, pady=10)
        self.drop_area.pack_propagate(False)
        
        # نص الإرشاد
        self.drop_label = tk.Label(
            self.drop_area,
            text="🖼️\n\nاسحب صورة هنا\nأو انقر للاختيار\n\nالتنسيقات المدعومة:\nPNG, JPG, JPEG, BMP, GIF",
            font=('Segoe UI', 11),
            fg='#888888',
            bg='#2a2a3e',
            justify='center'
        )
        self.drop_label.pack(expand=True)
        
        # ربط الأحداث
        self.drop_area.bind("<Button-1>", self.browse_image)
        self.drop_label.bind("<Button-1>", self.browse_image)
        
        # تفعيل السحب والإفلات
        self.setup_drag_drop()
        
        # أزرار سريعة
        buttons_frame = tk.Frame(drop_frame, bg='#16213e')
        buttons_frame.pack(fill='x', padx=10, pady=(0, 10))
        
        browse_btn = tk.Button(
            buttons_frame,
            text="📁 تصفح",
            command=self.browse_image,
            font=('Segoe UI', 9),
            fg='#ffffff',
            bg='#6366f1',
            activebackground='#8b8cf8',
            relief='flat',
            bd=0,
            padx=20,
            pady=5
        )
        browse_btn.pack(side='left', padx=(0, 5))
        
        paste_btn = tk.Button(
            buttons_frame,
            text="📋 لصق",
            command=self.paste_from_clipboard,
            font=('Segoe UI', 9),
            fg='#ffffff',
            bg='#ec4899',
            activebackground='#f472b6',
            relief='flat',
            bd=0,
            padx=20,
            pady=5
        )
        paste_btn.pack(side='left')
    
    def create_editing_tools(self, parent):
        """إنشاء أدوات التحرير"""
        tools_frame = tk.LabelFrame(
            parent,
            text="🛠️ أدوات التحرير",
            font=('Segoe UI', 10, 'bold'),
            fg='#ffffff',
            bg='#16213e',
            bd=2,
            relief='solid'
        )
        tools_frame.pack(side='left', fill='y', padx=5)
        
        # أدوات التحسين التلقائي
        auto_frame = tk.LabelFrame(
            tools_frame,
            text="⚡ تحسين تلقائي",
            font=('Segoe UI', 9),
            fg='#ffffff',
            bg='#16213e'
        )
        auto_frame.pack(fill='x', padx=5, pady=5)
        
        auto_tools = [
            ("🎯 إزالة الخلفية", self.remove_background),
            ("✨ تحسين الجودة", self.enhance_quality),
            ("🔄 تصحيح الاتجاه", self.auto_rotate),
            ("📐 قص ذكي", self.smart_crop)
        ]
        
        for tool_name, command in auto_tools:
            btn = tk.Button(
                auto_frame,
                text=tool_name,
                command=command,
                font=('Segoe UI', 8),
                fg='#ffffff',
                bg='#06b6d4',
                activebackground='#22d3ee',
                relief='flat',
                bd=0,
                width=15,
                pady=3
            )
            btn.pack(pady=2, padx=5, fill='x')
        
        # أدوات التأثيرات
        effects_frame = tk.LabelFrame(
            tools_frame,
            text="🎨 تأثيرات",
            font=('Segoe UI', 9),
            fg='#ffffff',
            bg='#16213e'
        )
        effects_frame.pack(fill='x', padx=5, pady=5)
        
        effects = [
            ("🌟 توهج", self.add_glow),
            ("💎 زجاجي", self.add_glass_effect),
            ("🌈 تدرج", self.add_gradient),
            ("⚡ نيون", self.add_neon_effect)
        ]
        
        for effect_name, command in effects:
            btn = tk.Button(
                effects_frame,
                text=effect_name,
                command=command,
                font=('Segoe UI', 8),
                fg='#ffffff',
                bg='#8b5cf6',
                activebackground='#a78bfa',
                relief='flat',
                bd=0,
                width=15,
                pady=3
            )
            btn.pack(pady=2, padx=5, fill='x')
        
        # أدوات الضبط
        adjust_frame = tk.LabelFrame(
            tools_frame,
            text="⚙️ ضبط",
            font=('Segoe UI', 9),
            fg='#ffffff',
            bg='#16213e'
        )
        adjust_frame.pack(fill='x', padx=5, pady=5)
        
        # السطوع
        brightness_label = tk.Label(
            adjust_frame,
            text="☀️ السطوع:",
            font=('Segoe UI', 8),
            fg='#ffffff',
            bg='#16213e'
        )
        brightness_label.pack(anchor='w', padx=5)
        
        self.brightness_var = tk.DoubleVar(value=1.0)
        brightness_scale = tk.Scale(
            adjust_frame,
            from_=0.5,
            to=2.0,
            resolution=0.1,
            orient='horizontal',
            variable=self.brightness_var,
            bg='#16213e',
            fg='#ffffff',
            highlightthickness=0,
            command=self.apply_adjustments
        )
        brightness_scale.pack(fill='x', padx=5)
        
        # التباين
        contrast_label = tk.Label(
            adjust_frame,
            text="🔆 التباين:",
            font=('Segoe UI', 8),
            fg='#ffffff',
            bg='#16213e'
        )
        contrast_label.pack(anchor='w', padx=5)
        
        self.contrast_var = tk.DoubleVar(value=1.0)
        contrast_scale = tk.Scale(
            adjust_frame,
            from_=0.5,
            to=2.0,
            resolution=0.1,
            orient='horizontal',
            variable=self.contrast_var,
            bg='#16213e',
            fg='#ffffff',
            highlightthickness=0,
            command=self.apply_adjustments
        )
        contrast_scale.pack(fill='x', padx=5)
        
        # التشبع
        saturation_label = tk.Label(
            adjust_frame,
            text="🎨 التشبع:",
            font=('Segoe UI', 8),
            fg='#ffffff',
            bg='#16213e'
        )
        saturation_label.pack(anchor='w', padx=5)
        
        self.saturation_var = tk.DoubleVar(value=1.0)
        saturation_scale = tk.Scale(
            adjust_frame,
            from_=0.0,
            to=2.0,
            resolution=0.1,
            orient='horizontal',
            variable=self.saturation_var,
            bg='#16213e',
            fg='#ffffff',
            highlightthickness=0,
            command=self.apply_adjustments
        )
        saturation_scale.pack(fill='x', padx=5)
        
        # أزرار الإجراءات
        actions_frame = tk.Frame(tools_frame, bg='#16213e')
        actions_frame.pack(fill='x', padx=5, pady=10)
        
        reset_btn = tk.Button(
            actions_frame,
            text="🔄 إعادة تعيين",
            command=self.reset_image,
            font=('Segoe UI', 9),
            fg='#ffffff',
            bg='#ef4444',
            activebackground='#f87171',
            relief='flat',
            bd=0,
            pady=5
        )
        reset_btn.pack(fill='x', pady=2)
        
        convert_btn = tk.Button(
            actions_frame,
            text="🎯 تحويل لأيقونة",
            command=self.convert_to_icon,
            font=('Segoe UI', 9, 'bold'),
            fg='#ffffff',
            bg='#10b981',
            activebackground='#34d399',
            relief='flat',
            bd=0,
            pady=8
        )
        convert_btn.pack(fill='x', pady=5)
    
    def create_preview_area(self, parent):
        """إنشاء منطقة المعاينة"""
        preview_frame = tk.LabelFrame(
            parent,
            text="👁️ معاينة النتيجة",
            font=('Segoe UI', 10, 'bold'),
            fg='#ffffff',
            bg='#16213e',
            bd=2,
            relief='solid'
        )
        preview_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))
        
        # معاينة الصورة الأصلية
        original_frame = tk.LabelFrame(
            preview_frame,
            text="📷 الأصلية",
            font=('Segoe UI', 9),
            fg='#ffffff',
            bg='#16213e'
        )
        original_frame.pack(fill='x', padx=5, pady=5)
        
        self.original_preview = tk.Label(
            original_frame,
            text="لا توجد صورة",
            bg='#2a2a3e',
            fg='#888888',
            width=20,
            height=8
        )
        self.original_preview.pack(padx=5, pady=5)
        
        # معاينة الصورة المعدلة
        processed_frame = tk.LabelFrame(
            preview_frame,
            text="✨ المعدلة",
            font=('Segoe UI', 9),
            fg='#ffffff',
            bg='#16213e'
        )
        processed_frame.pack(fill='x', padx=5, pady=5)
        
        self.processed_preview = tk.Label(
            processed_frame,
            text="لا توجد صورة",
            bg='#2a2a3e',
            fg='#888888',
            width=20,
            height=8
        )
        self.processed_preview.pack(padx=5, pady=5)
        
        # معاينة الأيقونة النهائية
        icon_frame = tk.LabelFrame(
            preview_frame,
            text="🎯 الأيقونة النهائية",
            font=('Segoe UI', 9),
            fg='#ffffff',
            bg='#16213e'
        )
        icon_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # أحجام مختلفة للأيقونة
        sizes_frame = tk.Frame(icon_frame, bg='#16213e')
        sizes_frame.pack(pady=10)
        
        sizes_label = tk.Label(
            sizes_frame,
            text="📏 أحجام الأيقونة:",
            font=('Segoe UI', 9),
            fg='#ffffff',
            bg='#16213e'
        )
        sizes_label.pack()
        
        self.icon_sizes_frame = tk.Frame(sizes_frame, bg='#16213e')
        self.icon_sizes_frame.pack(pady=5)
        
        # معلومات الأيقونة
        self.icon_info_label = tk.Label(
            icon_frame,
            text="ℹ️ قم بتحميل صورة لبدء التحويل",
            font=('Segoe UI', 8),
            fg='#888888',
            bg='#16213e',
            wraplength=200
        )
        self.icon_info_label.pack(pady=10)
        
        # زر الحفظ
        save_btn = tk.Button(
            icon_frame,
            text="💾 حفظ الأيقونة",
            command=self.save_icon,
            font=('Segoe UI', 9),
            fg='#ffffff',
            bg='#f59e0b',
            activebackground='#fbbf24',
            relief='flat',
            bd=0,
            pady=5,
            state='disabled'
        )
        save_btn.pack(pady=5)
        self.save_btn = save_btn

    def setup_drag_drop(self):
        """إعداد السحب والإفلات"""
        # ربط أحداث السحب والإفلات
        self.drop_area.bind("<Enter>", self.on_drag_enter)
        self.drop_area.bind("<Leave>", self.on_drag_leave)

        # محاولة تفعيل السحب والإفلات (يحتاج مكتبة إضافية في بعض الحالات)
        try:
            import tkinterdnd2 as tkdnd
            self.drop_area = tkdnd.Tk()
            self.drop_area.drop_target_register(tkdnd.DND_FILES)
            self.drop_area.dnd_bind('<<Drop>>', self.on_file_drop)
        except ImportError:
            # إذا لم تكن مكتبة السحب والإفلات متوفرة
            pass

    def on_drag_enter(self, event):
        """عند دخول السحب"""
        self.drop_area.configure(bg='#3a3a5e')
        self.drop_label.configure(text="🎯\n\nأفلت الصورة هنا", bg='#3a3a5e')

    def on_drag_leave(self, event):
        """عند مغادرة السحب"""
        self.drop_area.configure(bg='#2a2a3e')
        self.drop_label.configure(
            text="🖼️\n\nاسحب صورة هنا\nأو انقر للاختيار\n\nالتنسيقات المدعومة:\nPNG, JPG, JPEG, BMP, GIF",
            bg='#2a2a3e'
        )

    def on_file_drop(self, event):
        """عند إفلات ملف"""
        files = event.data.split()
        if files:
            file_path = files[0].strip('{}')
            self.load_image(file_path)

    def browse_image(self, event=None):
        """تصفح واختيار صورة"""
        file_path = filedialog.askopenfilename(
            title="اختر صورة",
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.bmp *.gif *.tiff"),
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            self.load_image(file_path)

    def paste_from_clipboard(self):
        """لصق صورة من الحافظة"""
        try:
            from PIL import ImageGrab
            image = ImageGrab.grabclipboard()

            if image:
                self.current_image = image
                self.processed_image = image.copy()
                self.update_previews()
                self.drop_label.configure(text="✅ تم لصق الصورة من الحافظة")
            else:
                messagebox.showwarning("تحذير", "لا توجد صورة في الحافظة")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في لصق الصورة: {e}")

    def load_image(self, file_path):
        """تحميل صورة"""
        try:
            image = Image.open(file_path)

            # تحويل إلى RGBA إذا لزم الأمر
            if image.mode != 'RGBA':
                image = image.convert('RGBA')

            self.current_image = image
            self.processed_image = image.copy()

            # تحديث المعاينات
            self.update_previews()

            # تحديث النص
            filename = os.path.basename(file_path)
            self.drop_label.configure(text=f"✅ تم تحميل:\n{filename}")

            # إعادة تعيين الضبط
            self.reset_adjustments()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الصورة: {e}")

    def update_previews(self):
        """تحديث المعاينات"""
        if self.current_image:
            # معاينة الصورة الأصلية
            original_thumb = self.create_thumbnail(self.current_image, (150, 150))
            original_photo = PhotoImage(original_thumb)
            self.original_preview.configure(image=original_photo, text="")
            self.original_preview.image = original_photo

        if self.processed_image:
            # معاينة الصورة المعدلة
            processed_thumb = self.create_thumbnail(self.processed_image, (150, 150))
            processed_photo = PhotoImage(processed_thumb)
            self.processed_preview.configure(image=processed_photo, text="")
            self.processed_preview.image = processed_photo

    def create_thumbnail(self, image, size):
        """إنشاء صورة مصغرة"""
        thumb = image.copy()
        thumb.thumbnail(size, Image.Resampling.LANCZOS)

        # إنشاء خلفية شطرنجية للشفافية
        bg = Image.new('RGBA', size, (255, 255, 255, 255))

        # رسم نمط شطرنجي
        for x in range(0, size[0], 10):
            for y in range(0, size[1], 10):
                if (x // 10 + y // 10) % 2:
                    for i in range(10):
                        for j in range(10):
                            if x + i < size[0] and y + j < size[1]:
                                bg.putpixel((x + i, y + j), (240, 240, 240, 255))

        # وضع الصورة على الخلفية
        bg.paste(thumb, ((size[0] - thumb.width) // 2, (size[1] - thumb.height) // 2), thumb)

        return bg

    def reset_adjustments(self):
        """إعادة تعيين الضبط"""
        self.brightness_var.set(1.0)
        self.contrast_var.set(1.0)
        self.saturation_var.set(1.0)

    def apply_adjustments(self, value=None):
        """تطبيق التعديلات"""
        if not self.current_image:
            return

        try:
            image = self.current_image.copy()

            # تطبيق السطوع
            if self.brightness_var.get() != 1.0:
                enhancer = ImageEnhance.Brightness(image)
                image = enhancer.enhance(self.brightness_var.get())

            # تطبيق التباين
            if self.contrast_var.get() != 1.0:
                enhancer = ImageEnhance.Contrast(image)
                image = enhancer.enhance(self.contrast_var.get())

            # تطبيق التشبع
            if self.saturation_var.get() != 1.0:
                enhancer = ImageEnhance.Color(image)
                image = enhancer.enhance(self.saturation_var.get())

            self.processed_image = image
            self.update_previews()

        except Exception as e:
            print(f"خطأ في تطبيق التعديلات: {e}")

    def remove_background(self):
        """إزالة الخلفية باستخدام مكتبة rembg"""
        if not self.processed_image:
            return
    
        try:
            # تحويل الصورة إلى صيغة bytes
            img_byte_arr = io.BytesIO()
            self.processed_image.save(img_byte_arr, format='PNG')
            img_byte_arr = img_byte_arr.getvalue()
    
            # إزالة الخلفية باستخدام rembg
            result_bytes = remove(img_byte_arr)
    
            # تحويل النتيجة إلى صورة
            self.processed_image = Image.open(io.BytesIO(result_bytes))
            self.update_previews()
    
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إزالة الخلفية (rembg): {e}")

    def enhance_quality(self):
        """تحسين جودة الصورة"""
        if not self.processed_image:
            return

        try:
            # تحسين الحدة
            enhanced = self.processed_image.filter(ImageFilter.UnsharpMask(radius=1, percent=150, threshold=3))

            # تحسين التباين قليلاً
            enhancer = ImageEnhance.Contrast(enhanced)
            enhanced = enhancer.enhance(1.1)

            # تحسين الألوان
            enhancer = ImageEnhance.Color(enhanced)
            enhanced = enhancer.enhance(1.05)

            self.processed_image = enhanced
            self.update_previews()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحسين الجودة: {e}")

    def auto_rotate(self):
        """تصحيح الاتجاه تلقائياً"""
        if not self.processed_image:
            return

        try:
            # محاولة قراءة معلومات EXIF للاتجاه
            rotated = ImageOps.exif_transpose(self.processed_image)
            self.processed_image = rotated
            self.update_previews()

        except Exception as e:
            print(f"تحذير: لا يمكن تصحيح الاتجاه: {e}")

    def smart_crop(self):
        """قص ذكي للصورة"""
        if not self.processed_image:
            return

        try:
            # العثور على المنطقة غير الشفافة
            bbox = self.processed_image.getbbox()
            if bbox:
                cropped = self.processed_image.crop(bbox)
                self.processed_image = cropped
                self.update_previews()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في القص الذكي: {e}")

    def add_glow(self):
        """إضافة تأثير التوهج"""
        if not self.processed_image:
            return

        try:
            # إنشاء تأثير التوهج
            glow = self.processed_image.copy()
            glow = glow.filter(ImageFilter.GaussianBlur(radius=5))

            # دمج التوهج مع الصورة الأصلية
            result = Image.alpha_composite(glow, self.processed_image)
            self.processed_image = result
            self.update_previews()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إضافة التوهج: {e}")

    def add_glass_effect(self):
        """إضافة تأثير زجاجي"""
        if not self.processed_image:
            return

        try:
            # تقليل الشفافية قليلاً
            glass = self.processed_image.copy()

            if HAS_NUMPY:
                glass_array = np.array(glass)

                # تطبيق تأثير زجاجي (تقليل الشفافية)
                if glass_array.shape[2] == 4:  # RGBA
                    glass_array[:, :, 3] = (glass_array[:, :, 3] * 0.8).astype(np.uint8)

                glass = Image.fromarray(glass_array, 'RGBA')
            else:
                # تأثير بديل بدون numpy
                enhancer = ImageEnhance.Brightness(glass)
                glass = enhancer.enhance(0.9)

            # إضافة توهج خفيف
            glass = glass.filter(ImageFilter.GaussianBlur(radius=1))

            self.processed_image = glass
            self.update_previews()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إضافة التأثير الزجاجي: {e}")

    def add_gradient(self):
        """إضافة تدرج لوني"""
        if not self.processed_image:
            return

        try:
            # إنشاء تدرج لوني
            width, height = self.processed_image.size
            gradient = Image.new('RGBA', (width, height))
            draw = ImageDraw.Draw(gradient)

            # تدرج من الأزرق إلى الوردي
            for y in range(height):
                ratio = y / height
                r = int(102 + (236 - 102) * ratio)  # من الأزرق إلى الوردي
                g = int(102 + (72 - 102) * ratio)
                b = int(241 + (153 - 241) * ratio)
                color = (r, g, b, 100)  # شفافية 100
                draw.line([(0, y), (width, y)], fill=color)

            # دمج التدرج مع الصورة
            result = Image.alpha_composite(self.processed_image, gradient)
            self.processed_image = result
            self.update_previews()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إضافة التدرج: {e}")

    def add_neon_effect(self):
        """إضافة تأثير نيون"""
        if not self.processed_image:
            return

        try:
            # تحسين السطوع والتباين للحصول على تأثير نيون
            neon = self.processed_image.copy()

            # زيادة التباين
            enhancer = ImageEnhance.Contrast(neon)
            neon = enhancer.enhance(1.5)

            # زيادة التشبع
            enhancer = ImageEnhance.Color(neon)
            neon = enhancer.enhance(1.8)

            # إضافة توهج
            glow = neon.filter(ImageFilter.GaussianBlur(radius=3))
            result = Image.alpha_composite(glow, neon)

            self.processed_image = result
            self.update_previews()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إضافة تأثير النيون: {e}")

    def reset_image(self):
        """إعادة تعيين الصورة"""
        if self.current_image:
            self.processed_image = self.current_image.copy()
            self.reset_adjustments()
            self.update_previews()

    def convert_to_icon(self):
        """تحويل الصورة إلى أيقونة"""
        if not self.processed_image:
            messagebox.showwarning("تحذير", "يرجى تحميل صورة أولاً")
            return

        try:
            # إنشاء أيقونة بأحجام متعددة
            sizes = [16, 24, 32, 48, 64, 128, 256]
            icon_images = []

            for size in sizes:
                # تغيير حجم الصورة
                resized = self.processed_image.copy()
                resized.thumbnail((size, size), Image.Resampling.LANCZOS)

                # إنشاء صورة مربعة
                square = Image.new('RGBA', (size, size), (0, 0, 0, 0))

                # وضع الصورة في المنتصف
                x = (size - resized.width) // 2
                y = (size - resized.height) // 2
                square.paste(resized, (x, y), resized)

                icon_images.append(square)

            # عرض المعاينات
            self.display_icon_previews(icon_images)

            # تفعيل زر الحفظ
            self.save_btn.configure(state='normal')

            # حفظ الأيقونات للاستخدام
            self.final_icon_images = icon_images

            # تحديث المعلومات
            self.icon_info_label.configure(
                text=f"✅ تم إنشاء الأيقونة بنجاح!\n📏 {len(sizes)} حجم مختلف\n💾 جاهزة للحفظ"
            )

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحويل الصورة: {e}")

    def display_icon_previews(self, icon_images):
        """عرض معاينات الأيقونة"""
        # مسح المعاينات السابقة
        for widget in self.icon_sizes_frame.winfo_children():
            widget.destroy()

        # عرض الأحجام المختلفة
        sizes = [16, 24, 32, 48, 64]  # أحجام للعرض

        for i, size in enumerate(sizes):
            if i < len(icon_images):
                frame = tk.Frame(self.icon_sizes_frame, bg='#16213e')
                frame.pack(side='left', padx=2)

                # تحويل لـ PhotoImage
                photo = PhotoImage(icon_images[i])

                label = tk.Label(
                    frame,
                    image=photo,
                    bg='white',
                    relief='solid',
                    bd=1
                )
                label.image = photo  # حفظ المرجع
                label.pack()

                size_label = tk.Label(
                    frame,
                    text=f"{size}px",
                    font=('Segoe UI', 7),
                    fg='#ffffff',
                    bg='#16213e'
                )
                size_label.pack()

    def save_icon(self):
        """حفظ الأيقونة"""
        if not hasattr(self, 'final_icon_images') or not self.final_icon_images:
            messagebox.showwarning("تحذير", "لا توجد أيقونة للحفظ")
            return

        file_path = filedialog.asksaveasfilename(
            title="حفظ الأيقونة",
            defaultextension=".ico",
            filetypes=[
                ("Icon files", "*.ico"),
                ("PNG files", "*.png"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                if file_path.endswith('.ico'):
                    # حفظ كملف ICO بأحجام متعددة
                    self.final_icon_images[0].save(
                        file_path,
                        format='ICO',
                        sizes=[(img.width, img.height) for img in self.final_icon_images]
                    )
                else:
                    # حفظ كـ PNG (أكبر حجم)
                    self.final_icon_images[-1].save(file_path, format='PNG')

                messagebox.showinfo("نجح الحفظ", f"تم حفظ الأيقونة في:\n{file_path}")

                # إشعار التطبيق الرئيسي
                if self.callback:
                    self.callback(file_path)

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ الأيقونة: {e}")
