#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد الصور بالذكاء الاصطناعي - AI Image Generator
توليد صور وأيقونات مخصصة بناءً على تحليل الكود
"""

import os
import sys
import json
import requests
import base64
import io
import threading
import time
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
from PIL.ImageTk import PhotoImage
import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import random
import hashlib

@dataclass
class ImagePrompt:
    """وصف لتوليد الصورة"""
    text: str
    style: str
    colors: List[str]
    elements: List[str]
    mood: str
    quality: str = "high"
    size: str = "512x512"

@dataclass
class GeneratedImage:
    """صورة مولدة"""
    prompt: ImagePrompt
    image: Image.Image
    thumbnail: Image.Image
    confidence: float
    source: str  # "ai", "template", "generated"
    metadata: Dict

class SmartImageAnalyzer:
    """محلل ذكي لتوليد أوصاف الصور"""
    
    def __init__(self):
        self.app_type_prompts = {
            'GUI': {
                'base': "modern desktop application icon",
                'elements': ["window", "interface", "buttons", "screen"],
                'style': "clean, professional, modern UI design",
                'colors': ["blue", "white", "gray", "accent colors"]
            },
            'Web': {
                'base': "web application icon",
                'elements': ["globe", "browser", "network", "cloud"],
                'style': "modern web design, responsive",
                'colors': ["blue", "green", "orange", "modern palette"]
            },
            'Game': {
                'base': "gaming application icon",
                'elements': ["controller", "joystick", "gaming", "entertainment"],
                'style': "vibrant, fun, energetic gaming style",
                'colors': ["bright colors", "neon", "purple", "orange"]
            },
            'Data': {
                'base': "data analysis application icon",
                'elements': ["charts", "graphs", "analytics", "statistics"],
                'style': "professional, analytical, clean",
                'colors': ["blue", "green", "data visualization colors"]
            },
            'ML/AI': {
                'base': "artificial intelligence application icon",
                'elements': ["neural network", "brain", "AI", "machine learning"],
                'style': "futuristic, high-tech, innovative",
                'colors': ["blue", "purple", "cyan", "tech colors"]
            },
            'CLI': {
                'base': "command line application icon",
                'elements': ["terminal", "console", "code", "command"],
                'style': "technical, developer-focused",
                'colors': ["black", "green", "white", "terminal colors"]
            }
        }
        
        self.domain_prompts = {
            'finance': {
                'elements': ["money", "banking", "financial", "investment"],
                'style': "professional, trustworthy, corporate",
                'colors': ["green", "gold", "blue", "professional"]
            },
            'education': {
                'elements': ["learning", "education", "academic", "knowledge"],
                'style': "friendly, approachable, educational",
                'colors': ["blue", "orange", "green", "educational"]
            },
            'health': {
                'elements': ["medical", "healthcare", "wellness", "health"],
                'style': "clean, medical, trustworthy",
                'colors': ["blue", "white", "red cross", "medical"]
            },
            'gaming': {
                'elements': ["games", "entertainment", "fun", "play"],
                'style': "vibrant, exciting, entertaining",
                'colors': ["bright", "neon", "rainbow", "gaming"]
            },
            'productivity': {
                'elements': ["productivity", "efficiency", "work", "organization"],
                'style': "clean, organized, professional",
                'colors': ["blue", "gray", "organized palette"]
            },
            'social': {
                'elements': ["social", "communication", "people", "community"],
                'style': "friendly, social, connecting",
                'colors': ["blue", "purple", "social colors"]
            }
        }
    
    def generate_image_prompts(self, analysis, count=6):
        """توليد أوصاف الصور بناءً على التحليل"""
        prompts = []
        
        # الحصول على معلومات نوع التطبيق
        app_info = self.app_type_prompts.get(analysis.app_type, self.app_type_prompts['GUI'])
        domain_info = self.domain_prompts.get(analysis.domain, {})
        
        # توليد أوصاف متنوعة
        styles = [
            "minimalist flat design",
            "modern 3D rendered",
            "professional corporate style",
            "vibrant colorful design",
            "elegant sophisticated",
            "playful cartoon style"
        ]
        
        for i in range(count):
            style = styles[i % len(styles)]
            
            # بناء الوصف الأساسي
            base_prompt = app_info['base']
            
            # إضافة معلومات المجال
            if domain_info:
                base_prompt += f" for {analysis.domain} application"
            
            # إضافة الكلمات المفتاحية
            if analysis.keywords:
                key_words = ", ".join(analysis.keywords[:3])
                base_prompt += f" related to {key_words}"
            
            # إضافة النمط
            full_prompt = f"{base_prompt}, {style}, high quality icon design"
            
            # إنشاء كائن ImagePrompt
            prompt = ImagePrompt(
                text=full_prompt,
                style=style,
                colors=app_info.get('colors', []) + domain_info.get('colors', []),
                elements=app_info.get('elements', []) + domain_info.get('elements', []),
                mood=domain_info.get('style', app_info.get('style', 'modern')),
                quality="high",
                size="512x512"
            )
            
            prompts.append(prompt)
        
        return prompts

class AIImageGenerator:
    """مولد الصور بالذكاء الاصطناعي"""
    
    def __init__(self, callback=None):
        self.callback = callback
        self.analyzer = SmartImageAnalyzer()
        self.generated_images = []
        self.cache_dir = "ai_cache"
        
        # إنشاء مجلد التخزين المؤقت
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
        
        # APIs للذكاء الاصطناعي (مع معالجة الأخطاء)
        self.ai_services = {
            'local_generation': True,  # توليد محلي كبديل
            'template_based': True,    # قوالب جاهزة
            'procedural': True         # توليد إجرائي
        }
    
    def log(self, message):
        """إرسال رسالة للواجهة"""
        if self.callback:
            self.callback(message)
        else:
            print(message)
    
    def generate_images_for_analysis(self, analysis, count=6):
        """توليد صور بناءً على التحليل"""
        self.log("🎨 بدء توليد الصور بالذكاء الاصطناعي...")
        
        # توليد أوصاف الصور
        prompts = self.analyzer.generate_image_prompts(analysis, count)
        
        generated_images = []
        
        for i, prompt in enumerate(prompts):
            self.log(f"🖼️ توليد صورة {i+1}/{count}: {prompt.style}")
            
            try:
                # محاولة توليد الصورة بطرق متعددة
                image = self._generate_single_image(prompt, analysis)
                
                if image:
                    # إنشاء صورة مصغرة
                    thumbnail = image.copy()
                    thumbnail.thumbnail((128, 128), Image.Resampling.LANCZOS)
                    
                    # إنشاء كائن GeneratedImage
                    generated_img = GeneratedImage(
                        prompt=prompt,
                        image=image,
                        thumbnail=thumbnail,
                        confidence=0.8 - (i * 0.1),
                        source="ai_generated",
                        metadata={
                            'app_type': analysis.app_type,
                            'domain': analysis.domain,
                            'style': prompt.style,
                            'generated_at': time.time()
                        }
                    )
                    
                    generated_images.append(generated_img)
                    
            except Exception as e:
                self.log(f"❌ خطأ في توليد الصورة {i+1}: {e}")
                continue
        
        self.generated_images = generated_images
        self.log(f"✅ تم توليد {len(generated_images)} صورة بنجاح")
        
        return generated_images
    
    def _generate_single_image(self, prompt, analysis):
        """توليد صورة واحدة"""
        # محاولة طرق متعددة للتوليد
        
        # 1. التوليد المحلي المتقدم
        try:
            return self._generate_local_advanced(prompt, analysis)
        except Exception as e:
            self.log(f"فشل التوليد المحلي: {e}")
        
        # 2. التوليد من القوالب
        try:
            return self._generate_from_template(prompt, analysis)
        except Exception as e:
            self.log(f"فشل التوليد من القوالب: {e}")
        
        # 3. التوليد الإجرائي
        try:
            return self._generate_procedural(prompt, analysis)
        except Exception as e:
            self.log(f"فشل التوليد الإجرائي: {e}")
        
        return None
    
    def _generate_local_advanced(self, prompt, analysis):
        """توليد محلي متقدم"""
        # إنشاء صورة 512x512
        size = 512
        image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(image)
        
        # اختيار ألوان بناءً على التحليل
        colors = self._get_smart_colors(analysis, prompt)
        
        # رسم خلفية متدرجة
        self._draw_gradient_background(draw, size, colors)
        
        # إضافة عناصر بناءً على نوع التطبيق
        self._add_app_elements(draw, analysis, size, colors)
        
        # إضافة تأثيرات بناءً على النمط
        image = self._apply_style_effects(image, prompt.style)
        
        return image
    
    def _generate_from_template(self, prompt, analysis):
        """توليد من قوالب جاهزة"""
        # قوالب أساسية لكل نوع تطبيق
        templates = {
            'GUI': self._create_gui_template,
            'Web': self._create_web_template,
            'Game': self._create_game_template,
            'Data': self._create_data_template,
            'ML/AI': self._create_ai_template,
            'CLI': self._create_cli_template
        }
        
        template_func = templates.get(analysis.app_type, self._create_default_template)
        return template_func(prompt, analysis)
    
    def _generate_procedural(self, prompt, analysis):
        """توليد إجرائي متقدم"""
        size = 512
        image = Image.new('RGBA', (size, size), (255, 255, 255, 0))
        draw = ImageDraw.Draw(image)
        
        # توليد أشكال هندسية بناءً على التحليل
        shapes = self._generate_smart_shapes(analysis, size)
        
        # رسم الأشكال
        colors = self._get_smart_colors(analysis, prompt)
        for shape in shapes:
            self._draw_shape(draw, shape, colors)
        
        # إضافة تفاصيل
        self._add_procedural_details(draw, analysis, size, colors)
        
        return image
    
    def _get_smart_colors(self, analysis, prompt):
        """الحصول على ألوان ذكية"""
        # ألوان افتراضية حسب نوع التطبيق
        app_colors = {
            'GUI': ['#4169E1', '#32CD32', '#FF6347'],
            'Web': ['#FF4500', '#1E90FF', '#32CD32'],
            'Game': ['#8A2BE2', '#FF1493', '#00CED1'],
            'Data': ['#2E8B57', '#4682B4', '#FF8C00'],
            'ML/AI': ['#6366f1', '#8b5cf6', '#06b6d4'],
            'CLI': ['#2F4F4F', '#00FF00', '#FFFFFF']
        }
        
        # ألوان حسب المجال
        domain_colors = {
            'finance': ['#2E8B57', '#FFD700', '#1E90FF'],
            'education': ['#4169E1', '#FF6347', '#32CD32'],
            'health': ['#DC143C', '#00FA9A', '#4682B4'],
            'gaming': ['#FF4500', '#8A2BE2', '#00CED1'],
            'productivity': ['#696969', '#FF8C00', '#6495ED'],
            'social': ['#FF69B4', '#00BFFF', '#32CD32']
        }
        
        colors = app_colors.get(analysis.app_type, app_colors['GUI'])
        if analysis.domain in domain_colors:
            colors.extend(domain_colors[analysis.domain])
        
        return colors[:3]  # أهم 3 ألوان
    
    def _draw_gradient_background(self, draw, size, colors):
        """رسم خلفية متدرجة"""
        color1 = self._hex_to_rgb(colors[0])
        color2 = self._hex_to_rgb(colors[1]) if len(colors) > 1 else color1
        
        for y in range(size):
            ratio = y / size
            r = int(color1[0] + (color2[0] - color1[0]) * ratio)
            g = int(color1[1] + (color2[1] - color1[1]) * ratio)
            b = int(color1[2] + (color2[2] - color1[2]) * ratio)
            color = (r, g, b, 100)  # شفافية خفيفة
            draw.line([(0, y), (size, y)], fill=color)
    
    def _add_app_elements(self, draw, analysis, size, colors):
        """إضافة عناصر حسب نوع التطبيق"""
        center = size // 2
        
        if analysis.app_type == 'GUI':
            # نافذة تطبيق
            self._draw_window_icon(draw, center, size//3, colors)
        elif analysis.app_type == 'Web':
            # كرة أرضية
            self._draw_globe_icon(draw, center, size//3, colors)
        elif analysis.app_type == 'Game':
            # يد تحكم
            self._draw_gamepad_icon(draw, center, size//3, colors)
        elif analysis.app_type == 'Data':
            # مخطط بياني
            self._draw_chart_icon(draw, center, size//3, colors)
        elif analysis.app_type == 'ML/AI':
            # شبكة عصبية
            self._draw_neural_network_icon(draw, center, size//3, colors)
        else:
            # أيقونة افتراضية
            self._draw_default_icon(draw, center, size//3, colors)
    
    def _draw_window_icon(self, draw, center, radius, colors):
        """رسم أيقونة نافذة"""
        color = self._hex_to_rgb(colors[0])
        # نافذة رئيسية
        draw.rectangle([center-radius, center-radius//2, center+radius, center+radius], 
                      fill=color, outline=(0, 0, 0), width=3)
        # شريط العنوان
        title_color = self._hex_to_rgb(colors[1]) if len(colors) > 1 else color
        draw.rectangle([center-radius, center-radius//2, center+radius, center-radius//4], 
                      fill=title_color)
        # أزرار النافذة
        btn_size = radius // 8
        for i, btn_color in enumerate(['#ff5f56', '#ffbd2e', '#27ca3f']):
            x = center + radius - (i + 1) * (btn_size * 2 + 5)
            y = center - radius//2 + btn_size
            draw.ellipse([x, y, x + btn_size, y + btn_size], fill=btn_color)
    
    def _draw_globe_icon(self, draw, center, radius, colors):
        """رسم أيقونة كرة أرضية"""
        color = self._hex_to_rgb(colors[0])
        # الكرة الأرضية
        draw.ellipse([center-radius, center-radius, center+radius, center+radius], 
                    fill=color, outline=(0, 0, 0), width=3)
        # خطوط الطول والعرض
        line_color = self._hex_to_rgb(colors[1]) if len(colors) > 1 else (255, 255, 255)
        draw.arc([center-radius//2, center-radius//2, center+radius//2, center+radius//2], 
                0, 180, fill=line_color, width=2)
        draw.line([center, center-radius, center, center+radius], fill=line_color, width=2)
    
    def _draw_gamepad_icon(self, draw, center, radius, colors):
        """رسم أيقونة يد تحكم"""
        color = self._hex_to_rgb(colors[0])
        # جسم يد التحكم
        draw.ellipse([center-radius, center-radius//2, center+radius, center+radius//2], 
                    fill=color, outline=(0, 0, 0), width=3)
        # الأزرار
        btn_color = self._hex_to_rgb(colors[1]) if len(colors) > 1 else (255, 255, 255)
        # أزرار يسار
        draw.ellipse([center-radius//2, center-radius//4, center-radius//3, center], fill=btn_color)
        # أزرار يمين
        draw.ellipse([center+radius//3, center-radius//4, center+radius//2, center], fill=btn_color)
    
    def _draw_chart_icon(self, draw, center, radius, colors):
        """رسم أيقونة مخطط بياني"""
        # خلفية المخطط
        bg_color = self._hex_to_rgb(colors[0])
        draw.rectangle([center-radius, center-radius//2, center+radius, center+radius], 
                      fill=bg_color, outline=(0, 0, 0), width=2)
        
        # أعمدة البيانات
        bar_colors = [self._hex_to_rgb(c) for c in colors[1:]] if len(colors) > 1 else [(255, 255, 255)]
        bar_width = radius // 4
        heights = [radius//3, radius//2, radius//4, radius//1.5]
        
        for i, height in enumerate(heights[:3]):
            x = center - radius + (i + 1) * bar_width
            color = bar_colors[i % len(bar_colors)]
            draw.rectangle([x, center + radius - height, x + bar_width//2, center + radius], 
                          fill=color)
    
    def _draw_neural_network_icon(self, draw, center, radius, colors):
        """رسم أيقونة شبكة عصبية"""
        color = self._hex_to_rgb(colors[0])
        node_color = self._hex_to_rgb(colors[1]) if len(colors) > 1 else color
        
        # عقد الشبكة
        nodes = [
            (center - radius//2, center - radius//2),
            (center, center - radius//2),
            (center + radius//2, center - radius//2),
            (center - radius//3, center + radius//2),
            (center + radius//3, center + radius//2)
        ]
        
        # رسم الاتصالات
        for i, node1 in enumerate(nodes[:3]):
            for node2 in nodes[3:]:
                draw.line([node1, node2], fill=color, width=2)
        
        # رسم العقد
        for node in nodes:
            draw.ellipse([node[0]-10, node[1]-10, node[0]+10, node[1]+10], 
                        fill=node_color, outline=(0, 0, 0), width=2)
    
    def _draw_default_icon(self, draw, center, radius, colors):
        """رسم أيقونة افتراضية"""
        color = self._hex_to_rgb(colors[0])
        # شكل هندسي بسيط
        draw.ellipse([center-radius, center-radius, center+radius, center+radius], 
                    fill=color, outline=(0, 0, 0), width=3)
        # رمز في المنتصف
        symbol_color = self._hex_to_rgb(colors[1]) if len(colors) > 1 else (255, 255, 255)
        draw.ellipse([center-radius//3, center-radius//3, center+radius//3, center+radius//3], 
                    fill=symbol_color)
    
    def _hex_to_rgb(self, hex_color):
        """تحويل من hex إلى RGB"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def _apply_style_effects(self, image, style):
        """تطبيق تأثيرات النمط"""
        if "3D" in style:
            # تأثير ثلاثي الأبعاد
            image = image.filter(ImageFilter.EMBOSS)
        elif "minimalist" in style:
            # تأثير بسيط
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.2)
        elif "vibrant" in style:
            # تأثير زاهي
            enhancer = ImageEnhance.Color(image)
            image = enhancer.enhance(1.5)
        elif "elegant" in style:
            # تأثير أنيق
            image = image.filter(ImageFilter.SMOOTH)
        
        return image
