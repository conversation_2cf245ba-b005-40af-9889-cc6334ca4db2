#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مثبت المتطلبات التلقائي لـ Python to EXE Converter Pro v2.0
Automatic requirements installer for Python to EXE Converter Pro v2.0
"""

import subprocess
import sys
import os
import importlib.util

def check_python_version():
    """التحقق من إصدار Python"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ خطأ: يتطلب Python 3.7 أو أحدث")
        print(f"📊 الإصدار الحالي: {version.major}.{version.minor}.{version.micro}")
        print("💡 يرجى تحديث Python من: https://python.org")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - متوافق")
    return True

def check_pip():
    """التحقق من وجود pip"""
    try:
        import pip
        print("✅ pip متوفر")
        return True
    except ImportError:
        print("❌ pip غير متوفر")
        print("💡 يرجى تثبيت pip أولاً")
        return False

def install_package(package_name, display_name=None):
    """تثبيت حزمة Python"""
    display_name = display_name or package_name
    
    try:
        # التحقق من وجود الحزمة
        spec = importlib.util.find_spec(package_name.split('>=')[0].split('==')[0])
        if spec is not None:
            print(f"✅ {display_name} مثبت مسبقاً")
            return True
    except ImportError:
        pass
    
    print(f"🔄 جاري تثبيت {display_name}...")
    
    try:
        # تشغيل pip install
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", package_name],
            capture_output=True,
            text=True,
            check=True
        )
        
        print(f"✅ تم تثبيت {display_name} بنجاح")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت {display_name}")
        print(f"📝 تفاصيل الخطأ: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع في تثبيت {display_name}: {e}")
        return False

def upgrade_pip():
    """تحديث pip"""
    print("🔄 جاري تحديث pip...")
    try:
        subprocess.run(
            [sys.executable, "-m", "pip", "install", "--upgrade", "pip"],
            capture_output=True,
            text=True,
            check=True
        )
        print("✅ تم تحديث pip بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("⚠️ تحذير: فشل في تحديث pip (قد يعمل التطبيق بدون تحديث)")
        return False

def check_required_files():
    """التحقق من وجود الملفات المطلوبة"""
    required_files = [
        "pyinstaller_gui.py",
        "glass_theme.py",
        "README.md"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   📄 {file}")
        return False
    
    print("✅ جميع الملفات المطلوبة موجودة")
    return True

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🚀 Python to EXE Converter Pro v2.0")
    print("📦 مثبت المتطلبات التلقائي")
    print("=" * 60)
    print()
    
    # التحقق من إصدار Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return False
    
    # التحقق من pip
    if not check_pip():
        input("اضغط Enter للخروج...")
        return False
    
    # تحديث pip
    upgrade_pip()
    print()
    
    # قائمة المتطلبات
    requirements = [
        ("PyInstaller>=5.0.0", "PyInstaller - محول Python إلى EXE"),
        ("Pillow>=9.0.0", "Pillow - معالج الصور (اختياري)")
    ]
    
    print("📦 بدء تثبيت المتطلبات...")
    print()
    
    success_count = 0
    total_count = len(requirements)
    
    for package, description in requirements:
        if install_package(package, description):
            success_count += 1
        print()
    
    # التحقق من الملفات
    print("📁 التحقق من الملفات...")
    files_ok = check_required_files()
    print()
    
    # النتيجة النهائية
    print("=" * 60)
    print("📊 ملخص التثبيت:")
    print(f"✅ تم تثبيت: {success_count}/{total_count} حزمة")
    
    if success_count == total_count and files_ok:
        print("🎉 تم التثبيت بنجاح! التطبيق جاهز للاستخدام")
        print()
        print("🚀 لتشغيل التطبيق:")
        print("   • Windows: انقر مرتين على run.bat")
        print("   • أو شغل: python pyinstaller_gui.py")
        print()
        return True
    else:
        print("⚠️ التثبيت غير مكتمل - قد تواجه مشاكل في التشغيل")
        print()
        if success_count < total_count:
            print("💡 جرب تشغيل الأوامر التالية يدوياً:")
            for package, description in requirements:
                print(f"   pip install {package}")
        print()
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("❌ فشل في إكمال التثبيت")
    except KeyboardInterrupt:
        print("\n⏹️ تم إلغاء التثبيت بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
    finally:
        input("\nاضغط Enter للخروج...")
