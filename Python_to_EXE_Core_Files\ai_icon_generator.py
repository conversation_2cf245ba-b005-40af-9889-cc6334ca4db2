#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الذكاء الاصطناعي لتصميم الأيقونات - AI Icon Generator
تحليل الكود وتوليد أيقونات ذكية مع محرر تفاعلي
"""

import os
import sys
import ast
import re
import json
import requests
import base64
import io
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
from PIL.ImageTk import PhotoImage
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser
import threading
import time
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

@dataclass
class CodeAnalysis:
    """نتائج تحليل الكود"""
    app_type: str = "unknown"  # GUI, CLI, Web, Game, Data, etc.
    domain: str = "general"    # finance, gaming, education, etc.
    keywords: List[str] = None
    functions: List[str] = None
    libraries: List[str] = None
    ui_elements: List[str] = None
    colors_suggested: List[str] = None
    style_suggested: str = "modern"
    
    def __post_init__(self):
        if self.keywords is None:
            self.keywords = []
        if self.functions is None:
            self.functions = []
        if self.libraries is None:
            self.libraries = []
        if self.ui_elements is None:
            self.ui_elements = []
        if self.colors_suggested is None:
            self.colors_suggested = []

@dataclass
class IconSuggestion:
    """اقتراح أيقونة"""
    name: str
    description: str
    style: str
    colors: List[str]
    elements: List[str]
    confidence: float
    preview_url: str = ""
    local_path: str = ""

class SmartCodeAnalyzer:
    """محلل الكود الذكي"""
    
    def __init__(self):
        self.app_type_patterns = {
            'GUI': ['tkinter', 'PyQt', 'PySide', 'kivy', 'wxpython', 'gtk'],
            'Web': ['flask', 'django', 'fastapi', 'tornado', 'bottle', 'pyramid'],
            'Game': ['pygame', 'panda3d', 'arcade', 'pyglet'],
            'Data': ['pandas', 'numpy', 'matplotlib', 'seaborn', 'plotly', 'scipy'],
            'ML/AI': ['tensorflow', 'pytorch', 'sklearn', 'keras', 'opencv'],
            'Network': ['socket', 'requests', 'urllib', 'aiohttp', 'twisted'],
            'Database': ['sqlite3', 'pymongo', 'sqlalchemy', 'psycopg2'],
            'CLI': ['argparse', 'click', 'fire', 'typer'],
            'Image': ['PIL', 'opencv', 'skimage', 'imageio'],
            'Audio': ['pygame', 'pydub', 'librosa', 'soundfile'],
            'File': ['os', 'pathlib', 'shutil', 'glob', 'zipfile']
        }
        
        self.domain_keywords = {
            'finance': ['money', 'bank', 'payment', 'crypto', 'trading', 'stock'],
            'education': ['learn', 'study', 'course', 'quiz', 'exam', 'school'],
            'gaming': ['game', 'player', 'score', 'level', 'enemy', 'weapon'],
            'health': ['medical', 'health', 'patient', 'doctor', 'medicine'],
            'business': ['company', 'employee', 'customer', 'order', 'invoice'],
            'social': ['user', 'friend', 'message', 'chat', 'post', 'share'],
            'productivity': ['task', 'todo', 'calendar', 'note', 'reminder'],
            'media': ['video', 'audio', 'image', 'photo', 'music', 'movie'],
            'security': ['password', 'encrypt', 'secure', 'auth', 'login'],
            'science': ['data', 'analysis', 'research', 'experiment', 'lab']
        }
        
        self.color_schemes = {
            'finance': ['#2E8B57', '#FFD700', '#1E90FF'],  # أخضر، ذهبي، أزرق
            'education': ['#4169E1', '#FF6347', '#32CD32'],  # أزرق، أحمر، أخضر
            'gaming': ['#FF4500', '#8A2BE2', '#00CED1'],     # برتقالي، بنفسجي، سماوي
            'health': ['#DC143C', '#00FA9A', '#4682B4'],     # أحمر، أخضر فاتح، أزرق
            'business': ['#2F4F4F', '#B8860B', '#4682B4'],   # رمادي، ذهبي، أزرق
            'social': ['#FF69B4', '#00BFFF', '#32CD32'],     # وردي، أزرق فاتح، أخضر
            'productivity': ['#696969', '#FF8C00', '#6495ED'], # رمادي، برتقالي، أزرق
            'media': ['#8B008B', '#FF1493', '#00CED1'],      # بنفسجي، وردي، سماوي
            'security': ['#8B0000', '#2F4F4F', '#FFD700'],   # أحمر غامق، رمادي، ذهبي
            'science': ['#4169E1', '#32CD32', '#FF6347']     # أزرق، أخضر، أحمر
        }
    
    def analyze_code(self, file_path_or_content, is_content=False):
        """تحليل الكود الذكي"""
        analysis = CodeAnalysis()
        
        try:
            if is_content:
                content = file_path_or_content
                file_path = "content"
            else:
                if os.path.isfile(file_path_or_content):
                    with open(file_path_or_content, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    file_path = file_path_or_content
                else:
                    # مجلد - تحليل جميع ملفات Python
                    content = self._analyze_directory(file_path_or_content)
                    file_path = file_path_or_content
            
            # تحليل المكتبات المستخدمة
            analysis.libraries = self._extract_libraries(content)
            
            # تحديد نوع التطبيق
            analysis.app_type = self._determine_app_type(analysis.libraries)
            
            # استخراج الوظائف والكلمات المفتاحية
            analysis.functions = self._extract_functions(content)
            analysis.keywords = self._extract_keywords(content, analysis.functions)
            
            # تحديد المجال
            analysis.domain = self._determine_domain(analysis.keywords, analysis.libraries)
            
            # اقتراح الألوان
            analysis.colors_suggested = self._suggest_colors(analysis.domain, analysis.app_type)
            
            # تحديد النمط
            analysis.style_suggested = self._suggest_style(analysis.app_type, analysis.domain)
            
            # تحليل عناصر الواجهة
            analysis.ui_elements = self._extract_ui_elements(content, analysis.app_type)
            
        except Exception as e:
            print(f"خطأ في تحليل الكود: {e}")
        
        return analysis
    
    def _analyze_directory(self, directory_path):
        """تحليل جميع ملفات Python في مجلد"""
        content = ""
        for root, dirs, files in os.walk(directory_path):
            for file in files:
                if file.endswith('.py'):
                    try:
                        file_path = os.path.join(root, file)
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content += f.read() + "\n"
                    except:
                        continue
        return content
    
    def _extract_libraries(self, content):
        """استخراج المكتبات المستخدمة"""
        libraries = set()
        
        # تحليل import statements
        import_patterns = [
            r'^\s*import\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            r'^\s*from\s+([a-zA-Z_][a-zA-Z0-9_]*)\s+import',
        ]
        
        for pattern in import_patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                lib_name = match.group(1).lower()
                libraries.add(lib_name)
        
        return list(libraries)
    
    def _determine_app_type(self, libraries):
        """تحديد نوع التطبيق"""
        scores = {}
        
        for app_type, type_libs in self.app_type_patterns.items():
            score = 0
            for lib in libraries:
                if any(type_lib.lower() in lib.lower() for type_lib in type_libs):
                    score += 1
            scores[app_type] = score
        
        if scores:
            return max(scores, key=scores.get)
        return "general"
    
    def _extract_functions(self, content):
        """استخراج أسماء الوظائف"""
        functions = []
        
        try:
            tree = ast.parse(content)
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    functions.append(node.name)
                elif isinstance(node, ast.ClassDef):
                    functions.append(node.name)
        except:
            # استخدام regex كبديل
            func_pattern = r'def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\('
            class_pattern = r'class\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*[\(:]'
            
            functions.extend(re.findall(func_pattern, content))
            functions.extend(re.findall(class_pattern, content))
        
        return functions
    
    def _extract_keywords(self, content, functions):
        """استخراج الكلمات المفتاحية"""
        keywords = set()
        
        # إضافة أسماء الوظائف
        keywords.update(functions)
        
        # البحث عن كلمات مفتاحية في التعليقات والنصوص
        comment_pattern = r'#\s*(.+)'
        string_pattern = r'["\']([^"\']{3,})["\']'
        
        for pattern in [comment_pattern, string_pattern]:
            matches = re.findall(pattern, content)
            for match in matches:
                words = re.findall(r'\b[a-zA-Z]{3,}\b', match.lower())
                keywords.update(words)
        
        # تنظيف الكلمات المفتاحية
        filtered_keywords = []
        common_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'man', 'end', 'few', 'got', 'let', 'put', 'say', 'she', 'too', 'use'}
        
        for keyword in keywords:
            if len(keyword) >= 3 and keyword not in common_words:
                filtered_keywords.append(keyword)
        
        return filtered_keywords[:20]  # أهم 20 كلمة
    
    def _determine_domain(self, keywords, libraries):
        """تحديد مجال التطبيق"""
        scores = {}
        
        for domain, domain_words in self.domain_keywords.items():
            score = 0
            
            # نقاط من الكلمات المفتاحية
            for keyword in keywords:
                if any(word in keyword.lower() for word in domain_words):
                    score += 2
            
            # نقاط من المكتبات
            for lib in libraries:
                if any(word in lib.lower() for word in domain_words):
                    score += 3
            
            scores[domain] = score
        
        if scores and max(scores.values()) > 0:
            return max(scores, key=scores.get)
        return "general"
    
    def _suggest_colors(self, domain, app_type):
        """اقتراح الألوان"""
        colors = []
        
        # ألوان حسب المجال
        if domain in self.color_schemes:
            colors.extend(self.color_schemes[domain])
        
        # ألوان حسب نوع التطبيق
        type_colors = {
            'GUI': ['#4169E1', '#32CD32', '#FF6347'],
            'Web': ['#FF4500', '#1E90FF', '#32CD32'],
            'Game': ['#8A2BE2', '#FF1493', '#00CED1'],
            'Data': ['#2E8B57', '#4682B4', '#FF8C00'],
            'CLI': ['#2F4F4F', '#B8860B', '#DC143C']
        }
        
        if app_type in type_colors:
            colors.extend(type_colors[app_type])
        
        # إزالة التكرار والحد الأقصى
        return list(set(colors))[:6]
    
    def _suggest_style(self, app_type, domain):
        """اقتراح نمط التصميم"""
        if app_type == 'Game':
            return 'cartoon'
        elif app_type in ['Data', 'ML/AI']:
            return 'technical'
        elif domain == 'business':
            return 'professional'
        elif domain == 'education':
            return 'friendly'
        else:
            return 'modern'
    
    def _extract_ui_elements(self, content, app_type):
        """استخراج عناصر الواجهة"""
        elements = []
        
        if app_type == 'GUI':
            ui_patterns = {
                'button': r'\b(button|btn)\b',
                'window': r'\b(window|frame|dialog)\b',
                'menu': r'\b(menu|menubar)\b',
                'text': r'\b(text|label|entry)\b',
                'image': r'\b(image|icon|photo)\b',
                'list': r'\b(list|tree|table)\b'
            }
            
            for element, pattern in ui_patterns.items():
                if re.search(pattern, content, re.IGNORECASE):
                    elements.append(element)
        
        return elements

class AIIconGenerator:
    """مولد الأيقونات بالذكاء الاصطناعي"""
    
    def __init__(self, callback=None):
        self.callback = callback
        self.analyzer = SmartCodeAnalyzer()
        self.icon_cache = {}
        
        # APIs للذكاء الاصطناعي (يمكن إضافة مفاتيح حقيقية)
        self.ai_apis = {
            'openai': {
                'url': 'https://api.openai.com/v1/images/generations',
                'key': 'your-openai-key-here'
            },
            'stability': {
                'url': 'https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image',
                'key': 'your-stability-key-here'
            }
        }
        
        # مكتبات الأيقونات المجانية
        self.icon_libraries = {
            'flaticon': 'https://api.flaticon.com/v3/search/icons',
            'icons8': 'https://api.icons8.com/api/iconsets/v5/search',
            'iconify': 'https://api.iconify.design/search'
        }
    
    def log(self, message):
        """إرسال رسالة للواجهة"""
        if self.callback:
            self.callback(message)
        else:
            print(message)
    
    def generate_icon_suggestions(self, code_path_or_content, is_content=False):
        """توليد اقتراحات الأيقونات"""
        self.log("🧠 بدء تحليل الكود...")
        
        # تحليل الكود
        analysis = self.analyzer.analyze_code(code_path_or_content, is_content)
        
        self.log(f"📊 نوع التطبيق: {analysis.app_type}")
        self.log(f"🎯 المجال: {analysis.domain}")
        self.log(f"🔑 الكلمات المفتاحية: {', '.join(analysis.keywords[:5])}")
        
        # توليد اقتراحات
        suggestions = []
        
        # اقتراحات بناءً على نوع التطبيق
        suggestions.extend(self._generate_type_based_suggestions(analysis))
        
        # اقتراحات بناءً على المجال
        suggestions.extend(self._generate_domain_based_suggestions(analysis))
        
        # اقتراحات بناءً على الكلمات المفتاحية
        suggestions.extend(self._generate_keyword_based_suggestions(analysis))
        
        self.log(f"✨ تم توليد {len(suggestions)} اقتراح")
        
        return suggestions, analysis
    
    def _generate_type_based_suggestions(self, analysis):
        """اقتراحات بناءً على نوع التطبيق"""
        suggestions = []
        
        type_suggestions = {
            'GUI': [
                IconSuggestion("Desktop App", "أيقونة تطبيق سطح المكتب", "modern", analysis.colors_suggested[:3], ["window", "computer"], 0.9),
                IconSuggestion("Interface", "واجهة تفاعلية", "clean", analysis.colors_suggested[:2], ["screen", "cursor"], 0.8)
            ],
            'Web': [
                IconSuggestion("Web App", "تطبيق ويب", "modern", ["#FF4500", "#1E90FF"], ["globe", "browser"], 0.9),
                IconSuggestion("Server", "خادم ويب", "technical", ["#2F4F4F", "#4682B4"], ["server", "cloud"], 0.8)
            ],
            'Game': [
                IconSuggestion("Game Controller", "يد تحكم", "cartoon", ["#8A2BE2", "#FF1493"], ["gamepad", "joystick"], 0.9),
                IconSuggestion("Gaming", "ألعاب", "fun", ["#00CED1", "#FF4500"], ["dice", "star"], 0.8)
            ],
            'Data': [
                IconSuggestion("Data Analysis", "تحليل البيانات", "technical", ["#2E8B57", "#4682B4"], ["chart", "graph"], 0.9),
                IconSuggestion("Statistics", "إحصائيات", "professional", ["#FF8C00", "#4169E1"], ["bar-chart", "pie-chart"], 0.8)
            ]
        }
        
        if analysis.app_type in type_suggestions:
            suggestions.extend(type_suggestions[analysis.app_type])
        
        return suggestions
    
    def _generate_domain_based_suggestions(self, analysis):
        """اقتراحات بناءً على المجال"""
        suggestions = []
        
        domain_suggestions = {
            'finance': [
                IconSuggestion("Money", "مال ومالية", "professional", ["#2E8B57", "#FFD700"], ["dollar", "coin"], 0.9),
                IconSuggestion("Banking", "خدمات مصرفية", "clean", ["#1E90FF", "#2E8B57"], ["bank", "card"], 0.8)
            ],
            'education': [
                IconSuggestion("Education", "تعليم", "friendly", ["#4169E1", "#FF6347"], ["book", "graduation"], 0.9),
                IconSuggestion("Learning", "تعلم", "modern", ["#32CD32", "#FF8C00"], ["lightbulb", "brain"], 0.8)
            ],
            'gaming': [
                IconSuggestion("Gaming", "ألعاب", "fun", ["#FF4500", "#8A2BE2"], ["gamepad", "trophy"], 0.9),
                IconSuggestion("Entertainment", "ترفيه", "cartoon", ["#00CED1", "#FF1493"], ["star", "music"], 0.8)
            ]
        }
        
        if analysis.domain in domain_suggestions:
            suggestions.extend(domain_suggestions[analysis.domain])
        
        return suggestions
    
    def _generate_keyword_based_suggestions(self, analysis):
        """اقتراحات بناءً على الكلمات المفتاحية"""
        suggestions = []
        
        # تحليل الكلمات المفتاحية وتوليد اقتراحات
        for keyword in analysis.keywords[:3]:  # أهم 3 كلمات
            suggestion = IconSuggestion(
                name=f"Custom {keyword.title()}",
                description=f"أيقونة مخصصة لـ {keyword}",
                style=analysis.style_suggested,
                colors=analysis.colors_suggested[:2],
                elements=[keyword],
                confidence=0.7
            )
            suggestions.append(suggestion)
        
        return suggestions
