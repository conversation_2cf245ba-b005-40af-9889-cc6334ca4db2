import openai
import os

# إعداد مفتاح OpenAI من متغير البيئة أو ملف إعدادات
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY", "")

class AICodeExplainer:
    """
    شرح ذكي للكود البرمجي باستخدام الذكاء الصناعي (يدعم اللغة العربية)
    """
    def __init__(self, api_key=None):
        self.api_key = api_key or OPENAI_API_KEY
        openai.api_key = self.api_key

    def explain_code(self, code, language="arabic"):
        """
        شرح الكود البرمجي وإرجاع الشرح بالعربية
        """
        prompt = (
            "اشرح الكود التالي شرحًا وافيًا باللغة العربية، مع وضع الشرح المناسب كسطر تعليق فوق كل سطر أو دالة، واستخدم لغة عربية فصحى واضحة وسهلة:\n"
            f"""
{code}
"""
        )
        try:
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "أنت مساعد برمجي محترف يشرح أكواد بايثون بالعربية."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1200,
                temperature=0.2
            )
            return response.choices[0].message["content"].strip()
        except Exception as e:
            return f"خطأ في شرح الكود: {e}"
