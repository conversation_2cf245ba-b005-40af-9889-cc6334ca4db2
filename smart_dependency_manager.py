#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المكتبات الذكي - Smart Dependency Manager
كشف وإدارة وتثبيت المكتبات تلقائياً مع الذكاء الاصطناعي
"""

import os
import sys
import ast
import re
import subprocess
import json
import requests
import importlib
import pkg_resources
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime
import threading
import time

@dataclass
class LibraryInfo:
    """معلومات المكتبة"""
    name: str
    version_required: Optional[str] = None
    version_installed: Optional[str] = None
    status: str = "unknown"  # installed, missing, outdated, conflict
    source_files: List[str] = None
    usage_count: int = 0
    is_standard: bool = False
    alternatives: List[str] = None
    install_command: str = ""
    error_message: str = ""
    
    def __post_init__(self):
        if self.source_files is None:
            self.source_files = []
        if self.alternatives is None:
            self.alternatives = []

class SmartDependencyManager:
    """مدير المكتبات الذكي"""
    
    def __init__(self, callback=None):
        self.callback = callback  # دالة لإرسال التحديثات للواجهة
        self.libraries = {}  # Dict[str, LibraryInfo]
        self.python_files = []
        self.project_path = ""
        self.standard_libraries = self._get_standard_libraries()
        self.pypi_cache = {}  # تخزين مؤقت لمعلومات PyPI
        
    def log(self, message, level="info"):
        """إرسال رسالة للواجهة"""
        if self.callback:
            self.callback(message, level)
        else:
            print(f"[{level.upper()}] {message}")
    
    def _get_standard_libraries(self):
        """الحصول على قائمة المكتبات المدمجة في Python"""
        standard_libs = {
            'os', 'sys', 'json', 'time', 'datetime', 'math', 'random', 're',
            'collections', 'itertools', 'functools', 'operator', 'pathlib',
            'urllib', 'http', 'email', 'html', 'xml', 'csv', 'sqlite3',
            'threading', 'multiprocessing', 'subprocess', 'socket', 'ssl',
            'hashlib', 'hmac', 'secrets', 'uuid', 'base64', 'binascii',
            'struct', 'codecs', 'locale', 'gettext', 'argparse', 'logging',
            'warnings', 'traceback', 'pdb', 'profile', 'timeit', 'gc',
            'weakref', 'copy', 'pickle', 'shelve', 'marshal', 'dbm',
            'zlib', 'gzip', 'bz2', 'lzma', 'zipfile', 'tarfile',
            'configparser', 'fileinput', 'stat', 'filecmp', 'tempfile',
            'glob', 'fnmatch', 'shutil', 'platform', 'ctypes', 'mmap',
            'winreg', 'winsound', 'posix', 'pwd', 'grp', 'termios', 'tty',
            'pty', 'fcntl', 'resource', 'syslog', 'pipes', 'nis',
            'unittest', 'doctest', 'test', 'lib2to3', 'pydoc', 'tkinter'
        }
        return standard_libs
    
    def analyze_project(self, project_path):
        """تحليل مشروع Python شامل"""
        self.project_path = project_path
        self.libraries = {}
        self.python_files = []
        
        self.log("🔍 بدء تحليل المشروع الذكي...")
        
        # 1. العثور على جميع ملفات Python
        self._find_python_files()
        
        # 2. تحليل ملفات الكود
        self._analyze_code_files()
        
        # 3. تحليل ملفات المتطلبات
        self._analyze_requirements_files()
        
        # 4. فحص حالة المكتبات
        self._check_libraries_status()
        
        # 5. البحث عن البدائل
        self._find_alternatives()
        
        self.log(f"✅ تم تحليل {len(self.python_files)} ملف و {len(self.libraries)} مكتبة")
        
        return self.libraries
    
    def _find_python_files(self):
        """العثور على جميع ملفات Python في المشروع"""
        if os.path.isfile(self.project_path):
            if self.project_path.endswith('.py'):
                self.python_files = [self.project_path]
        else:
            for root, dirs, files in os.walk(self.project_path):
                # تجاهل مجلدات معينة
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'build', 'dist']]
                
                for file in files:
                    if file.endswith('.py'):
                        self.python_files.append(os.path.join(root, file))
        
        self.log(f"📁 تم العثور على {len(self.python_files)} ملف Python")
    
    def _analyze_code_files(self):
        """تحليل ملفات الكود لاستخراج المكتبات"""
        for file_path in self.python_files:
            try:
                self._analyze_single_file(file_path)
            except Exception as e:
                self.log(f"⚠️ خطأ في تحليل {file_path}: {e}", "warning")
    
    def _analyze_single_file(self, file_path):
        """تحليل ملف واحد"""
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # تحليل AST للحصول على imports دقيقة
        try:
            tree = ast.parse(content)
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        self._add_library(alias.name, file_path)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        self._add_library(node.module, file_path)
        except SyntaxError:
            # إذا فشل AST، استخدم regex
            self._analyze_with_regex(content, file_path)
    
    def _analyze_with_regex(self, content, file_path):
        """تحليل باستخدام regex كبديل"""
        # البحث عن import statements
        import_patterns = [
            r'^\s*import\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)',
            r'^\s*from\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\s+import',
            r'__import__\([\'"]([^\'\"]+)[\'"]',
            r'importlib\.import_module\([\'"]([^\'\"]+)[\'"]'
        ]
        
        for pattern in import_patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                library_name = match.group(1)
                self._add_library(library_name, file_path)
    
    def _add_library(self, full_name, file_path):
        """إضافة مكتبة للقائمة"""
        # استخراج اسم المكتبة الأساسي
        base_name = full_name.split('.')[0]
        
        # تجاهل المكتبات المحلية والمدمجة
        if base_name in self.standard_libraries:
            return
        
        if base_name not in self.libraries:
            self.libraries[base_name] = LibraryInfo(
                name=base_name,
                source_files=[],
                usage_count=0
            )
        
        # إضافة معلومات الاستخدام
        if file_path not in self.libraries[base_name].source_files:
            self.libraries[base_name].source_files.append(file_path)
        
        self.libraries[base_name].usage_count += 1
    
    def _analyze_requirements_files(self):
        """تحليل ملفات المتطلبات"""
        req_files = [
            'requirements.txt',
            'requirements-dev.txt',
            'requirements-test.txt',
            'setup.py',
            'pyproject.toml',
            'Pipfile',
            'environment.yml'
        ]
        
        base_path = self.project_path if os.path.isdir(self.project_path) else os.path.dirname(self.project_path)
        
        for req_file in req_files:
            req_path = os.path.join(base_path, req_file)
            if os.path.exists(req_path):
                self.log(f"📋 تحليل {req_file}")
                self._parse_requirements_file(req_path)
    
    def _parse_requirements_file(self, file_path):
        """تحليل ملف متطلبات محدد"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if file_path.endswith('requirements.txt'):
                self._parse_requirements_txt(content)
            elif file_path.endswith('setup.py'):
                self._parse_setup_py(content)
            elif file_path.endswith('pyproject.toml'):
                self._parse_pyproject_toml(content)
                
        except Exception as e:
            self.log(f"⚠️ خطأ في تحليل {file_path}: {e}", "warning")
    
    def _parse_requirements_txt(self, content):
        """تحليل requirements.txt"""
        for line in content.split('\n'):
            line = line.strip()
            if line and not line.startswith('#'):
                # تحليل اسم المكتبة والإصدار
                match = re.match(r'([a-zA-Z0-9_-]+)([>=<!=~]+.*)?', line)
                if match:
                    name = match.group(1)
                    version = match.group(2) if match.group(2) else None
                    
                    if name not in self.libraries:
                        self.libraries[name] = LibraryInfo(name=name)
                    
                    if version:
                        self.libraries[name].version_required = version.strip()
    
    def _parse_setup_py(self, content):
        """تحليل setup.py"""
        # البحث عن install_requires
        pattern = r'install_requires\s*=\s*\[(.*?)\]'
        match = re.search(pattern, content, re.DOTALL)
        if match:
            requirements = match.group(1)
            # استخراج المكتبات من القائمة
            for req in re.findall(r'[\'"]([^\'"]+)[\'"]', requirements):
                self._parse_single_requirement(req)
    
    def _parse_pyproject_toml(self, content):
        """تحليل pyproject.toml"""
        try:
            import toml
            data = toml.loads(content)
            
            # البحث في أقسام مختلفة
            sections = [
                'project.dependencies',
                'tool.poetry.dependencies',
                'build-system.requires'
            ]
            
            for section in sections:
                deps = self._get_nested_value(data, section.split('.'))
                if deps:
                    for dep in deps:
                        self._parse_single_requirement(dep)
                        
        except ImportError:
            self.log("⚠️ مكتبة toml غير متوفرة لتحليل pyproject.toml", "warning")
        except Exception as e:
            self.log(f"⚠️ خطأ في تحليل pyproject.toml: {e}", "warning")
    
    def _parse_single_requirement(self, req):
        """تحليل متطلب واحد"""
        match = re.match(r'([a-zA-Z0-9_-]+)([>=<!=~]+.*)?', req)
        if match:
            name = match.group(1)
            version = match.group(2) if match.group(2) else None
            
            if name not in self.libraries:
                self.libraries[name] = LibraryInfo(name=name)
            
            if version:
                self.libraries[name].version_required = version.strip()
    
    def _get_nested_value(self, data, keys):
        """الحصول على قيمة متداخلة من dictionary"""
        for key in keys:
            if isinstance(data, dict) and key in data:
                data = data[key]
            else:
                return None
        return data
    
    def _check_libraries_status(self):
        """فحص حالة جميع المكتبات"""
        self.log("🔍 فحص حالة المكتبات...")
        
        for lib_name, lib_info in self.libraries.items():
            try:
                # محاولة استيراد المكتبة
                spec = importlib.util.find_spec(lib_name)
                if spec is not None:
                    # المكتبة موجودة، فحص الإصدار
                    try:
                        installed_version = pkg_resources.get_distribution(lib_name).version
                        lib_info.version_installed = installed_version
                        lib_info.status = "installed"
                        
                        # فحص إذا كان يحتاج تحديث
                        if lib_info.version_required:
                            if self._needs_update(installed_version, lib_info.version_required):
                                lib_info.status = "outdated"
                                
                    except pkg_resources.DistributionNotFound:
                        lib_info.status = "installed"  # موجودة لكن بدون معلومات إصدار
                        
                else:
                    lib_info.status = "missing"
                    
            except Exception as e:
                lib_info.status = "error"
                lib_info.error_message = str(e)
        
        # إحصائيات
        statuses = [lib.status for lib in self.libraries.values()]
        self.log(f"📊 النتائج: {statuses.count('installed')} مثبتة، "
                f"{statuses.count('missing')} مفقودة، "
                f"{statuses.count('outdated')} تحتاج تحديث")
    
    def _needs_update(self, installed, required):
        """فحص إذا كانت المكتبة تحتاج تحديث"""
        try:
            from packaging import version
            # تحليل متطلب الإصدار
            if '>=' in required:
                min_version = required.split('>=')[1].strip()
                return version.parse(installed) < version.parse(min_version)
            elif '>' in required:
                min_version = required.split('>')[1].strip()
                return version.parse(installed) <= version.parse(min_version)
            elif '==' in required:
                exact_version = required.split('==')[1].strip()
                return version.parse(installed) != version.parse(exact_version)
        except:
            pass
        return False

    def _find_alternatives(self):
        """البحث عن بدائل للمكتبات المفقودة"""
        self.log("🔍 البحث عن البدائل...")

        # قاموس البدائل الشائعة
        alternatives_map = {
            'PIL': ['Pillow'],
            'cv2': ['opencv-python', 'opencv-contrib-python'],
            'sklearn': ['scikit-learn'],
            'yaml': ['PyYAML', 'ruamel.yaml'],
            'Image': ['Pillow'],
            'requests': ['urllib3', 'httpx'],
            'beautifulsoup4': ['lxml', 'html.parser'],
            'matplotlib': ['plotly', 'seaborn'],
            'pandas': ['polars', 'dask'],
            'numpy': ['cupy', 'jax'],
            'tensorflow': ['pytorch', 'jax'],
            'torch': ['tensorflow', 'jax'],
        }

        for lib_name, lib_info in self.libraries.items():
            if lib_info.status == "missing":
                if lib_name in alternatives_map:
                    lib_info.alternatives = alternatives_map[lib_name]
                else:
                    # البحث في PyPI عن مكتبات مشابهة
                    lib_info.alternatives = self._search_pypi_alternatives(lib_name)

    def _search_pypi_alternatives(self, lib_name):
        """البحث في PyPI عن بدائل"""
        try:
            # البحث عن مكتبات مشابهة في PyPI
            search_url = f"https://pypi.org/search/?q={lib_name}"
            # هذا مجرد مثال - يمكن تطويره أكثر
            return []
        except:
            return []

    def install_missing_libraries(self, auto_fix=True):
        """تثبيت المكتبات المفقودة"""
        missing_libs = [lib for lib in self.libraries.values() if lib.status == "missing"]

        if not missing_libs:
            self.log("✅ جميع المكتبات مثبتة!")
            return True

        self.log(f"📦 بدء تثبيت {len(missing_libs)} مكتبة...")

        success_count = 0
        for lib_info in missing_libs:
            if self._install_single_library(lib_info, auto_fix):
                success_count += 1

        self.log(f"✅ تم تثبيت {success_count}/{len(missing_libs)} مكتبة بنجاح")

        # إعادة فحص الحالة
        self._check_libraries_status()

        return success_count == len(missing_libs)

    def _install_single_library(self, lib_info, auto_fix=True):
        """تثبيت مكتبة واحدة"""
        install_candidates = [lib_info.name] + lib_info.alternatives

        for candidate in install_candidates:
            self.log(f"📦 محاولة تثبيت {candidate}...")

            try:
                # بناء أمر التثبيت
                cmd = [sys.executable, "-m", "pip", "install", candidate]

                if lib_info.version_required:
                    cmd[-1] += lib_info.version_required

                # تشغيل أمر التثبيت
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=300  # 5 دقائق timeout
                )

                if result.returncode == 0:
                    self.log(f"✅ تم تثبيت {candidate} بنجاح")
                    lib_info.install_command = " ".join(cmd)
                    return True
                else:
                    self.log(f"❌ فشل تثبيت {candidate}: {result.stderr}", "warning")

            except subprocess.TimeoutExpired:
                self.log(f"⏰ انتهت مهلة تثبيت {candidate}", "warning")
            except Exception as e:
                self.log(f"❌ خطأ في تثبيت {candidate}: {e}", "warning")

        return False

    def test_project_execution(self, main_file=None):
        """اختبار تشغيل المشروع"""
        if not main_file:
            main_file = self._find_main_file()

        if not main_file:
            self.log("❌ لم يتم العثور على ملف رئيسي للاختبار", "error")
            return False

        self.log(f"🧪 اختبار تشغيل {os.path.basename(main_file)}...")

        try:
            # تشغيل الملف في عملية منفصلة
            result = subprocess.run(
                [sys.executable, main_file],
                capture_output=True,
                text=True,
                timeout=30,  # 30 ثانية timeout
                cwd=os.path.dirname(main_file) if os.path.dirname(main_file) else None
            )

            if result.returncode == 0:
                self.log("✅ تم تشغيل المشروع بنجاح!")
                return True
            else:
                self.log(f"❌ فشل تشغيل المشروع: {result.stderr}", "error")

                # تحليل الأخطاء وإصلاحها
                if self._analyze_and_fix_errors(result.stderr):
                    # إعادة المحاولة بعد الإصلاح
                    return self.test_project_execution(main_file)

                return False

        except subprocess.TimeoutExpired:
            self.log("⏰ انتهت مهلة تشغيل المشروع", "warning")
            return False
        except Exception as e:
            self.log(f"❌ خطأ في اختبار التشغيل: {e}", "error")
            return False

    def _find_main_file(self):
        """العثور على الملف الرئيسي"""
        # أولويات البحث
        main_candidates = [
            'main.py',
            '__main__.py',
            'app.py',
            'run.py',
            'start.py',
            'index.py'
        ]

        base_path = self.project_path if os.path.isdir(self.project_path) else os.path.dirname(self.project_path)

        # البحث في المجلد الرئيسي
        for candidate in main_candidates:
            candidate_path = os.path.join(base_path, candidate)
            if os.path.exists(candidate_path):
                return candidate_path

        # إذا لم نجد، نأخذ أول ملف Python
        if self.python_files:
            return self.python_files[0]

        return None

    def _analyze_and_fix_errors(self, error_output):
        """تحليل الأخطاء ومحاولة إصلاحها"""
        self.log("🔍 تحليل الأخطاء...")

        # أنماط الأخطاء الشائعة
        error_patterns = [
            (r"ModuleNotFoundError: No module named '([^']+)'", self._fix_missing_module),
            (r"ImportError: cannot import name '([^']+)' from '([^']+)'", self._fix_import_error),
            (r"AttributeError: module '([^']+)' has no attribute '([^']+)'", self._fix_attribute_error),
            (r"VersionConflict: \(([^)]+)\)", self._fix_version_conflict),
        ]

        fixed_any = False

        for pattern, fix_function in error_patterns:
            matches = re.finditer(pattern, error_output)
            for match in matches:
                if fix_function(match):
                    fixed_any = True

        return fixed_any

    def _fix_missing_module(self, match):
        """إصلاح خطأ المكتبة المفقودة"""
        module_name = match.group(1)
        self.log(f"🔧 محاولة إصلاح المكتبة المفقودة: {module_name}")

        # إضافة المكتبة للقائمة إذا لم تكن موجودة
        if module_name not in self.libraries:
            self.libraries[module_name] = LibraryInfo(
                name=module_name,
                status="missing"
            )

        # محاولة تثبيتها
        return self._install_single_library(self.libraries[module_name])

    def _fix_import_error(self, match):
        """إصلاح خطأ الاستيراد"""
        item_name = match.group(1)
        module_name = match.group(2)
        self.log(f"🔧 محاولة إصلاح خطأ استيراد {item_name} من {module_name}")

        # قد يكون الإصدار خاطئ أو المكتبة تحتاج تحديث
        if module_name in self.libraries:
            lib_info = self.libraries[module_name]
            # محاولة تحديث المكتبة
            return self._update_library(lib_info)

        return False

    def _fix_attribute_error(self, match):
        """إصلاح خطأ الخاصية المفقودة"""
        module_name = match.group(1)
        attr_name = match.group(2)
        self.log(f"🔧 محاولة إصلاح خطأ الخاصية {attr_name} في {module_name}")

        # قد يكون الإصدار قديم
        if module_name in self.libraries:
            return self._update_library(self.libraries[module_name])

        return False

    def _fix_version_conflict(self, match):
        """إصلاح تعارض الإصدارات"""
        conflict_info = match.group(1)
        self.log(f"🔧 محاولة إصلاح تعارض الإصدارات: {conflict_info}")

        # تحليل معلومات التعارض وحلها
        # هذا يحتاج تطوير أكثر تعقيداً
        return False

    def _update_library(self, lib_info):
        """تحديث مكتبة"""
        self.log(f"🔄 تحديث {lib_info.name}...")

        try:
            cmd = [sys.executable, "-m", "pip", "install", "--upgrade", lib_info.name]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                self.log(f"✅ تم تحديث {lib_info.name} بنجاح")
                return True
            else:
                self.log(f"❌ فشل تحديث {lib_info.name}: {result.stderr}", "warning")
                return False

        except Exception as e:
            self.log(f"❌ خطأ في تحديث {lib_info.name}: {e}", "error")
            return False

    def generate_requirements_file(self, output_path=None):
        """إنشاء ملف requirements.txt"""
        if not output_path:
            base_path = self.project_path if os.path.isdir(self.project_path) else os.path.dirname(self.project_path)
            output_path = os.path.join(base_path, "requirements_generated.txt")

        self.log(f"📝 إنشاء ملف المتطلبات: {output_path}")

        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("# Generated by Smart Dependency Manager\n")
                f.write(f"# Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                for lib_name, lib_info in sorted(self.libraries.items()):
                    if lib_info.status in ["installed", "outdated"]:
                        if lib_info.version_installed:
                            f.write(f"{lib_name}=={lib_info.version_installed}\n")
                        else:
                            f.write(f"{lib_name}\n")
                    elif lib_info.status == "missing":
                        f.write(f"# {lib_name}  # Missing - needs installation\n")

            self.log(f"✅ تم إنشاء ملف المتطلبات: {output_path}")
            return output_path

        except Exception as e:
            self.log(f"❌ خطأ في إنشاء ملف المتطلبات: {e}", "error")
            return None

    def get_summary_report(self):
        """الحصول على تقرير شامل"""
        total_libs = len(self.libraries)
        installed = sum(1 for lib in self.libraries.values() if lib.status == "installed")
        missing = sum(1 for lib in self.libraries.values() if lib.status == "missing")
        outdated = sum(1 for lib in self.libraries.values() if lib.status == "outdated")
        errors = sum(1 for lib in self.libraries.values() if lib.status == "error")

        report = {
            "total_libraries": total_libs,
            "installed": installed,
            "missing": missing,
            "outdated": outdated,
            "errors": errors,
            "python_files": len(self.python_files),
            "project_path": self.project_path,
            "libraries": dict(self.libraries)
        }

        return report
