#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python to EXE Converter Pro v3.0 - Services Module
وحدة الخدمات

طبقة الخدمات التي تحتوي على:
- خدمة إدارة التبعيات
- خدمة الأيقونات
- خدمة الذكاء الاصطناعي
- خدمة التحديثات
- خدمات أخرى مساعدة
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from ..core.logger import Logger

class Service(ABC):
    """فئة أساسية للخدمات"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = Logger(f"Service.{name}")
        self.is_initialized = False
        self.is_running = False
    
    @abstractmethod
    def initialize(self) -> bool:
        """تهيئة الخدمة"""
        pass
    
    @abstractmethod
    def start(self) -> bool:
        """بدء الخدمة"""
        pass
    
    @abstractmethod
    def stop(self) -> bool:
        """إيقاف الخدمة"""
        pass
    
    @abstractmethod
    def cleanup(self) -> bool:
        """تنظيف الخدمة"""
        pass
    
    def get_status(self) -> Dict[str, Any]:
        """الحصول على حالة الخدمة"""
        return {
            'name': self.name,
            'initialized': self.is_initialized,
            'running': self.is_running
        }

class ServiceManager:
    """مدير الخدمات"""
    
    def __init__(self):
        self.services: Dict[str, Service] = {}
        self.logger = Logger("ServiceManager")
    
    def register_service(self, service: Service) -> bool:
        """تسجيل خدمة"""
        try:
            if service.name in self.services:
                self.logger.warning(f"الخدمة {service.name} مسجلة بالفعل")
                return False
            
            self.services[service.name] = service
            self.logger.info(f"تم تسجيل الخدمة: {service.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل الخدمة {service.name}: {e}")
            return False
    
    def get_service(self, name: str) -> Optional[Service]:
        """الحصول على خدمة"""
        return self.services.get(name)
    
    def initialize_all(self) -> Dict[str, bool]:
        """تهيئة جميع الخدمات"""
        results = {}
        
        for name, service in self.services.items():
            try:
                results[name] = service.initialize()
                if results[name]:
                    service.is_initialized = True
                    self.logger.info(f"تم تهيئة الخدمة: {name}")
                else:
                    self.logger.error(f"فشل في تهيئة الخدمة: {name}")
            except Exception as e:
                results[name] = False
                self.logger.error(f"خطأ في تهيئة الخدمة {name}: {e}")
        
        return results
    
    def start_all(self) -> Dict[str, bool]:
        """بدء جميع الخدمات"""
        results = {}
        
        for name, service in self.services.items():
            if not service.is_initialized:
                self.logger.warning(f"الخدمة {name} غير مهيأة")
                results[name] = False
                continue
            
            try:
                results[name] = service.start()
                if results[name]:
                    service.is_running = True
                    self.logger.info(f"تم بدء الخدمة: {name}")
                else:
                    self.logger.error(f"فشل في بدء الخدمة: {name}")
            except Exception as e:
                results[name] = False
                self.logger.error(f"خطأ في بدء الخدمة {name}: {e}")
        
        return results
    
    def stop_all(self) -> Dict[str, bool]:
        """إيقاف جميع الخدمات"""
        results = {}
        
        for name, service in self.services.items():
            if not service.is_running:
                results[name] = True
                continue
            
            try:
                results[name] = service.stop()
                if results[name]:
                    service.is_running = False
                    self.logger.info(f"تم إيقاف الخدمة: {name}")
                else:
                    self.logger.error(f"فشل في إيقاف الخدمة: {name}")
            except Exception as e:
                results[name] = False
                self.logger.error(f"خطأ في إيقاف الخدمة {name}: {e}")
        
        return results
    
    def cleanup_all(self) -> Dict[str, bool]:
        """تنظيف جميع الخدمات"""
        results = {}
        
        for name, service in self.services.items():
            try:
                results[name] = service.cleanup()
                if results[name]:
                    service.is_initialized = False
                    service.is_running = False
                    self.logger.info(f"تم تنظيف الخدمة: {name}")
                else:
                    self.logger.error(f"فشل في تنظيف الخدمة: {name}")
            except Exception as e:
                results[name] = False
                self.logger.error(f"خطأ في تنظيف الخدمة {name}: {e}")
        
        return results
    
    def get_all_status(self) -> Dict[str, Dict[str, Any]]:
        """الحصول على حالة جميع الخدمات"""
        return {
            name: service.get_status()
            for name, service in self.services.items()
        }

# مثيل عام لمدير الخدمات
service_manager = ServiceManager()

# تصدير المكونات الرئيسية
__all__ = [
    'Service',
    'ServiceManager',
    'service_manager'
]
