#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python to EXE Converter Pro v3.0 - Plugin System
نظام الإضافات

نظام إضافات متقدم يدعم:
- تحميل الإضافات الديناميكي
- إدارة دورة حياة الإضافات
- نظام الأحداث والخطافات
- واجهة برمجة تطبيقات موحدة
"""

import os
import sys
import importlib
import inspect
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable, Type
from pathlib import Path
from dataclasses import dataclass
from enum import Enum

from ..core.logger import Logger
from ..core.exceptions import PluginError

class PluginType(Enum):
    """أنواع الإضافات"""
    CONVERTER = "converter"          # إضافات التحويل
    UI_COMPONENT = "ui_component"    # مكونات الواجهة
    AI_TOOL = "ai_tool"             # أدوات الذكاء الاصطناعي
    ICON_GENERATOR = "icon_generator" # مولدات الأيقونات
    CODE_ANALYZER = "code_analyzer"  # محللات الكود
    THEME = "theme"                 # الثيمات
    UTILITY = "utility"             # أدوات مساعدة

class PluginStatus(Enum):
    """حالات الإضافة"""
    LOADED = "loaded"
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    DISABLED = "disabled"

@dataclass
class PluginInfo:
    """معلومات الإضافة"""
    name: str
    version: str
    description: str
    author: str
    plugin_type: PluginType
    dependencies: List[str]
    min_app_version: str
    max_app_version: Optional[str] = None
    website: Optional[str] = None
    license: Optional[str] = None

class PluginHook:
    """خطاف الإضافة"""
    
    def __init__(self, name: str):
        self.name = name
        self.callbacks: List[Callable] = []
    
    def register(self, callback: Callable):
        """تسجيل callback"""
        if callback not in self.callbacks:
            self.callbacks.append(callback)
    
    def unregister(self, callback: Callable):
        """إلغاء تسجيل callback"""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
    
    def trigger(self, *args, **kwargs):
        """تشغيل جميع callbacks"""
        results = []
        for callback in self.callbacks:
            try:
                result = callback(*args, **kwargs)
                results.append(result)
            except Exception as e:
                # تسجيل الخطأ ولكن لا نوقف باقي callbacks
                print(f"خطأ في plugin hook {self.name}: {e}")
        return results

class Plugin(ABC):
    """فئة أساسية للإضافات"""
    
    def __init__(self, info: PluginInfo):
        self.info = info
        self.status = PluginStatus.LOADED
        self.logger = Logger(f"Plugin.{info.name}")
        self.hooks: Dict[str, PluginHook] = {}
        self.config = {}
    
    @abstractmethod
    def initialize(self) -> bool:
        """تهيئة الإضافة"""
        pass
    
    @abstractmethod
    def activate(self) -> bool:
        """تفعيل الإضافة"""
        pass
    
    @abstractmethod
    def deactivate(self) -> bool:
        """إلغاء تفعيل الإضافة"""
        pass
    
    @abstractmethod
    def cleanup(self) -> bool:
        """تنظيف الإضافة"""
        pass
    
    def get_info(self) -> PluginInfo:
        """الحصول على معلومات الإضافة"""
        return self.info
    
    def get_status(self) -> PluginStatus:
        """الحصول على حالة الإضافة"""
        return self.status
    
    def set_config(self, config: Dict[str, Any]):
        """تعيين إعدادات الإضافة"""
        self.config = config
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """الحصول على إعداد"""
        return self.config.get(key, default)
    
    def register_hook(self, hook_name: str, callback: Callable):
        """تسجيل خطاف"""
        if hook_name not in self.hooks:
            self.hooks[hook_name] = PluginHook(hook_name)
        self.hooks[hook_name].register(callback)
    
    def trigger_hook(self, hook_name: str, *args, **kwargs):
        """تشغيل خطاف"""
        if hook_name in self.hooks:
            return self.hooks[hook_name].trigger(*args, **kwargs)
        return []

class PluginManager:
    """مدير الإضافات"""
    
    def __init__(self, plugins_dir: str = "plugins"):
        self.plugins_dir = Path(plugins_dir)
        self.plugins: Dict[str, Plugin] = {}
        self.plugin_types: Dict[PluginType, List[str]] = {
            plugin_type: [] for plugin_type in PluginType
        }
        self.hooks: Dict[str, PluginHook] = {}
        self.logger = Logger("PluginManager")
        
        # إنشاء مجلد الإضافات إذا لم يكن موجوداً
        self.plugins_dir.mkdir(exist_ok=True)
        
        # تسجيل الخطافات الأساسية
        self._register_core_hooks()
    
    def _register_core_hooks(self):
        """تسجيل الخطافات الأساسية"""
        core_hooks = [
            "before_conversion",
            "after_conversion",
            "before_ui_create",
            "after_ui_create",
            "on_file_select",
            "on_settings_change",
            "on_error",
            "on_progress_update"
        ]
        
        for hook_name in core_hooks:
            self.hooks[hook_name] = PluginHook(hook_name)
    
    def discover_plugins(self) -> List[str]:
        """اكتشاف الإضافات المتاحة"""
        discovered = []
        
        if not self.plugins_dir.exists():
            return discovered
        
        # البحث في ملفات Python
        for py_file in self.plugins_dir.glob("*.py"):
            if py_file.name.startswith("__"):
                continue
            
            try:
                # محاولة تحميل الملف للتحقق من صحته
                spec = importlib.util.spec_from_file_location(
                    py_file.stem, py_file
                )
                if spec and spec.loader:
                    discovered.append(py_file.stem)
            except Exception as e:
                self.logger.warning(f"خطأ في اكتشاف الإضافة {py_file}: {e}")
        
        # البحث في المجلدات
        for plugin_dir in self.plugins_dir.iterdir():
            if plugin_dir.is_dir() and not plugin_dir.name.startswith("__"):
                init_file = plugin_dir / "__init__.py"
                if init_file.exists():
                    discovered.append(plugin_dir.name)
        
        self.logger.info(f"تم اكتشاف {len(discovered)} إضافة: {discovered}")
        return discovered
    
    def load_plugin(self, plugin_name: str) -> bool:
        """تحميل إضافة"""
        if plugin_name in self.plugins:
            self.logger.warning(f"الإضافة {plugin_name} محملة بالفعل")
            return True
        
        try:
            # تحديد مسار الإضافة
            plugin_path = self.plugins_dir / f"{plugin_name}.py"
            if not plugin_path.exists():
                plugin_path = self.plugins_dir / plugin_name / "__init__.py"
            
            if not plugin_path.exists():
                raise PluginError(f"ملف الإضافة غير موجود: {plugin_name}")
            
            # تحميل الوحدة
            spec = importlib.util.spec_from_file_location(
                plugin_name, plugin_path
            )
            if not spec or not spec.loader:
                raise PluginError(f"فشل في إنشاء spec للإضافة: {plugin_name}")
            
            module = importlib.util.module_from_spec(spec)
            sys.modules[plugin_name] = module
            spec.loader.exec_module(module)
            
            # البحث عن فئة الإضافة
            plugin_class = None
            for name, obj in inspect.getmembers(module):
                if (inspect.isclass(obj) and 
                    issubclass(obj, Plugin) and 
                    obj != Plugin):
                    plugin_class = obj
                    break
            
            if not plugin_class:
                raise PluginError(f"لم يتم العثور على فئة إضافة في: {plugin_name}")
            
            # إنشاء مثيل الإضافة
            plugin_instance = plugin_class()
            
            # تهيئة الإضافة
            if not plugin_instance.initialize():
                raise PluginError(f"فشل في تهيئة الإضافة: {plugin_name}")
            
            # تسجيل الإضافة
            self.plugins[plugin_name] = plugin_instance
            self.plugin_types[plugin_instance.info.plugin_type].append(plugin_name)
            
            self.logger.info(f"تم تحميل الإضافة: {plugin_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل الإضافة {plugin_name}: {e}")
            return False
    
    def unload_plugin(self, plugin_name: str) -> bool:
        """إلغاء تحميل إضافة"""
        if plugin_name not in self.plugins:
            self.logger.warning(f"الإضافة {plugin_name} غير محملة")
            return True
        
        try:
            plugin = self.plugins[plugin_name]
            
            # إلغاء تفعيل الإضافة
            if plugin.status == PluginStatus.ACTIVE:
                plugin.deactivate()
            
            # تنظيف الإضافة
            plugin.cleanup()
            
            # إزالة من القوائم
            del self.plugins[plugin_name]
            if plugin_name in self.plugin_types[plugin.info.plugin_type]:
                self.plugin_types[plugin.info.plugin_type].remove(plugin_name)
            
            # إزالة من sys.modules
            if plugin_name in sys.modules:
                del sys.modules[plugin_name]
            
            self.logger.info(f"تم إلغاء تحميل الإضافة: {plugin_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في إلغاء تحميل الإضافة {plugin_name}: {e}")
            return False
    
    def activate_plugin(self, plugin_name: str) -> bool:
        """تفعيل إضافة"""
        if plugin_name not in self.plugins:
            self.logger.error(f"الإضافة {plugin_name} غير محملة")
            return False
        
        plugin = self.plugins[plugin_name]
        
        if plugin.status == PluginStatus.ACTIVE:
            return True
        
        try:
            if plugin.activate():
                plugin.status = PluginStatus.ACTIVE
                self.logger.info(f"تم تفعيل الإضافة: {plugin_name}")
                return True
            else:
                plugin.status = PluginStatus.ERROR
                return False
                
        except Exception as e:
            plugin.status = PluginStatus.ERROR
            self.logger.error(f"خطأ في تفعيل الإضافة {plugin_name}: {e}")
            return False
    
    def deactivate_plugin(self, plugin_name: str) -> bool:
        """إلغاء تفعيل إضافة"""
        if plugin_name not in self.plugins:
            return True
        
        plugin = self.plugins[plugin_name]
        
        if plugin.status != PluginStatus.ACTIVE:
            return True
        
        try:
            if plugin.deactivate():
                plugin.status = PluginStatus.INACTIVE
                self.logger.info(f"تم إلغاء تفعيل الإضافة: {plugin_name}")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"خطأ في إلغاء تفعيل الإضافة {plugin_name}: {e}")
            return False
    
    def get_plugin(self, plugin_name: str) -> Optional[Plugin]:
        """الحصول على إضافة"""
        return self.plugins.get(plugin_name)
    
    def get_plugins_by_type(self, plugin_type: PluginType) -> List[Plugin]:
        """الحصول على الإضافات حسب النوع"""
        plugins = []
        for plugin_name in self.plugin_types[plugin_type]:
            if plugin_name in self.plugins:
                plugins.append(self.plugins[plugin_name])
        return plugins
    
    def get_active_plugins(self) -> List[Plugin]:
        """الحصول على الإضافات النشطة"""
        return [
            plugin for plugin in self.plugins.values()
            if plugin.status == PluginStatus.ACTIVE
        ]
    
    def load_all_plugins(self) -> Dict[str, bool]:
        """تحميل جميع الإضافات"""
        discovered = self.discover_plugins()
        results = {}
        
        for plugin_name in discovered:
            results[plugin_name] = self.load_plugin(plugin_name)
        
        return results
    
    def activate_all_plugins(self) -> Dict[str, bool]:
        """تفعيل جميع الإضافات المحملة"""
        results = {}
        
        for plugin_name in self.plugins:
            results[plugin_name] = self.activate_plugin(plugin_name)
        
        return results
    
    def register_hook(self, hook_name: str, callback: Callable):
        """تسجيل خطاف عام"""
        if hook_name not in self.hooks:
            self.hooks[hook_name] = PluginHook(hook_name)
        self.hooks[hook_name].register(callback)
    
    def trigger_hook(self, hook_name: str, *args, **kwargs):
        """تشغيل خطاف عام"""
        results = []
        
        # تشغيل الخطاف العام
        if hook_name in self.hooks:
            results.extend(self.hooks[hook_name].trigger(*args, **kwargs))
        
        # تشغيل خطافات الإضافات النشطة
        for plugin in self.get_active_plugins():
            plugin_results = plugin.trigger_hook(hook_name, *args, **kwargs)
            results.extend(plugin_results)
        
        return results
    
    def get_plugin_info(self, plugin_name: str) -> Optional[PluginInfo]:
        """الحصول على معلومات إضافة"""
        plugin = self.get_plugin(plugin_name)
        return plugin.get_info() if plugin else None
    
    def get_all_plugins_info(self) -> Dict[str, PluginInfo]:
        """الحصول على معلومات جميع الإضافات"""
        return {
            name: plugin.get_info()
            for name, plugin in self.plugins.items()
        }

# مثيل عام لمدير الإضافات
plugin_manager = PluginManager()

# دوال مساعدة للاستخدام السريع
def load_plugin(plugin_name: str) -> bool:
    return plugin_manager.load_plugin(plugin_name)

def activate_plugin(plugin_name: str) -> bool:
    return plugin_manager.activate_plugin(plugin_name)

def get_plugin(plugin_name: str) -> Optional[Plugin]:
    return plugin_manager.get_plugin(plugin_name)

def trigger_hook(hook_name: str, *args, **kwargs):
    return plugin_manager.trigger_hook(hook_name, *args, **kwargs)

# تصدير المكونات الرئيسية
__all__ = [
    'Plugin',
    'PluginInfo',
    'PluginType',
    'PluginStatus',
    'PluginManager',
    'PluginHook',
    'plugin_manager',
    'load_plugin',
    'activate_plugin',
    'get_plugin',
    'trigger_hook'
]
