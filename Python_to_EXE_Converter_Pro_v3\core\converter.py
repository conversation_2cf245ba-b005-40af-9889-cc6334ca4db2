#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python to EXE Converter Pro v3.0 - Main Converter Engine
محرك التحويل الرئيسي

محرك متقدم لتحويل ملفات Python إلى تطبيقات تنفيذية مع:
- دعم PyInstaller المتقدم
- تحليل التبعيات التلقائي
- تحسين الأداء
- معالجة الأخطاء الذكية
- دعم الإضافات
"""

import os
import sys
import subprocess
import threading
import time
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, field
from enum import Enum

from .logger import Logger
from .exceptions import (
    ConverterError, 
    ValidationError, 
    DependencyError, 
    ProcessError,
    FileError
)

class ConversionMode(Enum):
    """أنماط التحويل"""
    ONEFILE = "onefile"
    ONEDIR = "onedir"
    WINDOWED = "windowed"
    CONSOLE = "console"

class OptimizationLevel(Enum):
    """مستويات التحسين"""
    NONE = 0
    BASIC = 1
    ADVANCED = 2
    MAXIMUM = 3

@dataclass
class ConversionOptions:
    """خيارات التحويل"""
    # الملفات والمسارات
    source_file: str
    output_dir: str = "dist"
    output_name: Optional[str] = None
    icon_file: Optional[str] = None
    
    # خيارات التحويل الأساسية
    mode: ConversionMode = ConversionMode.ONEFILE
    windowed: bool = False
    console: bool = True
    debug: bool = False
    
    # التحسين والضغط
    optimization_level: OptimizationLevel = OptimizationLevel.BASIC
    upx_compress: bool = False
    strip_debug: bool = True
    
    # التبعيات والمكتبات
    additional_data: List[tuple] = field(default_factory=list)
    hidden_imports: List[str] = field(default_factory=list)
    exclude_modules: List[str] = field(default_factory=list)
    
    # خيارات متقدمة
    clean_build: bool = True
    no_confirm: bool = True
    log_level: str = "INFO"
    
    # خيارات الأمان
    key: Optional[str] = None  # تشفير
    obfuscate: bool = False   # تشويش الكود
    
    def validate(self) -> bool:
        """التحقق من صحة الخيارات"""
        if not self.source_file or not os.path.exists(self.source_file):
            raise ValidationError(f"ملف المصدر غير موجود: {self.source_file}")
        
        if not self.source_file.endswith('.py'):
            raise ValidationError("يجب أن يكون الملف المصدر من نوع Python (.py)")
        
        if self.icon_file and not os.path.exists(self.icon_file):
            raise ValidationError(f"ملف الأيقونة غير موجود: {self.icon_file}")
        
        return True

class ConversionProgress:
    """تتبع تقدم التحويل"""
    def __init__(self):
        self.current_stage = ""
        self.progress_percent = 0
        self.start_time = None
        self.end_time = None
        self.stages = []
        self.callbacks = []
    
    def add_callback(self, callback: Callable):
        """إضافة callback لتتبع التقدم"""
        self.callbacks.append(callback)
    
    def update(self, stage: str, progress: int):
        """تحديث التقدم"""
        self.current_stage = stage
        self.progress_percent = progress
        self.stages.append({
            'stage': stage,
            'progress': progress,
            'timestamp': time.time()
        })
        
        # استدعاء callbacks
        for callback in self.callbacks:
            try:
                callback(stage, progress)
            except Exception as e:
                print(f"Error in progress callback: {e}")

class PyToExeConverter:
    """محرك التحويل الرئيسي"""
    
    def __init__(self, logger: Optional[Logger] = None):
        self.logger = logger or Logger("PyToExeConverter")
        self.progress = ConversionProgress()
        self.is_converting = False
        self.conversion_thread = None
        
        # فحص متطلبات النظام
        self._check_requirements()
    
    def _check_requirements(self):
        """فحص متطلبات النظام"""
        try:
            # فحص Python
            if sys.version_info < (3, 7):
                raise DependencyError("يتطلب Python 3.7 أو أحدث")
            
            # فحص PyInstaller
            try:
                import PyInstaller
                self.logger.info(f"تم العثور على PyInstaller {PyInstaller.__version__}")
            except ImportError:
                raise DependencyError("PyInstaller غير مثبت")
            
            # فحص الأدوات الإضافية
            self._check_optional_tools()
            
        except Exception as e:
            self.logger.error(f"خطأ في فحص المتطلبات: {e}")
            raise
    
    def _check_optional_tools(self):
        """فحص الأدوات الاختيارية"""
        # فحص UPX للضغط
        try:
            subprocess.run(['upx', '--version'], 
                         capture_output=True, check=True)
            self.logger.info("تم العثور على UPX للضغط")
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.logger.warning("UPX غير متوفر - لن يتم ضغط الملف")
    
    def convert(self, options: ConversionOptions, 
                progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """تحويل ملف Python إلى EXE"""
        if self.is_converting:
            raise ConverterError("عملية تحويل أخرى قيد التشغيل")
        
        # التحقق من الخيارات
        options.validate()
        
        # إعداد التقدم
        if progress_callback:
            self.progress.add_callback(progress_callback)
        
        self.is_converting = True
        self.progress.start_time = time.time()
        
        try:
            self.logger.log_conversion_start(options.source_file, options.__dict__)
            
            # المراحل الرئيسية
            result = self._execute_conversion(options)
            
            self.progress.end_time = time.time()
            duration = self.progress.end_time - self.progress.start_time
            
            self.logger.log_conversion_complete(
                result.get('output_file', ''), duration
            )
            
            return result
            
        except Exception as e:
            self.logger.log_conversion_error(str(e))
            raise
        finally:
            self.is_converting = False
    
    def _execute_conversion(self, options: ConversionOptions) -> Dict[str, Any]:
        """تنفيذ عملية التحويل"""
        # المرحلة 1: التحضير
        self.progress.update("تحضير الملفات", 10)
        self._prepare_conversion(options)
        
        # المرحلة 2: تحليل التبعيات
        self.progress.update("تحليل التبعيات", 25)
        dependencies = self._analyze_dependencies(options.source_file)
        
        # المرحلة 3: بناء الأمر
        self.progress.update("بناء أمر التحويل", 40)
        command = self._build_command(options, dependencies)
        
        # المرحلة 4: تنفيذ التحويل
        self.progress.update("تنفيذ التحويل", 50)
        result = self._execute_pyinstaller(command, options)
        
        # المرحلة 5: التحسين
        self.progress.update("تحسين النتيجة", 80)
        self._optimize_output(options, result)
        
        # المرحلة 6: التنظيف
        self.progress.update("تنظيف الملفات المؤقتة", 95)
        self._cleanup(options)
        
        self.progress.update("اكتمل التحويل", 100)
        
        return result
    
    def _prepare_conversion(self, options: ConversionOptions):
        """تحضير عملية التحويل"""
        # إنشاء مجلد الإخراج
        output_path = Path(options.output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # تنظيف البناء السابق إذا طُلب
        if options.clean_build:
            build_dir = Path("build")
            if build_dir.exists():
                shutil.rmtree(build_dir)
            
            dist_dir = output_path
            if dist_dir.exists():
                for item in dist_dir.iterdir():
                    if item.is_file():
                        item.unlink()
                    elif item.is_dir():
                        shutil.rmtree(item)
    
    def _analyze_dependencies(self, source_file: str) -> List[str]:
        """تحليل تبعيات الملف"""
        dependencies = []
        
        try:
            with open(source_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # تحليل بسيط للاستيرادات
            import ast
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        dependencies.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        dependencies.append(node.module)
            
            self.logger.info(f"تم العثور على {len(dependencies)} تبعية")
            return list(set(dependencies))  # إزالة التكرار
            
        except Exception as e:
            self.logger.warning(f"خطأ في تحليل التبعيات: {e}")
            return []
    
    def _build_command(self, options: ConversionOptions, 
                      dependencies: List[str]) -> List[str]:
        """بناء أمر PyInstaller"""
        cmd = ['pyinstaller']
        
        # الخيارات الأساسية
        if options.mode == ConversionMode.ONEFILE:
            cmd.append('--onefile')
        elif options.mode == ConversionMode.ONEDIR:
            cmd.append('--onedir')
        
        if options.windowed:
            cmd.append('--windowed')
        elif not options.console:
            cmd.append('--noconsole')
        
        if options.debug:
            cmd.append('--debug=all')
        
        # المسارات
        cmd.extend(['--distpath', options.output_dir])
        
        if options.output_name:
            cmd.extend(['--name', options.output_name])
        
        if options.icon_file:
            cmd.extend(['--icon', options.icon_file])
        
        # التبعيات
        for dep in options.hidden_imports:
            cmd.extend(['--hidden-import', dep])
        
        for module in options.exclude_modules:
            cmd.extend(['--exclude-module', module])
        
        # البيانات الإضافية
        for src, dst in options.additional_data:
            cmd.extend(['--add-data', f"{src}{os.pathsep}{dst}"])
        
        # خيارات التحسين
        if options.upx_compress:
            cmd.append('--upx-dir=.')
        
        if options.strip_debug:
            cmd.append('--strip')
        
        # خيارات أخرى
        if options.clean_build:
            cmd.append('--clean')
        
        if options.no_confirm:
            cmd.append('--noconfirm')
        
        cmd.extend(['--log-level', options.log_level])
        
        # الملف المصدر
        cmd.append(options.source_file)
        
        self.logger.info(f"أمر التحويل: {' '.join(cmd)}")
        return cmd
    
    def _execute_pyinstaller(self, command: List[str], 
                           options: ConversionOptions) -> Dict[str, Any]:
        """تنفيذ أمر PyInstaller"""
        try:
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )
            
            # تتبع الإخراج
            output_lines = []
            error_lines = []
            
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    line = output.strip()
                    output_lines.append(line)
                    self.logger.debug(f"PyInstaller: {line}")
                    
                    # تحديث التقدم بناءً على الإخراج
                    self._update_progress_from_output(line)
            
            # الحصول على أي أخطاء
            stderr = process.stderr.read()
            if stderr:
                error_lines.extend(stderr.split('\n'))
            
            # فحص النتيجة
            if process.returncode != 0:
                error_msg = '\n'.join(error_lines) if error_lines else "خطأ غير معروف"
                raise ProcessError(
                    f"فشل في تنفيذ PyInstaller",
                    process_name="pyinstaller",
                    exit_code=process.returncode,
                    stderr=error_msg
                )
            
            # تحديد ملف الإخراج
            output_file = self._find_output_file(options)
            
            return {
                'success': True,
                'output_file': output_file,
                'output_lines': output_lines,
                'command': command
            }
            
        except subprocess.TimeoutExpired:
            raise ProcessError("انتهت مهلة عملية التحويل")
        except Exception as e:
            raise ProcessError(f"خطأ في تنفيذ التحويل: {e}")
    
    def _update_progress_from_output(self, line: str):
        """تحديث التقدم من إخراج PyInstaller"""
        # تحليل بسيط لإخراج PyInstaller لتحديد التقدم
        if "INFO: Building" in line:
            self.progress.update("بناء التطبيق", 60)
        elif "INFO: Building EXE" in line:
            self.progress.update("إنشاء ملف EXE", 75)
        elif "INFO: Building COLLECT" in line:
            self.progress.update("جمع الملفات", 85)
        elif "completed successfully" in line:
            self.progress.update("اكتمل البناء", 90)
    
    def _find_output_file(self, options: ConversionOptions) -> str:
        """العثور على ملف الإخراج"""
        output_dir = Path(options.output_dir)
        
        # اسم الملف المتوقع
        if options.output_name:
            expected_name = options.output_name
        else:
            expected_name = Path(options.source_file).stem
        
        # البحث عن الملف
        if options.mode == ConversionMode.ONEFILE:
            exe_file = output_dir / f"{expected_name}.exe"
            if exe_file.exists():
                return str(exe_file)
        else:
            # في وضع onedir
            app_dir = output_dir / expected_name
            if app_dir.exists():
                exe_file = app_dir / f"{expected_name}.exe"
                if exe_file.exists():
                    return str(exe_file)
        
        # البحث العام
        for exe_file in output_dir.rglob("*.exe"):
            return str(exe_file)
        
        raise FileError("لم يتم العثور على ملف الإخراج")
    
    def _optimize_output(self, options: ConversionOptions, result: Dict):
        """تحسين ملف الإخراج"""
        if options.optimization_level == OptimizationLevel.NONE:
            return
        
        output_file = result.get('output_file')
        if not output_file or not os.path.exists(output_file):
            return
        
        try:
            # ضغط UPX إذا كان متوفراً ومطلوباً
            if options.upx_compress:
                self._apply_upx_compression(output_file)
            
            # تحسينات أخرى حسب المستوى
            if options.optimization_level.value >= 2:
                self._apply_advanced_optimizations(output_file, options)
                
        except Exception as e:
            self.logger.warning(f"خطأ في التحسين: {e}")
    
    def _apply_upx_compression(self, exe_file: str):
        """تطبيق ضغط UPX"""
        try:
            cmd = ['upx', '--best', exe_file]
            subprocess.run(cmd, check=True, capture_output=True)
            self.logger.info("تم ضغط الملف بـ UPX")
        except Exception as e:
            self.logger.warning(f"فشل ضغط UPX: {e}")
    
    def _apply_advanced_optimizations(self, exe_file: str, options: ConversionOptions):
        """تطبيق تحسينات متقدمة"""
        # هنا يمكن إضافة تحسينات متقدمة مثل:
        # - تشويش الكود
        # - تشفير الموارد
        # - ضغط إضافي
        pass
    
    def _cleanup(self, options: ConversionOptions):
        """تنظيف الملفات المؤقتة"""
        if options.clean_build:
            # حذف مجلد build
            build_dir = Path("build")
            if build_dir.exists():
                shutil.rmtree(build_dir)
            
            # حذف ملفات .spec
            for spec_file in Path(".").glob("*.spec"):
                spec_file.unlink()
    
    def convert_async(self, options: ConversionOptions,
                     progress_callback: Optional[Callable] = None) -> threading.Thread:
        """تحويل غير متزامن"""
        def conversion_worker():
            try:
                self.convert(options, progress_callback)
            except Exception as e:
                self.logger.error(f"خطأ في التحويل غير المتزامن: {e}")

        self.conversion_thread = threading.Thread(target=conversion_worker)
        self.conversion_thread.start()
        return self.conversion_thread

    def cancel_conversion(self):
        """إلغاء عملية التحويل"""
        if self.conversion_thread and self.conversion_thread.is_alive():
            # في التطبيق الحقيقي، نحتاج لطريقة أفضل لإيقاف العملية
            self.logger.warning("تم طلب إلغاء التحويل")
            self.is_converting = False

    def get_conversion_info(self) -> Dict[str, Any]:
        """الحصول على معلومات التحويل الحالي"""
        return {
            'is_converting': self.is_converting,
            'current_stage': self.progress.current_stage,
            'progress_percent': self.progress.progress_percent,
            'start_time': self.progress.start_time,
            'stages': self.progress.stages
        }
