#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python to EXE Converter Pro v3.0 - Setup Script
سكريبت الإعداد والتثبيت
"""

from setuptools import setup, find_packages
from pathlib import Path

# قراءة README
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding='utf-8') if readme_file.exists() else ""

# قراءة المتطلبات
requirements_file = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_file.exists():
    requirements = requirements_file.read_text(encoding='utf-8').strip().split('\n')
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith('#')]

setup(
    name="python-to-exe-converter-pro",
    version="3.0.0",
    author="Python to EXE Converter Pro Team",
    author_email="<EMAIL>",
    description="محول متقدم وذكي لتحويل ملفات Python إلى تطبيقات تنفيذية",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-repo/python-to-exe-converter-pro-v3",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Software Development :: Build Tools",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: System :: Software Distribution",
    ],
    python_requires=">=3.7",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-cov>=4.1.0",
            "black>=23.7.0",
            "flake8>=6.0.0",
            "mypy>=1.5.0",
        ],
        "docs": [
            "Sphinx>=7.1.0",
            "sphinx-rtd-theme>=1.3.0",
        ],
        "qt": [
            "PyQt6>=6.5.0",
        ],
        "web": [
            "Flask>=2.3.0",
            "Flask-SocketIO>=5.3.0",
            "eventlet>=0.33.0",
        ],
        "ai": [
            "openai>=0.27.0",
            "requests>=2.31.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "pytoexe=main:main",
            "python-to-exe=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.txt", "*.md", "*.json", "*.ico", "*.png"],
        "ui/themes": ["*.json", "*.css"],
        "plugins": ["*.py"],
        "resources": ["*"],
    },
    keywords="python exe converter pyinstaller gui tkinter pyqt",
    project_urls={
        "Bug Reports": "https://github.com/your-repo/python-to-exe-converter-pro-v3/issues",
        "Source": "https://github.com/your-repo/python-to-exe-converter-pro-v3",
        "Documentation": "https://docs.pytoexeconverter.com",
    },
)
