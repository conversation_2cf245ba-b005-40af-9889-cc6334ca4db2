#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python to EXE Converter Pro v3.0 - Advanced Logger System
نظام السجلات المتقدم

نظام سجلات شامل يدعم:
- مستويات سجلات متعددة
- تنسيقات مختلفة للإخراج
- حفظ في ملفات وقواعد بيانات
- فلترة وبحث في السجلات
- إشعارات في الوقت الفعلي
"""

import logging
import logging.handlers
import os
import json
import threading
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, List, Callable
from enum import Enum

class LogLevel(Enum):
    """مستويات السجلات"""
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL

class LogFormat(Enum):
    """تنسيقات السجلات"""
    SIMPLE = "%(levelname)s - %(message)s"
    DETAILED = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    JSON = "json"
    COLORED = "colored"

class Logger:
    """نظام السجلات المتقدم"""
    
    def __init__(self, name="PyToExeConverter", level=LogLevel.INFO):
        self.name = name
        self.level = level
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level.value)
        
        # إعدادات السجل
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        # معالجات السجل
        self.handlers = {}
        self.callbacks = []
        self.filters = []
        
        # إعداد المعالجات الافتراضية
        self._setup_default_handlers()
        
        # قفل للأمان في البيئة متعددة الخيوط
        self.lock = threading.Lock()
        
        # إحصائيات السجل
        self.stats = {
            'debug': 0,
            'info': 0,
            'warning': 0,
            'error': 0,
            'critical': 0
        }
    
    def _setup_default_handlers(self):
        """إعداد المعالجات الافتراضية"""
        # معالج وحدة التحكم
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.level.value)
        console_formatter = logging.Formatter(LogFormat.DETAILED.value)
        console_handler.setFormatter(console_formatter)
        
        self.logger.addHandler(console_handler)
        self.handlers['console'] = console_handler
        
        # معالج الملف
        log_file = self.log_dir / f"{self.name}.log"
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(LogFormat.DETAILED.value)
        file_handler.setFormatter(file_formatter)
        
        self.logger.addHandler(file_handler)
        self.handlers['file'] = file_handler
    
    def add_handler(self, name: str, handler: logging.Handler):
        """إضافة معالج جديد"""
        with self.lock:
            self.logger.addHandler(handler)
            self.handlers[name] = handler
    
    def remove_handler(self, name: str):
        """إزالة معالج"""
        with self.lock:
            if name in self.handlers:
                self.logger.removeHandler(self.handlers[name])
                del self.handlers[name]
    
    def add_callback(self, callback: Callable):
        """إضافة callback للإشعارات الفورية"""
        self.callbacks.append(callback)
    
    def add_filter(self, filter_func: Callable):
        """إضافة فلتر للسجلات"""
        self.filters.append(filter_func)
    
    def _should_log(self, level: LogLevel, message: str, extra: Dict = None) -> bool:
        """فحص ما إذا كان يجب تسجيل الرسالة"""
        # فحص المستوى
        if level.value < self.level.value:
            return False
        
        # تطبيق الفلاتر
        for filter_func in self.filters:
            if not filter_func(level, message, extra):
                return False
        
        return True
    
    def _log(self, level: LogLevel, message: str, extra: Dict = None):
        """تسجيل رسالة"""
        if not self._should_log(level, message, extra):
            return
        
        with self.lock:
            # تحديث الإحصائيات
            level_name = level.name.lower()
            self.stats[level_name] += 1
            
            # إنشاء سجل مفصل
            log_record = {
                'timestamp': datetime.now().isoformat(),
                'level': level.name,
                'message': message,
                'extra': extra or {},
                'thread': threading.current_thread().name
            }
            
            # تسجيل في النظام
            getattr(self.logger, level_name.lower())(message, extra=extra or {})
            
            # استدعاء callbacks
            for callback in self.callbacks:
                try:
                    callback(log_record)
                except Exception as e:
                    # تجنب التكرار اللانهائي
                    print(f"Error in log callback: {e}")
    
    def debug(self, message: str, extra: Dict = None):
        """تسجيل رسالة تصحيح"""
        self._log(LogLevel.DEBUG, message, extra)
    
    def info(self, message: str, extra: Dict = None):
        """تسجيل رسالة معلومات"""
        self._log(LogLevel.INFO, message, extra)
    
    def warning(self, message: str, extra: Dict = None):
        """تسجيل رسالة تحذير"""
        self._log(LogLevel.WARNING, message, extra)
    
    def error(self, message: str, extra: Dict = None):
        """تسجيل رسالة خطأ"""
        self._log(LogLevel.ERROR, message, extra)
    
    def critical(self, message: str, extra: Dict = None):
        """تسجيل رسالة خطأ حرج"""
        self._log(LogLevel.CRITICAL, message, extra)
    
    def log_conversion_start(self, source_file: str, options: Dict):
        """تسجيل بداية عملية التحويل"""
        self.info(f"🚀 بدء تحويل الملف: {source_file}", {
            'event': 'conversion_start',
            'source_file': source_file,
            'options': options
        })
    
    def log_conversion_progress(self, progress: int, stage: str):
        """تسجيل تقدم التحويل"""
        self.info(f"⏳ تقدم التحويل: {progress}% - {stage}", {
            'event': 'conversion_progress',
            'progress': progress,
            'stage': stage
        })
    
    def log_conversion_complete(self, output_file: str, duration: float):
        """تسجيل اكتمال التحويل"""
        self.info(f"✅ تم التحويل بنجاح: {output_file} ({duration:.2f}s)", {
            'event': 'conversion_complete',
            'output_file': output_file,
            'duration': duration
        })
    
    def log_conversion_error(self, error: str, details: Dict = None):
        """تسجيل خطأ في التحويل"""
        self.error(f"❌ خطأ في التحويل: {error}", {
            'event': 'conversion_error',
            'error': error,
            'details': details or {}
        })
    
    def get_stats(self) -> Dict:
        """الحصول على إحصائيات السجل"""
        return self.stats.copy()
    
    def get_recent_logs(self, count: int = 100, level: Optional[LogLevel] = None) -> List[Dict]:
        """الحصول على السجلات الحديثة"""
        # هذه دالة مبسطة - في التطبيق الحقيقي ستقرأ من قاعدة البيانات
        logs = []
        try:
            log_file = self.log_dir / f"{self.name}.log"
            if log_file.exists():
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()[-count:]
                    for line in lines:
                        # تحليل بسيط للسطر
                        if ' - ' in line:
                            parts = line.strip().split(' - ', 3)
                            if len(parts) >= 4:
                                logs.append({
                                    'timestamp': parts[0],
                                    'logger': parts[1],
                                    'level': parts[2],
                                    'message': parts[3]
                                })
        except Exception as e:
            self.error(f"خطأ في قراءة السجلات: {e}")
        
        return logs
    
    def clear_logs(self):
        """مسح السجلات"""
        with self.lock:
            # إعادة تعيين الإحصائيات
            for key in self.stats:
                self.stats[key] = 0
            
            # مسح ملفات السجل
            try:
                for log_file in self.log_dir.glob(f"{self.name}*.log*"):
                    log_file.unlink()
                self._setup_default_handlers()
            except Exception as e:
                self.error(f"خطأ في مسح السجلات: {e}")
    
    def set_level(self, level: LogLevel):
        """تغيير مستوى السجل"""
        self.level = level
        self.logger.setLevel(level.value)
        for handler in self.handlers.values():
            handler.setLevel(level.value)

# إنشاء logger افتراضي
default_logger = Logger()

# دوال مساعدة للاستخدام السريع
def debug(message: str, extra: Dict = None):
    default_logger.debug(message, extra)

def info(message: str, extra: Dict = None):
    default_logger.info(message, extra)

def warning(message: str, extra: Dict = None):
    default_logger.warning(message, extra)

def error(message: str, extra: Dict = None):
    default_logger.error(message, extra)

def critical(message: str, extra: Dict = None):
    default_logger.critical(message, extra)
