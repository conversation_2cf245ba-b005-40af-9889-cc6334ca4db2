#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص النظام والمتطلبات لـ Python to EXE Converter Pro v2.0
System and requirements check for Python to EXE Converter Pro v2.0
"""

import sys
import os
import subprocess
import importlib.util
import shutil
from pathlib import Path

def print_header():
    """طباعة رأس التقرير"""
    print("=" * 70)
    print("🔍 فحص النظام - Python to EXE Converter Pro v2.0")
    print("🔍 System Check - Python to EXE Converter Pro v2.0")
    print("=" * 70)
    print()

def check_python():
    """فحص Python"""
    print("🐍 فحص Python:")
    
    version = sys.version_info
    version_str = f"{version.major}.{version.minor}.{version.micro}"
    print(f"   📊 الإصدار: {version_str}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("   ❌ الإصدار قديم - يتطلب Python 3.7+")
        return False
    else:
        print("   ✅ الإصدار متوافق")
    
    print(f"   📍 المسار: {sys.executable}")
    print(f"   🏗️  البناء: {sys.version}")
    return True

def check_pip():
    """فحص pip"""
    print("\n📦 فحص pip:")
    
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True, check=True)
        print(f"   ✅ متوفر: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError:
        print("   ❌ pip غير متوفر")
        return False
    except FileNotFoundError:
        print("   ❌ pip غير موجود")
        return False

def check_package(package_name, display_name=None):
    """فحص حزمة Python"""
    display_name = display_name or package_name
    
    try:
        # إزالة متطلبات الإصدار للفحص
        clean_name = package_name.split('>=')[0].split('==')[0].split('<')[0]
        spec = importlib.util.find_spec(clean_name)
        
        if spec is not None:
            try:
                module = importlib.import_module(clean_name)
                version = getattr(module, '__version__', 'غير معروف')
                print(f"   ✅ {display_name}: {version}")
                return True
            except ImportError:
                print(f"   ⚠️  {display_name}: مثبت لكن لا يمكن استيراده")
                return False
        else:
            print(f"   ❌ {display_name}: غير مثبت")
            return False
    except Exception as e:
        print(f"   ❌ {display_name}: خطأ في الفحص - {e}")
        return False

def check_required_packages():
    """فحص الحزم المطلوبة"""
    print("\n📚 فحص الحزم المطلوبة:")
    
    packages = [
        ("PyInstaller", "PyInstaller - محول Python إلى EXE"),
        ("tkinter", "tkinter - واجهة رسومية (مدمجة)"),
    ]
    
    results = []
    for package, description in packages:
        result = check_package(package, description)
        results.append(result)
    
    return all(results)

def check_optional_packages():
    """فحص الحزم الاختيارية"""
    print("\n🔧 فحص الحزم الاختيارية:")
    
    packages = [
        ("PIL", "Pillow - معالج الصور"),
        ("psutil", "psutil - معلومات النظام"),
    ]
    
    for package, description in packages:
        check_package(package, description)

def check_system_resources():
    """فحص موارد النظام"""
    print("\n💻 فحص موارد النظام:")
    
    # فحص مساحة القرص
    try:
        current_dir = Path.cwd()
        total, used, free = shutil.disk_usage(current_dir)
        
        free_gb = free / (1024**3)
        total_gb = total / (1024**3)
        used_percent = (used / total) * 100
        
        print(f"   💾 مساحة القرص:")
        print(f"      📊 الإجمالي: {total_gb:.1f} جيجابايت")
        print(f"      🆓 المتاح: {free_gb:.1f} جيجابايت")
        print(f"      📈 المستخدم: {used_percent:.1f}%")
        
        if free_gb < 1:
            print("      ⚠️  تحذير: مساحة قليلة (أقل من 1 جيجابايت)")
        else:
            print("      ✅ مساحة كافية")
            
    except Exception as e:
        print(f"   ❌ خطأ في فحص مساحة القرص: {e}")
    
    # فحص الذاكرة (إذا كان psutil متوفر)
    try:
        import psutil
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        available_gb = memory.available / (1024**3)
        
        print(f"   🧠 الذاكرة:")
        print(f"      📊 الإجمالي: {memory_gb:.1f} جيجابايت")
        print(f"      🆓 المتاح: {available_gb:.1f} جيجابايت")
        print(f"      📈 الاستخدام: {memory.percent:.1f}%")
        
        if memory_gb < 4:
            print("      ⚠️  تحذير: ذاكرة قليلة (أقل من 4 جيجابايت)")
        else:
            print("      ✅ ذاكرة كافية")
            
    except ImportError:
        print("   ℹ️  معلومات الذاكرة غير متوفرة (psutil غير مثبت)")
    except Exception as e:
        print(f"   ❌ خطأ في فحص الذاكرة: {e}")

def check_files():
    """فحص الملفات المطلوبة"""
    print("\n📁 فحص الملفات:")
    
    required_files = [
        "pyinstaller_gui.py",
        "glass_theme.py",
        "config.py",
        "README.md"
    ]
    
    optional_files = [
        "requirements.txt",
        "settings.json",
        "run.bat",
        "run.sh",
        "install_requirements.py"
    ]
    
    print("   📋 الملفات المطلوبة:")
    all_required_exist = True
    for file in required_files:
        if os.path.exists(file):
            print(f"      ✅ {file}")
        else:
            print(f"      ❌ {file} - مفقود")
            all_required_exist = False
    
    print("   🔧 الملفات الاختيارية:")
    for file in optional_files:
        if os.path.exists(file):
            print(f"      ✅ {file}")
        else:
            print(f"      ⚠️  {file} - مفقود (اختياري)")
    
    return all_required_exist

def check_permissions():
    """فحص الصلاحيات"""
    print("\n🔐 فحص الصلاحيات:")
    
    current_dir = Path.cwd()
    
    # فحص صلاحية القراءة
    if os.access(current_dir, os.R_OK):
        print("   ✅ صلاحية القراءة")
    else:
        print("   ❌ صلاحية القراءة مفقودة")
    
    # فحص صلاحية الكتابة
    if os.access(current_dir, os.W_OK):
        print("   ✅ صلاحية الكتابة")
    else:
        print("   ❌ صلاحية الكتابة مفقودة")
    
    # فحص صلاحية التنفيذ
    if os.access(current_dir, os.X_OK):
        print("   ✅ صلاحية التنفيذ")
    else:
        print("   ❌ صلاحية التنفيذ مفقودة")

def generate_report():
    """إنشاء تقرير شامل"""
    print("\n📊 ملخص الفحص:")
    
    checks = [
        ("Python", check_python()),
        ("pip", check_pip()),
        ("الحزم المطلوبة", check_required_packages()),
        ("الملفات", check_files())
    ]
    
    passed = sum(1 for _, result in checks if result)
    total = len(checks)
    
    print(f"   📈 النتيجة: {passed}/{total} فحوصات نجحت")
    
    if passed == total:
        print("   🎉 النظام جاهز تماماً!")
        print("   🚀 يمكنك تشغيل التطبيق بأمان")
        return True
    else:
        print("   ⚠️  يوجد مشاكل تحتاج حل")
        print("   💡 راجع التفاصيل أعلاه")
        return False

def main():
    """الدالة الرئيسية"""
    print_header()
    
    try:
        # تشغيل جميع الفحوصات
        check_python()
        check_pip()
        check_required_packages()
        check_optional_packages()
        check_system_resources()
        check_files()
        check_permissions()
        
        # إنشاء التقرير النهائي
        success = generate_report()
        
        print("\n" + "=" * 70)
        if success:
            print("✅ فحص النظام مكتمل - النظام جاهز!")
        else:
            print("⚠️  فحص النظام مكتمل - يوجد مشاكل")
        print("=" * 70)
        
        return success
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إلغاء الفحص بواسطة المستخدم")
        return False
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        exit_code = 0 if success else 1
    except Exception as e:
        print(f"خطأ فادح: {e}")
        exit_code = 2
    
    print(f"\nاضغط Enter للخروج...")
    input()
    sys.exit(exit_code)
