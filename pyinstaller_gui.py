#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python to EXE Converter Pro v2.0
واجهة رسومية احترافية بتصميم زجاجي لتحويل ملفات Python إلى تطبيقات تنفيذية
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
import subprocess
import threading
import json
import time
from datetime import datetime
from glass_theme import ModernGlassTheme
from config import Config

class PyInstallerGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.load_settings()
        self.create_widgets()
        self.check_requirements()
        
    def setup_window(self):
        """إعداد النافذة الرئيسية بتصميم عصري"""
        self.root.title("🚀 Python to EXE Converter Pro v2.0 - Modern Glass UI")
        self.root.geometry("1400x800")
        self.root.minsize(1200, 700)
        self.root.configure(bg=ModernGlassTheme.COLORS['background_primary'])

        # توسيط النافذة
        self.center_window()

        # أيقونة التطبيق (إذا كانت متوفرة)
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass

        # تطبيق تأثيرات النافذة
        self.root.attributes('-alpha', 0.98)  # شفافية خفيفة
    
    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def setup_variables(self):
        """إعداد المتغيرات"""
        self.source_path = tk.StringVar()
        self.output_path = tk.StringVar()
        self.icon_path = tk.StringVar()
        
        # خيارات التحويل
        self.onefile = tk.BooleanVar(value=True)
        self.noconsole = tk.BooleanVar(value=False)
        self.debug = tk.BooleanVar(value=False)
        self.optimize = tk.BooleanVar(value=False)
        self.upx = tk.BooleanVar(value=False)
        
        # متغيرات الحالة
        self.is_converting = False
        self.conversion_progress = 0
        
    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            if os.path.exists('settings.json'):
                with open('settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.output_path.set(settings.get('output_path', ''))
                    self.onefile.set(settings.get('onefile', True))
                    self.noconsole.set(settings.get('noconsole', False))
                    self.debug.set(settings.get('debug', False))
                    self.optimize.set(settings.get('optimize', False))
                    self.upx.set(settings.get('upx', False))
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            settings = {
                'output_path': self.output_path.get(),
                'onefile': self.onefile.get(),
                'noconsole': self.noconsole.get(),
                'debug': self.debug.get(),
                'optimize': self.optimize.get(),
                'upx': self.upx.get()
            }
            with open('settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة العصرية"""
        # الإطار الرئيسي بتدرج ديناميكي
        main_frame, main_canvas = ModernGlassTheme.create_modern_gradient_frame(
            self.root,
            gradient_type='aurora',
            animate=True
        )
        main_frame.pack(fill='both', expand=True, padx=15, pady=15)

        # العنوان الرئيسي مع تأثيرات
        self.create_modern_header(main_canvas)

        # الإطار الأفقي الرئيسي مع تصميم عصري
        content_frame = tk.Frame(main_canvas, bg=ModernGlassTheme.COLORS['background_primary'])
        content_frame.pack(fill='both', expand=True, padx=25, pady=15)

        # العمود الأيسر - لوحة الإعدادات العصرية
        self.create_modern_settings_panel(content_frame)

        # العمود الأيمن - لوحة المعاينة والسجل العصرية
        self.create_modern_preview_panel(content_frame)

        # شريط الحالة العصري
        self.create_modern_status_bar()

        # إضافة إشعار ترحيب
        self.root.after(1000, lambda: ModernGlassTheme.create_modern_notification(
            self.root,
            "مرحباً بك في Python to EXE Converter Pro v2.0 🚀",
            'info',
            4000
        ))

    def create_modern_header(self, parent):
        """إنشاء رأس عصري مع تأثيرات"""
        header_frame = tk.Frame(parent, bg=ModernGlassTheme.COLORS['background_primary'])
        header_frame.pack(fill='x', pady=(10, 25))

        # بطاقة العنوان الرئيسي
        title_card = ModernGlassTheme.create_modern_glass_card(
            header_frame,
            title="🚀 Python to EXE Converter Pro v2.0",
            content="واجهة عصرية زجاجية ديناميكية\nتحويل ملفات Python إلى تطبيقات تنفيذية",
            width=600,
            height=120,
            style='gradient'
        )
        title_card.pack(anchor='center')

        # شريط الأدوات السريعة
        self.create_quick_toolbar(header_frame)

    def create_quick_toolbar(self, parent):
        """إنشاء شريط أدوات سريع"""
        toolbar_frame = tk.Frame(parent, bg=ModernGlassTheme.COLORS['background_primary'])
        toolbar_frame.pack(pady=(15, 0))

        # أزرار سريعة
        quick_buttons = [
            ("🔍", "فحص سريع", self.quick_check),
            ("📁", "فتح سريع", self.quick_open),
            ("⚙️", "إعدادات", self.quick_settings),
            ("❓", "مساعدة", self.quick_help)
        ]

        for icon, tooltip, command in quick_buttons:
            btn = ModernGlassTheme.create_modern_glass_button(
                toolbar_frame,
                icon,
                command,
                style='glass',
                width=3,
                height=1
            )
            btn.pack(side='left', padx=5)

    def quick_check(self):
        """فحص سريع"""
        ModernGlassTheme.create_modern_notification(
            self.root, "جاري الفحص السريع...", 'info', 2000
        )
        self.check_requirements()

    def quick_open(self):
        """فتح سريع"""
        self.browse_file()

    def quick_settings(self):
        """إعدادات سريعة"""
        ModernGlassTheme.create_modern_notification(
            self.root, "إعدادات متقدمة قريباً...", 'info', 2000
        )

    def quick_help(self):
        """مساعدة سريعة"""
        help_text = """
🚀 دليل الاستخدام السريع:

1. اختر ملف Python أو مجلد
2. حدد مجلد الحفظ
3. اضبط الخيارات المتقدمة
4. انقر 'بدء التحويل'

💡 نصائح:
• استخدم 'ملف واحد' للتوزيع السهل
• فعل 'إخفاء الكونسول' للواجهات الرسومية
• استخدم 'فحص المتطلبات' قبل التحويل
        """

        ModernGlassTheme.create_modern_notification(
            self.root, help_text.strip(), 'info', 8000
        )

    def create_modern_settings_panel(self, parent):
        """إنشاء لوحة الإعدادات العصرية"""
        # بطاقة الإعدادات الرئيسية
        settings_card = ModernGlassTheme.create_modern_glass_card(
            parent,
            title="⚙️ إعدادات التحويل",
            content="",
            width=650,
            height=550,
            style='glass'
        )
        settings_card.pack(side='left', fill='both', expand=True, padx=(0, 15))

        # إطار داخلي للمحتوى
        inner_frame = tk.Frame(settings_card, bg=ModernGlassTheme.COLORS['background_primary'])
        inner_frame.pack(fill='both', expand=True, padx=20, pady=60)

        # قسم اختيار الملف المصدر
        self.create_modern_source_selection(inner_frame)

        # قسم اختيار مجلد الحفظ
        self.create_modern_output_selection(inner_frame)

        # قسم الخيارات المتقدمة
        self.create_modern_advanced_options(inner_frame)

        # قسم أزرار العمليات
        self.create_modern_action_buttons(inner_frame)

    def create_modern_source_selection(self, parent):
        """إنشاء قسم اختيار الملف المصدر العصري"""
        source_frame = tk.Frame(parent, bg=ModernGlassTheme.COLORS['background_primary'])
        source_frame.pack(fill='x', pady=(0, 20))

        # عنوان القسم
        title_label = tk.Label(
            source_frame,
            text="📁 الملف المصدر",
            font=('Segoe UI', 12, 'bold'),
            fg=ModernGlassTheme.COLORS['text_primary'],
            bg=ModernGlassTheme.COLORS['background_primary']
        )
        title_label.pack(anchor='w', pady=(0, 10))

        # حقل الإدخال العصري
        self.source_input = ModernGlassTheme.create_modern_input_field(
            source_frame,
            placeholder="اختر ملف Python أو مجلد المشروع...",
            width=500,
            height=45
        )
        self.source_input.pack(fill='x', pady=(0, 10))

        # أزرار الاختيار العصرية
        buttons_frame = tk.Frame(source_frame, bg=ModernGlassTheme.COLORS['background_primary'])
        buttons_frame.pack(fill='x')

        file_btn = ModernGlassTheme.create_modern_glass_button(
            buttons_frame,
            "📄 اختيار ملف",
            self.browse_file,
            style='primary',
            width=15,
            height=2
        )
        file_btn.pack(side='left', padx=(0, 10))

        folder_btn = ModernGlassTheme.create_modern_glass_button(
            buttons_frame,
            "📁 اختيار مجلد",
            self.browse_folder,
            style='secondary',
            width=15,
            height=2
        )
        folder_btn.pack(side='left')

    def create_modern_output_selection(self, parent):
        """إنشاء قسم اختيار مجلد الحفظ العصري"""
        output_frame = tk.Frame(parent, bg=ModernGlassTheme.COLORS['background_primary'])
        output_frame.pack(fill='x', pady=(0, 20))

        # عنوان القسم
        title_label = tk.Label(
            output_frame,
            text="💾 مجلد الحفظ",
            font=('Segoe UI', 12, 'bold'),
            fg=ModernGlassTheme.COLORS['text_primary'],
            bg=ModernGlassTheme.COLORS['background_primary']
        )
        title_label.pack(anchor='w', pady=(0, 10))

        # إطار الإدخال والزر
        input_frame = tk.Frame(output_frame, bg=ModernGlassTheme.COLORS['background_primary'])
        input_frame.pack(fill='x')

        # حقل الإدخال
        self.output_input = ModernGlassTheme.create_modern_input_field(
            input_frame,
            placeholder="اختر مجلد حفظ الملف التنفيذي...",
            width=400,
            height=45
        )
        self.output_input.pack(side='left', fill='x', expand=True, padx=(0, 10))

        # زر التصفح
        browse_btn = ModernGlassTheme.create_modern_glass_button(
            input_frame,
            "📁 تصفح",
            self.browse_output,
            style='accent',
            width=12,
            height=2
        )
        browse_btn.pack(side='right')

    def create_modern_advanced_options(self, parent):
        """إنشاء قسم الخيارات المتقدمة العصري"""
        options_frame = tk.Frame(parent, bg=ModernGlassTheme.COLORS['background_primary'])
        options_frame.pack(fill='x', pady=(0, 20))

        # عنوان القسم
        title_label = tk.Label(
            options_frame,
            text="🔧 الخيارات المتقدمة",
            font=('Segoe UI', 12, 'bold'),
            fg=ModernGlassTheme.COLORS['text_primary'],
            bg=ModernGlassTheme.COLORS['background_primary']
        )
        title_label.pack(anchor='w', pady=(0, 15))

        # إطار الخيارات
        toggles_frame = tk.Frame(options_frame, bg=ModernGlassTheme.COLORS['background_primary'])
        toggles_frame.pack(fill='x')

        # الصف الأول من المفاتيح
        row1 = tk.Frame(toggles_frame, bg=ModernGlassTheme.COLORS['background_primary'])
        row1.pack(fill='x', pady=(0, 10))

        self.onefile_toggle = ModernGlassTheme.create_modern_toggle_switch(
            row1, "📦 ملف واحد", self.onefile.get(),
            lambda state: self.onefile.set(state)
        )
        self.onefile_toggle.pack(side='left', padx=(0, 30))

        self.noconsole_toggle = ModernGlassTheme.create_modern_toggle_switch(
            row1, "🖥️ إخفاء الكونسول", self.noconsole.get(),
            lambda state: self.noconsole.set(state)
        )
        self.noconsole_toggle.pack(side='left', padx=(0, 30))

        # الصف الثاني من المفاتيح
        row2 = tk.Frame(toggles_frame, bg=ModernGlassTheme.COLORS['background_primary'])
        row2.pack(fill='x', pady=(0, 10))

        self.debug_toggle = ModernGlassTheme.create_modern_toggle_switch(
            row2, "🐛 وضع التصحيح", self.debug.get(),
            lambda state: self.debug.set(state)
        )
        self.debug_toggle.pack(side='left', padx=(0, 30))

        self.optimize_toggle = ModernGlassTheme.create_modern_toggle_switch(
            row2, "⚡ تحسين الحجم", self.optimize.get(),
            lambda state: self.optimize.set(state)
        )
        self.optimize_toggle.pack(side='left', padx=(0, 30))

        # الصف الثالث - أيقونة
        row3 = tk.Frame(toggles_frame, bg=ModernGlassTheme.COLORS['background_primary'])
        row3.pack(fill='x', pady=(10, 0))

        # عنوان الأيقونة
        icon_label = tk.Label(
            row3,
            text="🎨 أيقونة التطبيق:",
            font=('Segoe UI', 10),
            fg=ModernGlassTheme.COLORS['text_secondary'],
            bg=ModernGlassTheme.COLORS['background_primary']
        )
        icon_label.pack(anchor='w', pady=(0, 5))

        # حقل الأيقونة
        icon_input_frame = tk.Frame(row3, bg=ModernGlassTheme.COLORS['background_primary'])
        icon_input_frame.pack(fill='x')

        self.icon_input = ModernGlassTheme.create_modern_input_field(
            icon_input_frame,
            placeholder="اختر ملف أيقونة (.ico, .png)...",
            width=350,
            height=40
        )
        self.icon_input.pack(side='left', fill='x', expand=True, padx=(0, 10))

        icon_btn = ModernGlassTheme.create_modern_glass_button(
            icon_input_frame,
            "🎨 تصفح",
            self.browse_icon,
            style='accent',
            width=10,
            height=1
        )
        icon_btn.pack(side='right')

    def open_ai_icon_generator(self):
        """فتح أداة إنشاء الأيقونات بالذكاء الاصطناعي"""
        self.add_to_log("🤖 فتح أداة إنشاء الأيقونات بالذكاء الاصطناعي...")
        try:
            # استخدام sys.executable لضمان استخدام نفس مفسر بايثون
            subprocess.Popen([sys.executable, "ai_icon_generator.py"])
            ModernGlassTheme.create_modern_notification(
                self.root,
                "تم فتح أداة إنشاء الأيقونات بالذكاء الاصطناعي",
                'info',
                3000
            )
        except FileNotFoundError:
            error_msg = "❌ خطأ: لم يتم العثور على ملف 'ai_icon_generator.py'."
            self.add_to_log(error_msg)
            messagebox.showerror("خطأ في التشغيل", error_msg)
        except Exception as e:
            error_msg = f"❌ فشل في فتح الأداة: {e}"
            self.add_to_log(error_msg)
            messagebox.showerror("خطأ في التشغيل", error_msg)

    def open_icon_editor(self):
        """فتح أداة تحرير الأيقونات"""
        self.add_to_log("✏️ فتح أداة تحرير الأيقونات...")
        try:
            # استخدام sys.executable لضمان استخدام نفس مفسر بايثون
            subprocess.Popen([sys.executable, "icon_editor.py"])
            ModernGlassTheme.create_modern_notification(
                self.root,
                "تم فتح أداة تحرير الأيقونات",
                'info',
                3000
            )
        except FileNotFoundError:
            error_msg = "❌ خطأ: لم يتم العثور على ملف 'icon_editor.py'."
            self.add_to_log(error_msg)
            messagebox.showerror("خطأ في التشغيل", error_msg)
        except Exception as e:
            error_msg = f"❌ فشل في فتح الأداة: {e}"
            self.add_to_log(error_msg)
            messagebox.showerror("خطأ في التشغيل", error_msg)

        # إطار أزرار أدوات الأيقونات
        icon_tools_frame = tk.Frame(row3, bg=ModernGlassTheme.COLORS['background_primary'])
        icon_tools_frame.pack(fill='x', pady=(10, 0))

        ai_icon_btn = ModernGlassTheme.create_modern_glass_button(
            icon_tools_frame,
            "🎨 إنشاء بالـ AI",
            self.open_ai_icon_generator,
            style='secondary',
            width=15,
            height=1
        )
        ai_icon_btn.pack(side='left', padx=(0, 10))

        edit_icon_btn = ModernGlassTheme.create_modern_glass_button(
            icon_tools_frame,
            "✏️ تحرير أيقونة",
            self.open_icon_editor,
            style='glass',
            width=15,
            height=1
        )
        edit_icon_btn.pack(side='left')

    def create_modern_action_buttons(self, parent):
        """إنشاء أزرار العمليات العصرية"""
        buttons_frame = tk.Frame(parent, bg=ModernGlassTheme.COLORS['background_primary'])
        buttons_frame.pack(fill='x', pady=(20, 0))

        # الصف الأول - أزرار المساعدة
        help_row = tk.Frame(buttons_frame, bg=ModernGlassTheme.COLORS['background_primary'])
        help_row.pack(fill='x', pady=(0, 15))

        preview_btn = ModernGlassTheme.create_modern_glass_button(
            help_row,
            "👁️ معاينة الأمر",
            self.preview_command,
            style='glass',
            width=18,
            height=2
        )
        preview_btn.pack(side='left', padx=(0, 15))

        check_btn = ModernGlassTheme.create_modern_glass_button(
            help_row,
            "🔍 فحص المتطلبات",
            self.check_requirements,
            style='glass',
            width=18,
            height=2
        )
        check_btn.pack(side='left')

        # الصف الثاني - زر التحويل الرئيسي
        main_row = tk.Frame(buttons_frame, bg=ModernGlassTheme.COLORS['background_primary'])
        main_row.pack(fill='x')

        self.convert_btn = ModernGlassTheme.create_modern_glass_button(
            main_row,
            "🚀 بدء التحويل",
            self.start_conversion,
            style='success',
            width=40,
            height=3
        )
        self.convert_btn.pack(fill='x')

    def create_modern_preview_panel(self, parent):
        """إنشاء لوحة المعاينة والسجل العصرية"""
        # بطاقة المعاينة والسجل
        preview_card = ModernGlassTheme.create_modern_glass_card(
            parent,
            title="📊 المعاينة والسجل",
            content="",
            width=650,
            height=550,
            style='gradient'
        )
        preview_card.pack(side='right', fill='both', expand=True)

        # إطار داخلي للمحتوى
        inner_frame = tk.Frame(preview_card, bg=ModernGlassTheme.COLORS['background_primary'])
        inner_frame.pack(fill='both', expand=True, padx=20, pady=60)

        # شريط التبويبات العصري
        self.create_modern_tabs(inner_frame)

        # شريط التقدم العصري
        self.create_modern_progress_section(inner_frame)

    def create_modern_tabs(self, parent):
        """إنشاء تبويبات عصرية"""
        tabs_frame = tk.Frame(parent, bg=ModernGlassTheme.COLORS['background_primary'])
        tabs_frame.pack(fill='both', expand=True, pady=(0, 20))

        # أزرار التبويبات العصرية
        tab_buttons_frame = tk.Frame(tabs_frame, bg=ModernGlassTheme.COLORS['background_primary'])
        tab_buttons_frame.pack(fill='x', pady=(0, 15))

        self.current_tab = tk.StringVar(value="command")

        # تبويب معاينة الأمر
        self.command_tab_btn = ModernGlassTheme.create_modern_glass_button(
            tab_buttons_frame,
            "💻 معاينة الأمر",
            lambda: self.switch_modern_tab("command"),
            style='primary',
            width=15,
            height=1
        )
        self.command_tab_btn.pack(side='left', padx=(0, 10))

        # تبويب السجل
        self.log_tab_btn = ModernGlassTheme.create_modern_glass_button(
            tab_buttons_frame,
            "📋 سجل العمليات",
            lambda: self.switch_modern_tab("log"),
            style='glass',
            width=15,
            height=1
        )
        self.log_tab_btn.pack(side='left', padx=(0, 10))

        # تبويب الملفات
        self.files_tab_btn = ModernGlassTheme.create_modern_glass_button(
            tab_buttons_frame,
            "📁 الملفات المحولة",
            lambda: self.switch_modern_tab("files"),
            style='glass',
            width=15,
            height=1
        )
        self.files_tab_btn.pack(side='left')

        # محتوى التبويبات
        self.tabs_content = tk.Frame(tabs_frame, bg=ModernGlassTheme.COLORS['background_primary'])
        self.tabs_content.pack(fill='both', expand=True)

        # إنشاء محتوى كل تبويب
        self.create_modern_command_tab()
        self.create_modern_log_tab()
        self.create_modern_files_tab()

        # عرض التبويب الافتراضي
        self.switch_modern_tab("command")

    def create_modern_command_tab(self):
        """إنشاء تبويب معاينة الأمر العصري"""
        self.command_frame = tk.Frame(self.tabs_content, bg=ModernGlassTheme.COLORS['background_primary'])

        # بطاقة معاينة الأمر
        command_card = ModernGlassTheme.create_modern_glass_card(
            self.command_frame,
            title="💻 أمر PyInstaller",
            content="",
            width=580,
            height=300,
            style='glass'
        )
        command_card.pack(fill='both', expand=True)

        # منطقة النص داخل البطاقة
        text_frame = tk.Frame(command_card, bg=ModernGlassTheme.COLORS['background_primary'])
        text_frame.pack(fill='both', expand=True, padx=20, pady=60)

        self.command_text = tk.Text(
            text_frame,
            font=('Consolas', 10),
            bg=ModernGlassTheme.COLORS['background_primary'],
            fg=ModernGlassTheme.COLORS['text_primary'],
            insertbackground=ModernGlassTheme.COLORS['accent'],
            relief='flat',
            bd=0,
            wrap=tk.WORD,
            highlightthickness=0
        )

        # شريط التمرير العصري
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.command_text.yview)
        self.command_text.configure(yscrollcommand=scrollbar.set)

        self.command_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # تحديث المعاينة
        self.update_command_preview()

    def create_modern_log_tab(self):
        """إنشاء تبويب السجل العصري"""
        self.log_frame = tk.Frame(self.tabs_content, bg=ModernGlassTheme.COLORS['background_primary'])

        # بطاقة السجل
        log_card = ModernGlassTheme.create_modern_glass_card(
            self.log_frame,
            title="📋 سجل العمليات المباشر",
            content="",
            width=580,
            height=300,
            style='glass'
        )
        log_card.pack(fill='both', expand=True)

        # منطقة النص داخل البطاقة
        text_frame = tk.Frame(log_card, bg=ModernGlassTheme.COLORS['background_primary'])
        text_frame.pack(fill='both', expand=True, padx=20, pady=60)

        self.log_text = tk.Text(
            text_frame,
            font=('Consolas', 9),
            bg=ModernGlassTheme.COLORS['background_secondary'],
            fg=ModernGlassTheme.COLORS['text_secondary'],
            insertbackground=ModernGlassTheme.COLORS['accent'],
            relief='flat',
            bd=0,
            wrap=tk.WORD,
            highlightthickness=0
        )

        # شريط التمرير
        log_scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side='left', fill='both', expand=True)
        log_scrollbar.pack(side='right', fill='y')

        # إضافة رسالة ترحيب
        self.add_to_log("🚀 مرحباً بك في Python to EXE Converter Pro v2.0 - Modern Glass UI")
        self.add_to_log("✨ واجهة عصرية زجاجية ديناميكية مع تأثيرات متقدمة")
        self.add_to_log("📝 جاهز لبدء التحويل...")

    def create_modern_files_tab(self):
        """إنشاء تبويب الملفات العصري"""
        self.files_frame = tk.Frame(self.tabs_content, bg=ModernGlassTheme.COLORS['background_primary'])

        # بطاقة الملفات
        files_card = ModernGlassTheme.create_modern_glass_card(
            self.files_frame,
            title="📁 قائمة الملفات المحولة",
            content="",
            width=580,
            height=300,
            style='glass'
        )
        files_card.pack(fill='both', expand=True)

        # منطقة النص داخل البطاقة
        text_frame = tk.Frame(files_card, bg=ModernGlassTheme.COLORS['background_primary'])
        text_frame.pack(fill='both', expand=True, padx=20, pady=60)

        self.files_text = tk.Text(
            text_frame,
            font=('Segoe UI', 10),
            bg=ModernGlassTheme.COLORS['background_primary'],
            fg=ModernGlassTheme.COLORS['text_primary'],
            insertbackground=ModernGlassTheme.COLORS['accent'],
            relief='flat',
            bd=0,
            wrap=tk.WORD,
            highlightthickness=0
        )

        # شريط التمرير
        files_scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.files_text.yview)
        self.files_text.configure(yscrollcommand=files_scrollbar.set)

        self.files_text.pack(side='left', fill='both', expand=True)
        files_scrollbar.pack(side='right', fill='y')

        # إضافة رسالة افتراضية
        self.files_text.insert('1.0', "📂 لم يتم تحويل أي ملفات بعد...\n\n")
        self.files_text.insert('end', "🔄 ستظهر الملفات المحولة هنا بعد التحويل الناجح\n")
        self.files_text.insert('end', "✨ مع معلومات مفصلة عن كل ملف محول")

    def create_modern_progress_section(self, parent):
        """إنشاء قسم شريط التقدم العصري"""
        progress_frame = tk.Frame(parent, bg=ModernGlassTheme.COLORS['background_primary'])
        progress_frame.pack(fill='x', pady=(15, 0))

        # عنوان التقدم
        progress_label = tk.Label(
            progress_frame,
            text="📊 تقدم العملية:",
            font=('Segoe UI', 11, 'bold'),
            fg=ModernGlassTheme.COLORS['text_primary'],
            bg=ModernGlassTheme.COLORS['background_primary']
        )
        progress_label.pack(anchor='w', pady=(0, 10))

        # شريط التقدم العصري
        self.progress_bar = ModernGlassTheme.create_modern_progress_bar(
            progress_frame,
            width=580,
            height=35,
            style='neon'
        )
        self.progress_bar.pack(fill='x', pady=(0, 10))

        # تسمية حالة التقدم
        self.progress_status_label = tk.Label(
            progress_frame,
            text="⏳ في انتظار بدء العملية...",
            font=('Segoe UI', 10),
            fg=ModernGlassTheme.COLORS['text_secondary'],
            bg=ModernGlassTheme.COLORS['background_primary']
        )
        self.progress_status_label.pack(anchor='w')

    def create_modern_status_bar(self):
        """إنشاء شريط الحالة العصري"""
        # بطاقة شريط الحالة
        status_card = ModernGlassTheme.create_modern_glass_card(
            self.root,
            title="",
            content="",
            width=1370,
            height=60,
            style='glass'
        )
        status_card.pack(fill='x', side='bottom', padx=15, pady=(0, 15))

        # محتوى شريط الحالة
        status_content = tk.Frame(status_card, bg=ModernGlassTheme.COLORS['background_primary'])
        status_content.pack(fill='both', expand=True, padx=20, pady=15)

        # أيقونة الحالة
        self.status_icon = tk.Label(
            status_content,
            text="✅",
            font=('Segoe UI', 14),
            fg=ModernGlassTheme.COLORS['success'],
            bg=ModernGlassTheme.COLORS['background_primary']
        )
        self.status_icon.pack(side='left', padx=(0, 10))

        # نص الحالة
        self.status_label = tk.Label(
            status_content,
            text="جاهز للاستخدام - Modern Glass UI",
            font=('Segoe UI', 11),
            fg=ModernGlassTheme.COLORS['text_primary'],
            bg=ModernGlassTheme.COLORS['background_primary']
        )
        self.status_label.pack(side='left')

        # معلومات إضافية على اليمين
        info_frame = tk.Frame(status_content, bg=ModernGlassTheme.COLORS['background_primary'])
        info_frame.pack(side='right')

        version_label = tk.Label(
            info_frame,
            text="v2.0 | Modern Glass Theme",
            font=('Segoe UI', 9),
            fg=ModernGlassTheme.COLORS['text_muted'],
            bg=ModernGlassTheme.COLORS['background_primary']
        )
        version_label.pack(side='right')

    # ==================== وظائف التفاعل العصرية ====================

    def switch_modern_tab(self, tab_name):
        """تبديل التبويبات العصرية مع تأثيرات"""
        self.current_tab.set(tab_name)

        # إخفاء جميع التبويبات
        for widget in self.tabs_content.winfo_children():
            widget.pack_forget()

        # عرض التبويب المحدد مع تأثير
        if tab_name == "command":
            self.command_frame.pack(fill='both', expand=True)
            self.update_command_preview()
            # تحديث نمط الزر
            self.update_tab_button_style(self.command_tab_btn, True)
            self.update_tab_button_style(self.log_tab_btn, False)
            self.update_tab_button_style(self.files_tab_btn, False)
        elif tab_name == "log":
            self.log_frame.pack(fill='both', expand=True)
            self.update_tab_button_style(self.command_tab_btn, False)
            self.update_tab_button_style(self.log_tab_btn, True)
            self.update_tab_button_style(self.files_tab_btn, False)
        elif tab_name == "files":
            self.files_frame.pack(fill='both', expand=True)
            self.update_tab_button_style(self.command_tab_btn, False)
            self.update_tab_button_style(self.log_tab_btn, False)
            self.update_tab_button_style(self.files_tab_btn, True)

    def update_tab_button_style(self, button, active):
        """تحديث نمط أزرار التبويبات"""
        # هذا مجرد محاكاة - في التطبيق الحقيقي يمكن تحديث الألوان
        pass

    def browse_file(self):
        """تصفح ملف Python مع إشعارات عصرية"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف Python",
            filetypes=[
                ("Python files", "*.py"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.source_input.set_value(file_path)
            self.source_path.set(file_path)
            self.add_to_log(f"📄 تم اختيار الملف: {os.path.basename(file_path)}")
            self.update_command_preview()
            self.update_modern_status("✅ تم اختيار الملف المصدر", "success")

            # إشعار نجاح
            ModernGlassTheme.create_modern_notification(
                self.root,
                f"تم اختيار الملف: {os.path.basename(file_path)}",
                'success',
                3000
            )

    def browse_folder(self):
        """تصفح مجلد Python مع إشعارات عصرية"""
        folder_path = filedialog.askdirectory(title="اختر مجلد Python")
        if folder_path:
            # البحث عن ملف main.py أو __main__.py
            main_files = []
            for file in os.listdir(folder_path):
                if file in ['main.py', '__main__.py', 'app.py']:
                    main_files.append(file)

            if main_files:
                main_file = os.path.join(folder_path, main_files[0])
                self.source_input.set_value(main_file)
                self.source_path.set(main_file)
                self.add_to_log(f"📁 تم اختيار المجلد: {os.path.basename(folder_path)}")
                self.add_to_log(f"📄 الملف الرئيسي: {main_files[0]}")

                ModernGlassTheme.create_modern_notification(
                    self.root,
                    f"تم اختيار المجلد مع الملف الرئيسي: {main_files[0]}",
                    'success',
                    4000
                )
            else:
                self.source_input.set_value(folder_path)
                self.source_path.set(folder_path)
                self.add_to_log(f"📁 تم اختيار المجلد: {os.path.basename(folder_path)}")
                self.add_to_log("⚠️ لم يتم العثور على ملف رئيسي، يرجى تحديد الملف يدوياً")

                ModernGlassTheme.create_modern_notification(
                    self.root,
                    "تم اختيار المجلد - لم يتم العثور على ملف رئيسي",
                    'warning',
                    4000
                )

            self.update_command_preview()
            self.update_modern_status("✅ تم اختيار المجلد المصدر", "success")

    def browse_output(self):
        """تصفح مجلد الحفظ مع إشعارات عصرية"""
        folder_path = filedialog.askdirectory(title="اختر مجلد الحفظ")
        if folder_path:
            self.output_input.set_value(folder_path)
            self.output_path.set(folder_path)
            self.add_to_log(f"💾 مجلد الحفظ: {folder_path}")
            self.update_command_preview()
            self.update_modern_status("✅ تم اختيار مجلد الحفظ", "success")
            self.save_settings()

            ModernGlassTheme.create_modern_notification(
                self.root,
                "تم تحديد مجلد الحفظ بنجاح",
                'success',
                3000
            )

    def browse_icon(self):
        """تصفح ملف الأيقونة مع إشعارات عصرية"""
        icon_path = filedialog.askopenfilename(
            title="اختر ملف الأيقونة",
            filetypes=[
                ("Icon files", "*.ico"),
                ("PNG files", "*.png"),
                ("JPG files", "*.jpg"),
                ("JPEG files", "*.jpeg"),
                ("BMP files", "*.bmp"),
                ("All files", "*.*")
            ]
        )
        if icon_path:
            self.icon_input.set_value(icon_path)
            self.icon_path.set(icon_path)
            self.add_to_log(f"🎨 تم اختيار الأيقونة: {os.path.basename(icon_path)}")
            self.update_command_preview()
            self.update_modern_status("✅ تم اختيار الأيقونة", "success")

            ModernGlassTheme.create_modern_notification(
                self.root,
                f"تم اختيار الأيقونة: {os.path.basename(icon_path)}",
                'success',
                3000
            )

    def update_modern_status(self, message, status_type="info"):
        """تحديث شريط الحالة العصري"""
        # تحديث الأيقونة حسب النوع
        icons = {
            'info': '💡',
            'success': '✅',
            'warning': '⚠️',
            'error': '❌',
            'loading': '⏳'
        }

        colors = {
            'info': ModernGlassTheme.COLORS['accent'],
            'success': ModernGlassTheme.COLORS['success'],
            'warning': ModernGlassTheme.COLORS['warning'],
            'error': ModernGlassTheme.COLORS['error'],
            'loading': ModernGlassTheme.COLORS['primary']
        }

        icon = icons.get(status_type, '💡')
        color = colors.get(status_type, ModernGlassTheme.COLORS['accent'])

        if hasattr(self, 'status_icon'):
            self.status_icon.configure(text=icon, fg=color)

        if hasattr(self, 'status_label'):
            self.status_label.configure(text=message)

    def update_command_preview(self):
        """تحديث معاينة الأمر العصرية"""
        if not hasattr(self, 'command_text'):
            return

        command = self.build_pyinstaller_command()

        self.command_text.delete('1.0', tk.END)

        # إضافة محتوى منسق وملون
        self.command_text.insert('1.0', "💻 أمر PyInstaller الذي سيتم تنفيذه:\n")
        self.command_text.insert('end', "=" * 60 + "\n\n")

        # الأمر الفعلي
        self.command_text.insert('end', command + "\n\n")

        self.command_text.insert('end', "=" * 60 + "\n")
        self.command_text.insert('end', "📝 ملاحظات مهمة:\n\n")
        self.command_text.insert('end', "🔹 تأكد من صحة مسار الملف المصدر\n")
        self.command_text.insert('end', "🔹 تأكد من وجود مساحة كافية في مجلد الحفظ\n")
        self.command_text.insert('end', "🔹 قد تستغرق العملية عدة دقائق حسب حجم المشروع\n")
        self.command_text.insert('end', "🔹 استخدم 'فحص المتطلبات' للتأكد من جاهزية النظام\n\n")

        self.command_text.insert('end', "✨ نصائح للحصول على أفضل النتائج:\n\n")
        self.command_text.insert('end', "• فعل 'ملف واحد' لسهولة التوزيع\n")
        self.command_text.insert('end', "• استخدم 'إخفاء الكونسول' للتطبيقات ذات الواجهة الرسومية\n")
        self.command_text.insert('end', "• أضف أيقونة مخصصة لإعطاء مظهر احترافي\n")
        self.command_text.insert('end', "• استخدم 'وضع التصحيح' فقط عند وجود مشاكل\n")
    
    def create_settings_panel(self, parent):
        """إنشاء لوحة الإعدادات"""
        settings_frame, settings_inner = GlassTheme.create_glass_frame(parent)
        settings_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        # عنوان القسم
        settings_title = GlassTheme.create_glass_label(
            settings_inner, 
            "⚙️ إعدادات التحويل", 
            font_size=12, 
            font_weight='bold'
        )
        settings_title.pack(anchor='w', pady=(0, 15))
        
        # اختيار الملف المصدر
        self.create_source_selection(settings_inner)
        
        # اختيار مجلد الحفظ
        self.create_output_selection(settings_inner)
        
        # الخيارات المتقدمة
        self.create_advanced_options(settings_inner)
        
        # أزرار العمليات
        self.create_action_buttons(settings_inner)
    
    def create_source_selection(self, parent):
        """إنشاء قسم اختيار الملف المصدر"""
        source_frame = tk.Frame(parent, bg=ModernGlassTheme.COLORS['surface'])
        source_frame.pack(fill='x', pady=(0, 15))
        
        source_label = GlassTheme.create_glass_label(source_frame, "📁 الملف المصدر:")
        source_label.pack(anchor='w', pady=(0, 5))
        
        source_entry_frame = tk.Frame(source_frame, bg=ModernGlassTheme.COLORS['surface'])
        source_entry_frame.pack(fill='x')
        
        self.source_entry = GlassTheme.create_glass_entry(
            source_entry_frame, 
            width=40, 
            placeholder="اختر ملف Python أو مجلد..."
        )
        self.source_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))
        self.source_entry.configure(textvariable=self.source_path)
        
        # أزرار الاختيار
        btn_frame = tk.Frame(source_entry_frame, bg=ModernGlassTheme.COLORS['surface'])
        btn_frame.pack(side='right')
        
        file_btn = GlassTheme.create_glass_button(
            btn_frame, 
            "📄 ملف", 
            self.browse_file,
            width=8, 
            height=1
        )
        file_btn.pack(side='left', padx=(0, 5))
        
        folder_btn = GlassTheme.create_glass_button(
            btn_frame, 
            "📁 مجلد", 
            self.browse_folder,
            width=8, 
            height=1
        )
        folder_btn.pack(side='left')
    
    def create_output_selection(self, parent):
        """إنشاء قسم اختيار مجلد الحفظ"""
        output_frame = tk.Frame(parent, bg=ModernGlassTheme.COLORS['surface'])
        output_frame.pack(fill='x', pady=(0, 15))
        
        output_label = GlassTheme.create_glass_label(output_frame, "💾 مجلد الحفظ:")
        output_label.pack(anchor='w', pady=(0, 5))
        
        output_entry_frame = tk.Frame(output_frame, bg=ModernGlassTheme.COLORS['surface'])
        output_entry_frame.pack(fill='x')
        
        self.output_entry = GlassTheme.create_glass_entry(
            output_entry_frame, 
            width=40, 
            placeholder="اختر مجلد الحفظ..."
        )
        self.output_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))
        self.output_entry.configure(textvariable=self.output_path)
        
        browse_btn = GlassTheme.create_glass_button(
            output_entry_frame, 
            "📁 تصفح", 
            self.browse_output,
            width=10, 
            height=1
        )
        browse_btn.pack(side='right')
    
    def create_advanced_options(self, parent):
        """إنشاء الخيارات المتقدمة"""
        options_frame = tk.Frame(parent, bg=ModernGlassTheme.COLORS['surface'])
        options_frame.pack(fill='x', pady=(0, 15))
        
        options_label = GlassTheme.create_glass_label(
            options_frame, 
            "🔧 الخيارات المتقدمة:", 
            font_weight='bold'
        )
        options_label.pack(anchor='w', pady=(0, 10))
        
        # الخيارات في صفين
        row1 = tk.Frame(options_frame, bg=ModernGlassTheme.COLORS['surface'])
        row1.pack(fill='x', pady=(0, 5))
        
        row2 = tk.Frame(options_frame, bg=ModernGlassTheme.COLORS['surface'])
        row2.pack(fill='x', pady=(0, 5))
        
        # الصف الأول
        self.create_checkbox(row1, "📦 ملف واحد", self.onefile)
        self.create_checkbox(row1, "🖥️ إخفاء الكونسول", self.noconsole)
        self.create_checkbox(row1, "🐛 وضع التصحيح", self.debug)
        
        # الصف الثاني
        self.create_checkbox(row2, "⚡ تحسين الحجم", self.optimize)
        self.create_checkbox(row2, "🗜️ ضغط UPX", self.upx)
        
        # اختيار الأيقونة
        self.create_icon_selection(options_frame)
    
    def create_checkbox(self, parent, text, variable):
        """إنشاء checkbox مخصص"""
        cb = tk.Checkbutton(
            parent,
            text=text,
            variable=variable,
            font=('Segoe UI', 9),
            fg=ModernGlassTheme.COLORS['text_primary'],
            bg=ModernGlassTheme.COLORS['surface'],
            activebackground=ModernGlassTheme.COLORS['surface'],
            activeforeground=ModernGlassTheme.COLORS['text_primary'],
            selectcolor=ModernGlassTheme.COLORS['accent'],
            relief='flat',
            bd=0
        )
        cb.pack(side='left', padx=(0, 20))
        return cb

    def create_icon_selection(self, parent):
        """إنشاء قسم اختيار الأيقونة"""
        icon_frame = tk.Frame(parent, bg=ModernGlassTheme.COLORS['surface'])
        icon_frame.pack(fill='x', pady=(10, 0))

        icon_label = GlassTheme.create_glass_label(icon_frame, "🎨 أيقونة التطبيق:")
        icon_label.pack(anchor='w', pady=(0, 5))

        icon_entry_frame = tk.Frame(icon_frame, bg=ModernGlassTheme.COLORS['surface'])
        icon_entry_frame.pack(fill='x')

        self.icon_entry = GlassTheme.create_glass_entry(
            icon_entry_frame,
            width=30,
            placeholder="اختر ملف أيقونة (.ico)..."
        )
        self.icon_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))
        self.icon_entry.configure(textvariable=self.icon_path)

        icon_btn = GlassTheme.create_glass_button(
            icon_entry_frame,
            "🎨 تصفح",
            self.browse_icon,
            width=10,
            height=1
        )
        icon_btn.pack(side='right')

    def create_action_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        buttons_frame = tk.Frame(parent, bg=ModernGlassTheme.COLORS['surface'])
        buttons_frame.pack(fill='x', pady=(20, 0))

        # الصف الأول من الأزرار
        row1 = tk.Frame(buttons_frame, bg=ModernGlassTheme.COLORS['surface'])
        row1.pack(fill='x', pady=(0, 10))

        preview_btn = GlassTheme.create_glass_button(
            row1,
            "👁️ معاينة الأمر",
            self.preview_command,
            width=15,
            height=2
        )
        preview_btn.pack(side='left', padx=(0, 10))

        check_btn = GlassTheme.create_glass_button(
            row1,
            "🔍 فحص المتطلبات",
            self.check_requirements,
            width=15,
            height=2
        )
        check_btn.pack(side='left')

        # الصف الثاني - زر التحويل الرئيسي
        row2 = tk.Frame(buttons_frame, bg=ModernGlassTheme.COLORS['surface'])
        row2.pack(fill='x')

        self.convert_btn = GlassTheme.create_glass_button(
            row2,
            "🚀 بدء التحويل",
            self.start_conversion,
            width=32,
            height=3
        )
        self.convert_btn.pack(fill='x')

    def create_preview_panel(self, parent):
        """إنشاء لوحة المعاينة والسجل"""
        preview_frame, preview_inner = GlassTheme.create_glass_frame(parent)
        preview_frame.pack(side='right', fill='both', expand=True)

        # عنوان القسم
        preview_title = GlassTheme.create_glass_label(
            preview_inner,
            "📊 المعاينة والسجل",
            font_size=12,
            font_weight='bold'
        )
        preview_title.pack(anchor='w', pady=(0, 15))

        # تبويبات
        self.create_tabs(preview_inner)

        # شريط التقدم
        self.create_progress_section(preview_inner)

    def create_tabs(self, parent):
        """إنشاء التبويبات"""
        # إطار التبويبات
        tabs_frame = tk.Frame(parent, bg=ModernGlassTheme.COLORS['surface'])
        tabs_frame.pack(fill='both', expand=True, pady=(0, 15))

        # أزرار التبويبات
        tab_buttons_frame = tk.Frame(tabs_frame, bg=ModernGlassTheme.COLORS['surface'])
        tab_buttons_frame.pack(fill='x', pady=(0, 10))

        self.current_tab = tk.StringVar(value="command")

        # تبويب معاينة الأمر
        self.command_tab_btn = GlassTheme.create_glass_button(
            tab_buttons_frame,
            "💻 معاينة الأمر",
            lambda: self.switch_tab("command"),
            width=15,
            height=1
        )
        self.command_tab_btn.pack(side='left', padx=(0, 5))

        # تبويب السجل
        self.log_tab_btn = GlassTheme.create_glass_button(
            tab_buttons_frame,
            "📋 سجل العمليات",
            lambda: self.switch_tab("log"),
            width=15,
            height=1
        )
        self.log_tab_btn.pack(side='left', padx=(0, 5))

        # تبويب الملفات
        self.files_tab_btn = GlassTheme.create_glass_button(
            tab_buttons_frame,
            "📁 الملفات المحولة",
            lambda: self.switch_tab("files"),
            width=15,
            height=1
        )
        self.files_tab_btn.pack(side='left')

        # محتوى التبويبات
        self.tabs_content = tk.Frame(tabs_frame, bg=ModernGlassTheme.COLORS['surface'])
        self.tabs_content.pack(fill='both', expand=True)

        # إنشاء محتوى كل تبويب
        self.create_command_tab()
        self.create_log_tab()
        self.create_files_tab()

        # عرض التبويب الافتراضي
        self.switch_tab("command")

    def create_command_tab(self):
        """إنشاء تبويب معاينة الأمر"""
        self.command_frame = tk.Frame(self.tabs_content, bg=ModernGlassTheme.COLORS['surface'])

        command_label = GlassTheme.create_glass_label(
            self.command_frame,
            "💻 أمر PyInstaller الذي سيتم تنفيذه:"
        )
        command_label.pack(anchor='w', pady=(0, 10))

        self.command_text, command_scroll = GlassTheme.create_glass_text(
            self.command_frame,
            width=60,
            height=15
        )
        self.command_text.pack(side='left', fill='both', expand=True)
        command_scroll.pack(side='right', fill='y')

        # تحديث المعاينة
        self.update_command_preview()

    def create_log_tab(self):
        """إنشاء تبويب السجل"""
        self.log_frame = tk.Frame(self.tabs_content, bg=ModernGlassTheme.COLORS['surface'])

        log_label = GlassTheme.create_glass_label(
            self.log_frame,
            "📋 سجل العمليات المباشر:"
        )
        log_label.pack(anchor='w', pady=(0, 10))

        self.log_text, log_scroll = GlassTheme.create_glass_text(
            self.log_frame,
            width=60,
            height=15
        )
        self.log_text.pack(side='left', fill='both', expand=True)
        log_scroll.pack(side='right', fill='y')

        # إضافة رسالة ترحيب
        self.add_to_log("🚀 مرحباً بك في Python to EXE Converter Pro v2.0")
        self.add_to_log("📝 جاهز لبدء التحويل...")

    def create_files_tab(self):
        """إنشاء تبويب الملفات المحولة"""
        self.files_frame = tk.Frame(self.tabs_content, bg=ModernGlassTheme.COLORS['surface'])

        files_label = GlassTheme.create_glass_label(
            self.files_frame,
            "📁 قائمة الملفات المحولة:"
        )
        files_label.pack(anchor='w', pady=(0, 10))

        self.files_text, files_scroll = GlassTheme.create_glass_text(
            self.files_frame,
            width=60,
            height=15
        )
        self.files_text.pack(side='left', fill='both', expand=True)
        files_scroll.pack(side='right', fill='y')

        # إضافة رسالة افتراضية
        self.files_text.insert('1.0', "📂 لم يتم تحويل أي ملفات بعد...\n")
        self.files_text.insert('end', "🔄 ستظهر الملفات المحولة هنا بعد التحويل الناجح.")

    def create_progress_section(self, parent):
        """إنشاء قسم شريط التقدم"""
        progress_frame = tk.Frame(parent, bg=ModernGlassTheme.COLORS['surface'])
        progress_frame.pack(fill='x', pady=(10, 0))

        progress_label = GlassTheme.create_glass_label(progress_frame, "📊 تقدم العملية:")
        progress_label.pack(anchor='w', pady=(0, 5))

        self.progress_bar = GlassTheme.create_progress_bar(progress_frame, width=500, height=25)
        self.progress_bar.pack(fill='x', pady=(0, 10))

        self.progress_label = GlassTheme.create_glass_label(
            progress_frame,
            "⏳ في انتظار بدء العملية...",
            font_size=9
        )
        self.progress_label.pack(anchor='w')

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = tk.Frame(self.root, bg=ModernGlassTheme.COLORS['primary'], height=30)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)

        self.status_label = GlassTheme.create_glass_label(
            status_frame,
            "✅ جاهز للاستخدام",
            font_size=9
        )
        self.status_label.pack(side='left', padx=10, pady=5)

    # ==================== وظائف التفاعل ====================

    def switch_tab(self, tab_name):
        """تبديل التبويبات"""
        self.current_tab.set(tab_name)

        # إخفاء جميع التبويبات
        for widget in self.tabs_content.winfo_children():
            widget.pack_forget()

        # عرض التبويب المحدد
        if tab_name == "command":
            self.command_frame.pack(fill='both', expand=True)
            self.update_command_preview()
        elif tab_name == "log":
            self.log_frame.pack(fill='both', expand=True)
        elif tab_name == "files":
            self.files_frame.pack(fill='both', expand=True)

        # تحديث ألوان الأزرار
        self.update_tab_buttons()

    def update_tab_buttons(self):
        """تحديث ألوان أزرار التبويبات"""
        current = self.current_tab.get()

        # إعادة تعيين الألوان
        buttons = [
            (self.command_tab_btn, "command"),
            (self.log_tab_btn, "log"),
            (self.files_tab_btn, "files")
        ]

        for btn, tab_name in buttons:
            if tab_name == current:
                btn.configure(bg=ModernGlassTheme.COLORS['accent_light'])
            else:
                btn.configure(bg=ModernGlassTheme.COLORS['accent'])

    def browse_file(self):
        """تصفح ملف Python"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف Python",
            filetypes=[
                ("Python files", "*.py"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.source_path.set(file_path)
            self.add_to_log(f"📄 تم اختيار الملف: {os.path.basename(file_path)}")
            self.update_command_preview()
            self.update_status("✅ تم اختيار الملف المصدر")

    def browse_folder(self):
        """تصفح مجلد Python"""
        folder_path = filedialog.askdirectory(title="اختر مجلد Python")
        if folder_path:
            # البحث عن ملف main.py أو __main__.py
            main_files = []
            for file in os.listdir(folder_path):
                if file in ['main.py', '__main__.py', 'app.py']:
                    main_files.append(file)

            if main_files:
                main_file = os.path.join(folder_path, main_files[0])
                self.source_path.set(main_file)
                self.add_to_log(f"📁 تم اختيار المجلد: {os.path.basename(folder_path)}")
                self.add_to_log(f"📄 الملف الرئيسي: {main_files[0]}")
            else:
                self.source_path.set(folder_path)
                self.add_to_log(f"📁 تم اختيار المجلد: {os.path.basename(folder_path)}")
                self.add_to_log("⚠️ لم يتم العثور على ملف رئيسي، يرجى تحديد الملف يدوياً")

            self.update_command_preview()
            self.update_status("✅ تم اختيار المجلد المصدر")

    def browse_output(self):
        """تصفح مجلد الحفظ"""
        folder_path = filedialog.askdirectory(title="اختر مجلد الحفظ")
        if folder_path:
            self.output_path.set(folder_path)
            self.add_to_log(f"💾 مجلد الحفظ: {folder_path}")
            self.update_command_preview()
            self.update_status("✅ تم اختيار مجلد الحفظ")
            self.save_settings()

    def browse_icon(self):
        """تصفح ملف الأيقونة"""
        icon_path = filedialog.askopenfilename(
            title="اختر ملف الأيقونة",
            filetypes=[
                ("Icon files", "*.ico"),
                ("Image files", "*.png *.jpg *.jpeg *.bmp"),
                ("All files", "*.*")
            ]
        )
        if icon_path:
            self.icon_path.set(icon_path)
            self.add_to_log(f"🎨 تم اختيار الأيقونة: {os.path.basename(icon_path)}")
            self.update_command_preview()
            self.update_status("✅ تم اختيار الأيقونة")

    def update_command_preview(self):
        """تحديث معاينة الأمر"""
        if not hasattr(self, 'command_text'):
            return

        command = self.build_pyinstaller_command()

        self.command_text.delete('1.0', tk.END)
        self.command_text.insert('1.0', "💻 أمر PyInstaller:\n")
        self.command_text.insert('end', "=" * 50 + "\n\n")
        self.command_text.insert('end', command + "\n\n")
        self.command_text.insert('end', "=" * 50 + "\n")
        self.command_text.insert('end', "📝 ملاحظات:\n")
        self.command_text.insert('end', "• تأكد من صحة مسار الملف المصدر\n")
        self.command_text.insert('end', "• تأكد من وجود مساحة كافية في مجلد الحفظ\n")
        self.command_text.insert('end', "• قد تستغرق العملية عدة دقائق حسب حجم المشروع\n")

    def build_pyinstaller_command(self):
        """بناء أمر PyInstaller"""
        if not self.source_path.get():
            return "❌ لم يتم تحديد ملف مصدر"

        command_parts = ["pyinstaller"]

        # الخيارات الأساسية
        if self.onefile.get():
            command_parts.append("--onefile")

        if self.noconsole.get():
            command_parts.append("--noconsole")

        if self.debug.get():
            command_parts.append("--debug=all")

        if self.optimize.get():
            command_parts.append("--optimize=2")

        # مجلد الحفظ
        if self.output_path.get():
            command_parts.append(f'--distpath="{self.output_path.get()}"')

        # الأيقونة
        if self.icon_path.get():
            command_parts.append(f'--icon="{self.icon_path.get()}"')

        # UPX
        if self.upx.get():
            command_parts.append("--upx-dir=upx")

        # الملف المصدر
        command_parts.append(f'"{self.source_path.get()}"')

        return " ".join(command_parts)

    def add_to_log(self, message):
        """إضافة رسالة للسجل"""
        if not hasattr(self, 'log_text'):
            return

        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)

        # حفظ في ملف السجل
        try:
            with open('conversion_log.txt', 'a', encoding='utf-8') as f:
                f.write(log_message)
        except:
            pass

    def update_status(self, message):
        """تحديث شريط الحالة"""
        if hasattr(self, 'status_label'):
            self.status_label.configure(text=message)

    def update_progress(self, value, message=""):
        """تحديث شريط التقدم"""
        if hasattr(self, 'progress_bar'):
            self.progress_bar.update_progress(value)

        if hasattr(self, 'progress_label') and message:
            self.progress_label.configure(text=message)

    # ==================== وظائف العمليات ====================

    def preview_command(self):
        """معاينة أمر PyInstaller"""
        self.switch_tab("command")
        self.update_command_preview()
        self.add_to_log("👁️ تم عرض معاينة الأمر")
        self.update_status("👁️ معاينة الأمر")

    def check_requirements(self):
        """فحص المتطلبات والاعتمادات"""
        self.add_to_log("🔍 بدء فحص المتطلبات...")
        self.update_status("🔍 جاري فحص المتطلبات...")

        # فحص PyInstaller
        try:
            import PyInstaller
            version = PyInstaller.__version__
            self.add_to_log(f"✅ PyInstaller مثبت - الإصدار: {version}")
        except ImportError:
            self.add_to_log("❌ PyInstaller غير مثبت")
            self.add_to_log("💡 تشغيل: pip install PyInstaller")
            self.update_status("❌ PyInstaller غير مثبت")
            return

        # فحص Python
        python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        self.add_to_log(f"✅ Python - الإصدار: {python_version}")

        # فحص الملف المصدر
        source = self.source_path.get()
        if source:
            if os.path.exists(source):
                self.add_to_log(f"✅ الملف المصدر موجود: {os.path.basename(source)}")

                # فحص بناء الجملة
                try:
                    with open(source, 'r', encoding='utf-8') as f:
                        code = f.read()
                    compile(code, source, 'exec')
                    self.add_to_log("✅ بناء الجملة صحيح")
                except SyntaxError as e:
                    self.add_to_log(f"❌ خطأ في بناء الجملة: {e}")
                except Exception as e:
                    self.add_to_log(f"⚠️ تحذير: {e}")
            else:
                self.add_to_log("❌ الملف المصدر غير موجود")
        else:
            self.add_to_log("⚠️ لم يتم تحديد ملف مصدر")

        # فحص مجلد الحفظ
        output = self.output_path.get()
        if output:
            if os.path.exists(output):
                self.add_to_log(f"✅ مجلد الحفظ موجود: {output}")
            else:
                self.add_to_log("⚠️ مجلد الحفظ غير موجود - سيتم إنشاؤه")
        else:
            self.add_to_log("⚠️ لم يتم تحديد مجلد حفظ")

        # فحص مساحة القرص
        if output and os.path.exists(output):
            try:
                import shutil
                total, used, free = shutil.disk_usage(output)
                free_gb = free // (1024**3)
                self.add_to_log(f"💾 المساحة المتاحة: {free_gb} جيجابايت")
                if free_gb < 1:
                    self.add_to_log("⚠️ تحذير: مساحة القرص قليلة")
            except:
                pass

        self.add_to_log("✅ انتهى فحص المتطلبات")
        self.update_status("✅ تم فحص المتطلبات")
        self.switch_tab("log")

    def start_conversion(self):
        """بدء عملية التحويل"""
        if self.is_converting:
            self.add_to_log("⚠️ عملية تحويل جارية بالفعل")
            return

        # التحقق من المتطلبات الأساسية
        if not self.source_path.get():
            messagebox.showerror("خطأ", "يرجى اختيار ملف Python أولاً")
            return

        if not os.path.exists(self.source_path.get()):
            messagebox.showerror("خطأ", "الملف المصدر غير موجود")
            return

        # التحقق من PyInstaller
        try:
            import PyInstaller
        except ImportError:
            messagebox.showerror("خطأ", "PyInstaller غير مثبت\nيرجى تشغيل: pip install PyInstaller")
            return

        # بدء التحويل في thread منفصل
        self.is_converting = True
        self.convert_btn.configure(text="⏳ جاري التحويل...", state='disabled')
        self.update_status("🚀 بدء عملية التحويل...")
        self.update_progress(0, "⏳ تحضير العملية...")

        # تبديل إلى تبويب السجل
        self.switch_tab("log")

        # بدء thread التحويل
        conversion_thread = threading.Thread(target=self.run_conversion, daemon=True)
        conversion_thread.start()

    def run_conversion(self):
        """تشغيل عملية التحويل"""
        try:
            self.add_to_log("🚀 بدء عملية التحويل...")
            self.update_progress(10, "📋 بناء الأمر...")

            # بناء الأمر
            command = self.build_pyinstaller_command()
            self.add_to_log(f"💻 الأمر: {command}")

            # إنشاء مجلد الحفظ إذا لم يكن موجوداً
            output_dir = self.output_path.get()
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
                self.add_to_log(f"📁 تم إنشاء مجلد الحفظ: {output_dir}")

            self.update_progress(20, "⚙️ تشغيل PyInstaller...")

            # تشغيل PyInstaller
            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                encoding='utf-8'
            )

            # قراءة المخرجات
            progress_steps = [30, 40, 50, 60, 70, 80, 90]
            step_index = 0

            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break

                if output:
                    # تنظيف النص وإضافته للسجل
                    clean_output = output.strip()
                    if clean_output:
                        self.add_to_log(f"📝 {clean_output}")

                        # تحديث التقدم بناءً على المخرجات
                        if step_index < len(progress_steps):
                            if any(keyword in clean_output.lower() for keyword in
                                   ['analyzing', 'building', 'collecting', 'copying', 'writing']):
                                self.update_progress(progress_steps[step_index], f"⚙️ {clean_output[:50]}...")
                                step_index += 1

            # انتظار انتهاء العملية
            return_code = process.wait()

            if return_code == 0:
                self.update_progress(100, "✅ تم التحويل بنجاح!")
                self.add_to_log("✅ تم التحويل بنجاح!")
                self.update_status("✅ تم التحويل بنجاح")

                # البحث عن الملف المحول
                self.find_converted_files()

                # عرض رسالة نجاح
                self.root.after(0, lambda: messagebox.showinfo(
                    "نجح التحويل",
                    "تم تحويل الملف بنجاح!\nيمكنك العثور على الملف في مجلد الحفظ المحدد."
                ))

            else:
                self.update_progress(0, "❌ فشل التحويل")
                self.add_to_log(f"❌ فشل التحويل - رمز الخطأ: {return_code}")
                self.update_status("❌ فشل التحويل")

                self.root.after(0, lambda: messagebox.showerror(
                    "فشل التحويل",
                    "حدث خطأ أثناء التحويل.\nيرجى مراجعة السجل للتفاصيل."
                ))

        except Exception as e:
            self.update_progress(0, "❌ خطأ في التحويل")
            self.add_to_log(f"❌ خطأ: {str(e)}")
            self.update_status("❌ خطأ في التحويل")

            self.root.after(0, lambda: messagebox.showerror(
                "خطأ",
                f"حدث خطأ غير متوقع:\n{str(e)}"
            ))

        finally:
            # إعادة تفعيل الزر
            self.is_converting = False
            self.root.after(0, lambda: self.convert_btn.configure(
                text="🚀 بدء التحويل",
                state='normal'
            ))

    def find_converted_files(self):
        """البحث عن الملفات المحولة وإضافتها لقائمة الملفات"""
        try:
            output_dir = self.output_path.get() or "dist"
            source_name = os.path.splitext(os.path.basename(self.source_path.get()))[0]

            # البحث في مجلد dist
            possible_paths = [
                os.path.join(output_dir, f"{source_name}.exe"),
                os.path.join(output_dir, source_name, f"{source_name}.exe"),
                os.path.join("dist", f"{source_name}.exe"),
                os.path.join("dist", source_name, f"{source_name}.exe")
            ]

            found_files = []
            for path in possible_paths:
                if os.path.exists(path):
                    found_files.append(path)

            if found_files:
                self.files_text.delete('1.0', tk.END)
                self.files_text.insert('1.0', "📂 الملفات المحولة:\n")
                self.files_text.insert('end', "=" * 50 + "\n\n")

                for i, file_path in enumerate(found_files, 1):
                    file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                    self.files_text.insert('end', f"{i}. 📄 {os.path.basename(file_path)}\n")
                    self.files_text.insert('end', f"   📍 المسار: {file_path}\n")
                    self.files_text.insert('end', f"   📊 الحجم: {file_size:.2f} ميجابايت\n")
                    self.files_text.insert('end', f"   📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                self.add_to_log(f"📁 تم العثور على {len(found_files)} ملف محول")
            else:
                self.add_to_log("⚠️ لم يتم العثور على ملفات محولة في المسارات المتوقعة")

        except Exception as e:
            self.add_to_log(f"⚠️ خطأ في البحث عن الملفات: {e}")

    # ==================== تشغيل التطبيق ====================

    def run(self):
        """تشغيل التطبيق"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.save_settings()
            self.root.quit()
        except Exception as e:
            print(f"خطأ في تشغيل التطبيق: {e}")
        finally:
            self.save_settings()

def main():
    """الدالة الرئيسية"""
    try:
        app = PyInstallerGUI()
        app.run()
    except Exception as e:
        print(f"خطأ في بدء التطبيق: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()

    # ==================== وظائف التفاعل ====================

    def switch_tab(self, tab_name):
        """تبديل التبويبات"""
        self.current_tab.set(tab_name)

        # إخفاء جميع التبويبات
        for widget in self.tabs_content.winfo_children():
            widget.pack_forget()

        # عرض التبويب المحدد
        if tab_name == "command":
            self.command_frame.pack(fill='both', expand=True)
            self.update_command_preview()
        elif tab_name == "log":
            self.log_frame.pack(fill='both', expand=True)
        elif tab_name == "files":
            self.files_frame.pack(fill='both', expand=True)

        # تحديث ألوان الأزرار
        self.update_tab_buttons()

    def update_tab_buttons(self):
        """تحديث ألوان أزرار التبويبات"""
        current = self.current_tab.get()

        # إعادة تعيين الألوان
        buttons = [
            (self.command_tab_btn, "command"),
            (self.log_tab_btn, "log"),
            (self.files_tab_btn, "files")
        ]

        for btn, tab_name in buttons:
            if tab_name == current:
                btn.configure(bg=ModernGlassTheme.COLORS['accent_light'])
            else:
                btn.configure(bg=ModernGlassTheme.COLORS['accent'])

    def browse_file(self):
        """تصفح ملف Python"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف Python",
            filetypes=[
                ("Python files", "*.py"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.source_path.set(file_path)
            self.add_to_log(f"📄 تم اختيار الملف: {os.path.basename(file_path)}")
            self.update_command_preview()
            self.update_status("✅ تم اختيار الملف المصدر")

    def browse_folder(self):
        """تصفح مجلد Python"""
        folder_path = filedialog.askdirectory(title="اختر مجلد Python")
        if folder_path:
            # البحث عن ملف main.py أو __main__.py
            main_files = []
            for file in os.listdir(folder_path):
                if file in ['main.py', '__main__.py', 'app.py']:
                    main_files.append(file)

            if main_files:
                main_file = os.path.join(folder_path, main_files[0])
                self.source_path.set(main_file)
                self.add_to_log(f"📁 تم اختيار المجلد: {os.path.basename(folder_path)}")
                self.add_to_log(f"📄 الملف الرئيسي: {main_files[0]}")
            else:
                self.source_path.set(folder_path)
                self.add_to_log(f"📁 تم اختيار المجلد: {os.path.basename(folder_path)}")
                self.add_to_log("⚠️ لم يتم العثور على ملف رئيسي، يرجى تحديد الملف يدوياً")

            self.update_command_preview()
            self.update_status("✅ تم اختيار المجلد المصدر")

    def browse_output(self):
        """تصفح مجلد الحفظ"""
        folder_path = filedialog.askdirectory(title="اختر مجلد الحفظ")
        if folder_path:
            self.output_path.set(folder_path)
            self.add_to_log(f"💾 مجلد الحفظ: {folder_path}")
            self.update_command_preview()
            self.update_status("✅ تم اختيار مجلد الحفظ")
            self.save_settings()

    def browse_icon(self):
        """تصفح ملف الأيقونة"""
        icon_path = filedialog.askopenfilename(
            title="اختر ملف الأيقونة",
            filetypes=[
                ("Icon files", "*.ico"),
                ("Image files", "*.png *.jpg *.jpeg *.bmp"),
                ("All files", "*.*")
            ]
        )
        if icon_path:
            self.icon_path.set(icon_path)
            self.add_to_log(f"🎨 تم اختيار الأيقونة: {os.path.basename(icon_path)}")
            self.update_command_preview()
            self.update_status("✅ تم اختيار الأيقونة")

    def update_command_preview(self):
        """تحديث معاينة الأمر"""
        if not hasattr(self, 'command_text'):
            return

        command = self.build_pyinstaller_command()

        self.command_text.delete('1.0', tk.END)
        self.command_text.insert('1.0', "💻 أمر PyInstaller:\n")
        self.command_text.insert('end', "=" * 50 + "\n\n")
        self.command_text.insert('end', command + "\n\n")
        self.command_text.insert('end', "=" * 50 + "\n")
        self.command_text.insert('end', "📝 ملاحظات:\n")
        self.command_text.insert('end', "• تأكد من صحة مسار الملف المصدر\n")
        self.command_text.insert('end', "• تأكد من وجود مساحة كافية في مجلد الحفظ\n")
        self.command_text.insert('end', "• قد تستغرق العملية عدة دقائق حسب حجم المشروع\n")

    def build_pyinstaller_command(self):
        """بناء أمر PyInstaller"""
        if not self.source_path.get():
            return "❌ لم يتم تحديد ملف مصدر"

        command_parts = ["pyinstaller"]

        # الخيارات الأساسية
        if self.onefile.get():
            command_parts.append("--onefile")

        if self.noconsole.get():
            command_parts.append("--noconsole")

        if self.debug.get():
            command_parts.append("--debug=all")

        if self.optimize.get():
            command_parts.append("--optimize=2")

        # مجلد الحفظ
        if self.output_path.get():
            command_parts.append(f'--distpath="{self.output_path.get()}"')

        # الأيقونة
        if self.icon_path.get():
            command_parts.append(f'--icon="{self.icon_path.get()}"')

        # UPX
        if self.upx.get():
            command_parts.append("--upx-dir=upx")

        # الملف المصدر
        command_parts.append(f'"{self.source_path.get()}"')

        return " ".join(command_parts)

    def add_to_log(self, message):
        """إضافة رسالة للسجل"""
        if not hasattr(self, 'log_text'):
            return

        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)

        # حفظ في ملف السجل
        try:
            with open('conversion_log.txt', 'a', encoding='utf-8') as f:
                f.write(log_message)
        except:
            pass

    def update_status(self, message):
        """تحديث شريط الحالة"""
        if hasattr(self, 'status_label'):
            self.status_label.configure(text=message)

    def update_progress(self, value, message=""):
        """تحديث شريط التقدم"""
        if hasattr(self, 'progress_bar'):
            self.progress_bar.update_progress(value)

        if hasattr(self, 'progress_label') and message:
            self.progress_label.configure(text=message)
