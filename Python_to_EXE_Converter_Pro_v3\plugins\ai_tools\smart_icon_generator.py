#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python to EXE Converter Pro v3.0 - Smart Icon Generator Plugin
إضافة مولد الأيقونات الذكي

مولد أيقونات متقدم بالذكاء الاصطناعي يدعم:
- تحليل الكود لفهم نوع التطبيق
- توليد أيقونات مخصصة
- أنماط وألوان متعددة
- تحسين تلقائي للأيقونات
"""

import os
import ast
import re
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
import json
import hashlib

try:
    from PIL import Image, ImageDraw, ImageFont, ImageFilter
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

from .. import Plugin, PluginInfo, PluginType

class IconStyle(Enum):
    """أنماط الأيقونات"""
    FLAT = "flat"
    GRADIENT = "gradient"
    GLASS = "glass"
    NEON = "neon"
    MINIMAL = "minimal"
    RETRO = "retro"

class IconShape(Enum):
    """أشكال الأيقونات"""
    SQUARE = "square"
    CIRCLE = "circle"
    ROUNDED_SQUARE = "rounded_square"
    HEXAGON = "hexagon"
    DIAMOND = "diamond"

@dataclass
class IconConfig:
    """إعدادات الأيقونة"""
    size: int = 256
    style: IconStyle = IconStyle.FLAT
    shape: IconShape = IconShape.ROUNDED_SQUARE
    primary_color: str = "#3498db"
    secondary_color: str = "#2980b9"
    background_color: str = "#ffffff"
    text: str = ""
    font_size: int = 64
    add_shadow: bool = True
    add_glow: bool = False

@dataclass
class AppAnalysis:
    """تحليل التطبيق"""
    app_type: str = "general"
    domain: str = "utility"
    keywords: List[str] = None
    libraries: List[str] = None
    ui_framework: str = "none"
    suggested_colors: List[str] = None
    suggested_symbols: List[str] = None
    
    def __post_init__(self):
        if self.keywords is None:
            self.keywords = []
        if self.libraries is None:
            self.libraries = []
        if self.suggested_colors is None:
            self.suggested_colors = []
        if self.suggested_symbols is None:
            self.suggested_symbols = []

class SmartIconGenerator(Plugin):
    """مولد الأيقونات الذكي"""
    
    def __init__(self):
        info = PluginInfo(
            name="smart_icon_generator",
            version="1.0.0",
            description="مولد أيقونات ذكي بالذكاء الاصطناعي",
            author="Python to EXE Converter Pro Team",
            plugin_type=PluginType.AI_TOOL,
            dependencies=["Pillow"],
            min_app_version="3.0.0"
        )
        super().__init__(info)
        
        # قواعد التحليل
        self.app_type_patterns = {
            'web': ['flask', 'django', 'fastapi', 'tornado', 'bottle', 'requests', 'urllib'],
            'gui': ['tkinter', 'pyqt', 'pyside', 'kivy', 'wxpython', 'flet'],
            'game': ['pygame', 'arcade', 'panda3d', 'pyglet'],
            'data': ['pandas', 'numpy', 'matplotlib', 'seaborn', 'plotly', 'scipy'],
            'ai': ['tensorflow', 'pytorch', 'sklearn', 'opencv', 'transformers'],
            'network': ['socket', 'asyncio', 'twisted', 'paramiko', 'scapy'],
            'database': ['sqlite3', 'sqlalchemy', 'pymongo', 'redis', 'psycopg2'],
            'file': ['os', 'pathlib', 'shutil', 'zipfile', 'tarfile'],
            'system': ['psutil', 'subprocess', 'threading', 'multiprocessing']
        }
        
        # ألوان مقترحة حسب النوع
        self.type_colors = {
            'web': ['#e74c3c', '#3498db', '#2ecc71'],
            'gui': ['#9b59b6', '#34495e', '#16a085'],
            'game': ['#e67e22', '#f39c12', '#d35400'],
            'data': ['#27ae60', '#2980b9', '#8e44ad'],
            'ai': ['#e74c3c', '#9b59b6', '#34495e'],
            'network': ['#3498db', '#2980b9', '#1abc9c'],
            'database': ['#f39c12', '#e67e22', '#d35400'],
            'file': ['#95a5a6', '#7f8c8d', '#34495e'],
            'system': ['#34495e', '#2c3e50', '#95a5a6']
        }
        
        # رموز مقترحة
        self.type_symbols = {
            'web': ['🌐', '🔗', '📡', '🖥️'],
            'gui': ['🖼️', '🎨', '📱', '💻'],
            'game': ['🎮', '🕹️', '🎯', '🏆'],
            'data': ['📊', '📈', '📉', '🔢'],
            'ai': ['🤖', '🧠', '⚡', '🔮'],
            'network': ['🌐', '📡', '🔗', '📶'],
            'database': ['🗄️', '💾', '📚', '🔍'],
            'file': ['📁', '📄', '💾', '🗂️'],
            'system': ['⚙️', '🔧', '⚡', '🖥️']
        }
        
        # خطوط افتراضية
        self.default_fonts = [
            "arial.ttf", "calibri.ttf", "segoeui.ttf",
            "DejaVuSans.ttf", "LiberationSans-Regular.ttf"
        ]
        
        self.cache = {}
    
    def initialize(self) -> bool:
        """تهيئة الإضافة"""
        if not PIL_AVAILABLE:
            self.logger.error("مكتبة Pillow غير متوفرة")
            return False
        
        self.logger.info("تم تهيئة مولد الأيقونات الذكي")
        return True
    
    def activate(self) -> bool:
        """تفعيل الإضافة"""
        # تسجيل الخطافات
        self.register_hook("on_file_select", self._on_file_select)
        self.register_hook("generate_icon", self._generate_icon)
        
        self.logger.info("تم تفعيل مولد الأيقونات الذكي")
        return True
    
    def deactivate(self) -> bool:
        """إلغاء تفعيل الإضافة"""
        self.logger.info("تم إلغاء تفعيل مولد الأيقونات الذكي")
        return True
    
    def cleanup(self) -> bool:
        """تنظيف الإضافة"""
        self.cache.clear()
        return True
    
    def _on_file_select(self, file_path: str):
        """معالج اختيار الملف"""
        if file_path.endswith('.py'):
            analysis = self.analyze_python_file(file_path)
            self.logger.info(f"تحليل الملف: {analysis.app_type} - {analysis.domain}")
    
    def _generate_icon(self, file_path: str, output_path: str, config: Optional[IconConfig] = None):
        """معالج توليد الأيقونة"""
        if not config:
            # تحليل الملف لإنشاء إعدادات تلقائية
            analysis = self.analyze_python_file(file_path)
            config = self._create_config_from_analysis(analysis)
        
        return self.generate_icon(config, output_path)
    
    def analyze_python_file(self, file_path: str) -> AppAnalysis:
        """تحليل ملف Python"""
        cache_key = f"{file_path}:{os.path.getmtime(file_path)}"
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        analysis = AppAnalysis()
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # تحليل AST
            tree = ast.parse(content)
            
            # استخراج المكتبات
            analysis.libraries = self._extract_imports(tree)
            
            # تحديد نوع التطبيق
            analysis.app_type = self._determine_app_type(analysis.libraries)
            
            # استخراج الكلمات المفتاحية
            analysis.keywords = self._extract_keywords(content)
            
            # تحديد المجال
            analysis.domain = self._determine_domain(analysis.keywords, analysis.libraries)
            
            # تحديد إطار الواجهة
            analysis.ui_framework = self._detect_ui_framework(analysis.libraries)
            
            # اقتراح الألوان والرموز
            analysis.suggested_colors = self.type_colors.get(analysis.app_type, ['#3498db'])
            analysis.suggested_symbols = self.type_symbols.get(analysis.app_type, ['⚙️'])
            
            # حفظ في الذاكرة المؤقتة
            self.cache[cache_key] = analysis
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الملف {file_path}: {e}")
        
        return analysis
    
    def _extract_imports(self, tree: ast.AST) -> List[str]:
        """استخراج المكتبات المستوردة"""
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name.split('.')[0])
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    imports.append(node.module.split('.')[0])
        
        return list(set(imports))
    
    def _determine_app_type(self, libraries: List[str]) -> str:
        """تحديد نوع التطبيق"""
        scores = {}
        
        for app_type, patterns in self.app_type_patterns.items():
            score = sum(1 for lib in libraries if lib.lower() in patterns)
            if score > 0:
                scores[app_type] = score
        
        if scores:
            return max(scores, key=scores.get)
        
        return 'general'
    
    def _extract_keywords(self, content: str) -> List[str]:
        """استخراج الكلمات المفتاحية"""
        keywords = []
        
        # البحث عن كلمات مفتاحية في التعليقات والنصوص
        comment_pattern = r'#\s*(.+)'
        string_pattern = r'["\']([^"\']+)["\']'
        
        for match in re.finditer(comment_pattern, content):
            keywords.extend(match.group(1).split())
        
        for match in re.finditer(string_pattern, content):
            text = match.group(1)
            if len(text) > 2 and len(text) < 50:
                keywords.append(text)
        
        # تنظيف الكلمات المفتاحية
        cleaned_keywords = []
        for keyword in keywords:
            keyword = re.sub(r'[^\w\s]', '', keyword).strip().lower()
            if len(keyword) > 2:
                cleaned_keywords.append(keyword)
        
        return list(set(cleaned_keywords))
    
    def _determine_domain(self, keywords: List[str], libraries: List[str]) -> str:
        """تحديد مجال التطبيق"""
        domain_keywords = {
            'business': ['business', 'finance', 'accounting', 'invoice', 'sales'],
            'education': ['education', 'learning', 'student', 'teacher', 'course'],
            'entertainment': ['game', 'music', 'video', 'entertainment', 'media'],
            'productivity': ['todo', 'task', 'note', 'calendar', 'reminder'],
            'communication': ['chat', 'message', 'email', 'communication', 'social'],
            'development': ['code', 'development', 'programming', 'debug', 'compile'],
            'science': ['research', 'analysis', 'experiment', 'data', 'statistics']
        }
        
        all_terms = keywords + libraries
        scores = {}
        
        for domain, terms in domain_keywords.items():
            score = sum(1 for term in all_terms if any(keyword in term.lower() for keyword in terms))
            if score > 0:
                scores[domain] = score
        
        if scores:
            return max(scores, key=scores.get)
        
        return 'utility'
    
    def _detect_ui_framework(self, libraries: List[str]) -> str:
        """كشف إطار عمل الواجهة"""
        ui_frameworks = {
            'tkinter': ['tkinter'],
            'pyqt': ['pyqt5', 'pyqt6'],
            'pyside': ['pyside2', 'pyside6'],
            'kivy': ['kivy'],
            'wxpython': ['wx', 'wxpython'],
            'flet': ['flet']
        }
        
        for framework, libs in ui_frameworks.items():
            if any(lib in libraries for lib in libs):
                return framework
        
        return 'none'
    
    def _create_config_from_analysis(self, analysis: AppAnalysis) -> IconConfig:
        """إنشاء إعدادات من التحليل"""
        config = IconConfig()
        
        # اختيار اللون الأساسي
        if analysis.suggested_colors:
            config.primary_color = analysis.suggested_colors[0]
            if len(analysis.suggested_colors) > 1:
                config.secondary_color = analysis.suggested_colors[1]
        
        # اختيار الرمز
        if analysis.suggested_symbols:
            config.text = analysis.suggested_symbols[0]
        
        # تحديد النمط حسب نوع التطبيق
        style_mapping = {
            'web': IconStyle.GRADIENT,
            'gui': IconStyle.GLASS,
            'game': IconStyle.NEON,
            'data': IconStyle.FLAT,
            'ai': IconStyle.GRADIENT
        }
        config.style = style_mapping.get(analysis.app_type, IconStyle.FLAT)
        
        return config
    
    def generate_icon(self, config: IconConfig, output_path: str) -> bool:
        """توليد الأيقونة"""
        try:
            # إنشاء صورة جديدة
            image = Image.new('RGBA', (config.size, config.size), (0, 0, 0, 0))
            draw = ImageDraw.Draw(image)
            
            # رسم الخلفية
            self._draw_background(draw, config)
            
            # رسم النص/الرمز
            if config.text:
                self._draw_text(draw, config)
            
            # إضافة التأثيرات
            if config.add_shadow:
                image = self._add_shadow(image)
            
            if config.add_glow:
                image = self._add_glow(image, config.primary_color)
            
            # حفظ الأيقونة
            # تحويل إلى RGB إذا كان الإخراج لا يدعم الشفافية
            if output_path.lower().endswith('.ico'):
                # ICO يدعم الشفافية
                image.save(output_path, format='ICO', sizes=[(config.size, config.size)])
            else:
                # PNG يدعم الشفافية
                image.save(output_path, format='PNG')
            
            self.logger.info(f"تم إنشاء الأيقونة: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في توليد الأيقونة: {e}")
            return False
    
    def _draw_background(self, draw: ImageDraw.Draw, config: IconConfig):
        """رسم الخلفية"""
        size = config.size
        
        if config.shape == IconShape.CIRCLE:
            # دائرة
            if config.style == IconStyle.GRADIENT:
                self._draw_gradient_circle(draw, size, config)
            else:
                draw.ellipse([0, 0, size, size], fill=config.primary_color)
        
        elif config.shape == IconShape.ROUNDED_SQUARE:
            # مربع مدور
            radius = size // 8
            if config.style == IconStyle.GRADIENT:
                self._draw_gradient_rounded_rect(draw, size, radius, config)
            else:
                self._draw_rounded_rect(draw, 0, 0, size, size, radius, config.primary_color)
        
        else:
            # مربع عادي
            if config.style == IconStyle.GRADIENT:
                self._draw_gradient_rect(draw, size, config)
            else:
                draw.rectangle([0, 0, size, size], fill=config.primary_color)
    
    def _draw_rounded_rect(self, draw: ImageDraw.Draw, x1: int, y1: int, x2: int, y2: int, radius: int, fill: str):
        """رسم مستطيل مدور"""
        draw.rectangle([x1 + radius, y1, x2 - radius, y2], fill=fill)
        draw.rectangle([x1, y1 + radius, x2, y2 - radius], fill=fill)
        
        # الزوايا
        draw.pieslice([x1, y1, x1 + 2*radius, y1 + 2*radius], 180, 270, fill=fill)
        draw.pieslice([x2 - 2*radius, y1, x2, y1 + 2*radius], 270, 360, fill=fill)
        draw.pieslice([x1, y2 - 2*radius, x1 + 2*radius, y2], 90, 180, fill=fill)
        draw.pieslice([x2 - 2*radius, y2 - 2*radius, x2, y2], 0, 90, fill=fill)
    
    def _draw_gradient_circle(self, draw: ImageDraw.Draw, size: int, config: IconConfig):
        """رسم دائرة متدرجة"""
        # تدرج بسيط من الوسط للخارج
        center = size // 2
        for r in range(center, 0, -1):
            # حساب اللون المتدرج
            ratio = r / center
            color = self._interpolate_color(config.secondary_color, config.primary_color, ratio)
            draw.ellipse([center-r, center-r, center+r, center+r], fill=color)
    
    def _draw_gradient_rounded_rect(self, draw: ImageDraw.Draw, size: int, radius: int, config: IconConfig):
        """رسم مستطيل مدور متدرج"""
        # تدرج عمودي بسيط
        for y in range(size):
            ratio = y / size
            color = self._interpolate_color(config.primary_color, config.secondary_color, ratio)
            draw.line([(0, y), (size, y)], fill=color)
        
        # قناع للشكل المدور
        mask = Image.new('L', (size, size), 0)
        mask_draw = ImageDraw.Draw(mask)
        self._draw_rounded_rect(mask_draw, 0, 0, size, size, radius, 255)
        
        # تطبيق القناع
        background = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        background.paste(Image.new('RGBA', (size, size), config.primary_color), mask=mask)
    
    def _draw_gradient_rect(self, draw: ImageDraw.Draw, size: int, config: IconConfig):
        """رسم مستطيل متدرج"""
        for y in range(size):
            ratio = y / size
            color = self._interpolate_color(config.primary_color, config.secondary_color, ratio)
            draw.line([(0, y), (size, y)], fill=color)
    
    def _interpolate_color(self, color1: str, color2: str, ratio: float) -> str:
        """تداخل الألوان"""
        # تحويل الألوان من hex إلى RGB
        c1 = tuple(int(color1[i:i+2], 16) for i in (1, 3, 5))
        c2 = tuple(int(color2[i:i+2], 16) for i in (1, 3, 5))
        
        # حساب اللون المتداخل
        r = int(c1[0] + (c2[0] - c1[0]) * ratio)
        g = int(c1[1] + (c2[1] - c1[1]) * ratio)
        b = int(c1[2] + (c2[2] - c1[2]) * ratio)
        
        return f"#{r:02x}{g:02x}{b:02x}"
    
    def _draw_text(self, draw: ImageDraw.Draw, config: IconConfig):
        """رسم النص"""
        try:
            # محاولة تحميل خط
            font = None
            for font_name in self.default_fonts:
                try:
                    font = ImageFont.truetype(font_name, config.font_size)
                    break
                except:
                    continue
            
            if not font:
                font = ImageFont.load_default()
            
            # حساب موضع النص
            bbox = draw.textbbox((0, 0), config.text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (config.size - text_width) // 2
            y = (config.size - text_height) // 2
            
            # رسم النص
            text_color = self._get_contrast_color(config.primary_color)
            draw.text((x, y), config.text, fill=text_color, font=font)
            
        except Exception as e:
            self.logger.warning(f"خطأ في رسم النص: {e}")
    
    def _get_contrast_color(self, bg_color: str) -> str:
        """الحصول على لون متباين"""
        # تحويل إلى RGB
        rgb = tuple(int(bg_color[i:i+2], 16) for i in (1, 3, 5))
        
        # حساب السطوع
        brightness = (rgb[0] * 299 + rgb[1] * 587 + rgb[2] * 114) / 1000
        
        # إرجاع أبيض أو أسود حسب السطوع
        return "#ffffff" if brightness < 128 else "#000000"
    
    def _add_shadow(self, image: Image.Image) -> Image.Image:
        """إضافة ظل"""
        try:
            # إنشاء طبقة الظل
            shadow = Image.new('RGBA', image.size, (0, 0, 0, 0))
            shadow_draw = ImageDraw.Draw(shadow)
            
            # رسم الظل (نسخة سوداء مزاحة)
            shadow.paste(image, (2, 2))
            shadow = shadow.filter(ImageFilter.GaussianBlur(radius=2))
            
            # دمج الظل مع الصورة الأصلية
            result = Image.new('RGBA', image.size, (0, 0, 0, 0))
            result.paste(shadow, (0, 0))
            result.paste(image, (0, 0), image)
            
            return result
        except:
            return image
    
    def _add_glow(self, image: Image.Image, glow_color: str) -> Image.Image:
        """إضافة توهج"""
        try:
            # إنشاء طبقة التوهج
            glow = image.copy()
            glow = glow.filter(ImageFilter.GaussianBlur(radius=4))
            
            # تلوين التوهج
            glow_colored = Image.new('RGBA', image.size, glow_color + '80')  # شفافية 50%
            glow_colored.paste(glow, mask=glow)
            
            # دمج التوهج مع الصورة
            result = Image.new('RGBA', image.size, (0, 0, 0, 0))
            result.paste(glow_colored, (0, 0))
            result.paste(image, (0, 0), image)
            
            return result
        except:
            return image
    
    def get_suggested_configs(self, file_path: str) -> List[IconConfig]:
        """الحصول على إعدادات مقترحة"""
        analysis = self.analyze_python_file(file_path)
        configs = []
        
        # إنشاء عدة إعدادات مختلفة
        base_config = self._create_config_from_analysis(analysis)
        
        # إعداد افتراضي
        configs.append(base_config)
        
        # إعدادات بأنماط مختلفة
        for style in IconStyle:
            if style != base_config.style:
                config = IconConfig(
                    style=style,
                    primary_color=base_config.primary_color,
                    secondary_color=base_config.secondary_color,
                    text=base_config.text
                )
                configs.append(config)
        
        # إعدادات بألوان مختلفة
        for color in analysis.suggested_colors[1:3]:  # أول 2 ألوان إضافية
            config = IconConfig(
                style=base_config.style,
                primary_color=color,
                secondary_color=self._darken_color(color),
                text=base_config.text
            )
            configs.append(config)
        
        return configs
    
    def _darken_color(self, color: str, factor: float = 0.8) -> str:
        """تغميق اللون"""
        rgb = tuple(int(color[i:i+2], 16) for i in (1, 3, 5))
        darkened = tuple(int(c * factor) for c in rgb)
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"

# إنشاء مثيل الإضافة
def create_plugin():
    return SmartIconGenerator()
