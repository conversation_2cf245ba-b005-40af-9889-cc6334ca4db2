#!/bin/bash

# Python to EXE Converter Pro v2.0
# ملف تشغيل للأنظمة Unix/Linux/macOS

# تعيين الترميز
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}"
echo "========================================"
echo "  Python to EXE Converter Pro v2.0"
echo "  واجهة رسومية احترافية بتصميم زجاجي"
echo "========================================"
echo -e "${NC}"

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo -e "${RED}❌ خطأ: Python غير مثبت أو غير موجود في PATH${NC}"
        echo -e "${YELLOW}💡 يرجى تثبيت Python من: https://python.org${NC}"
        echo
        read -p "اضغط Enter للخروج..."
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo -e "${GREEN}✅ تم العثور على Python${NC}"
$PYTHON_CMD --version

# التحقق من وجود pip
if ! command -v pip3 &> /dev/null; then
    if ! command -v pip &> /dev/null; then
        echo -e "${RED}❌ خطأ: pip غير متوفر${NC}"
        echo -e "${YELLOW}💡 يرجى تثبيت pip${NC}"
        echo
        read -p "اضغط Enter للخروج..."
        exit 1
    else
        PIP_CMD="pip"
    fi
else
    PIP_CMD="pip3"
fi

echo -e "${GREEN}✅ تم العثور على pip${NC}"

# التحقق من وجود PyInstaller
if ! $PYTHON_CMD -c "import PyInstaller" &> /dev/null; then
    echo
    echo -e "${YELLOW}⚠️  PyInstaller غير مثبت${NC}"
    echo -e "${BLUE}🔄 جاري تثبيت PyInstaller...${NC}"
    echo
    
    if ! $PIP_CMD install PyInstaller; then
        echo -e "${RED}❌ فشل في تثبيت PyInstaller${NC}"
        echo -e "${YELLOW}💡 جرب تشغيل الأمر يدوياً: $PIP_CMD install PyInstaller${NC}"
        echo
        read -p "اضغط Enter للخروج..."
        exit 1
    fi
    
    echo -e "${GREEN}✅ تم تثبيت PyInstaller بنجاح${NC}"
fi

# التحقق من وجود Pillow (اختياري)
if ! $PYTHON_CMD -c "import PIL" &> /dev/null; then
    echo
    echo -e "${YELLOW}⚠️  Pillow غير مثبت (اختياري لدعم الصور)${NC}"
    echo -e "${BLUE}🔄 جاري تثبيت Pillow...${NC}"
    echo
    
    if ! $PIP_CMD install Pillow; then
        echo -e "${YELLOW}⚠️  تحذير: فشل في تثبيت Pillow (غير ضروري للتشغيل الأساسي)${NC}"
    else
        echo -e "${GREEN}✅ تم تثبيت Pillow بنجاح${NC}"
    fi
fi

# التحقق من وجود الملفات المطلوبة
if [ ! -f "pyinstaller_gui.py" ]; then
    echo -e "${RED}❌ خطأ: ملف pyinstaller_gui.py غير موجود${NC}"
    echo -e "${YELLOW}💡 تأكد من وجود جميع ملفات التطبيق في نفس المجلد${NC}"
    echo
    read -p "اضغط Enter للخروج..."
    exit 1
fi

if [ ! -f "glass_theme.py" ]; then
    echo -e "${RED}❌ خطأ: ملف glass_theme.py غير موجود${NC}"
    echo -e "${YELLOW}💡 تأكد من وجود جميع ملفات التطبيق في نفس المجلد${NC}"
    echo
    read -p "اضغط Enter للخروج..."
    exit 1
fi

echo
echo -e "${PURPLE}🚀 بدء تشغيل التطبيق...${NC}"
echo

# تشغيل التطبيق
$PYTHON_CMD pyinstaller_gui.py

# في حالة حدوث خطأ
if [ $? -ne 0 ]; then
    echo
    echo -e "${RED}❌ حدث خطأ أثناء تشغيل التطبيق${NC}"
    echo -e "${YELLOW}💡 تحقق من رسائل الخطأ أعلاه${NC}"
    echo
    read -p "اضغط Enter للخروج..."
fi

echo
echo -e "${CYAN}👋 شكراً لاستخدام Python to EXE Converter Pro${NC}"
read -p "اضغط Enter للخروج..."
