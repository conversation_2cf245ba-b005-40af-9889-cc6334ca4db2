#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python to EXE Converter Pro v3.0 - Main Application
التطبيق الرئيسي

نقطة الدخول الرئيسية للتطبيق مع:
- اختيار إطار العمل المناسب
- إدارة الإعدادات
- معالجة الأخطاء الشاملة
- نظام السجلات المتقدم
"""

import sys
import os
import argparse
import traceback
from pathlib import Path
from typing import Optional

# إضافة مسار المشروع لـ Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.logger import Logger, LogLevel
from core.config_manager import ConfigManager
from core.exceptions import handle_exception
from ui import UIFramework, Theme, create_ui_manager

class PyToExeConverterApp:
    """التطبيق الرئيسي"""
    
    def __init__(self):
        self.logger = Logger("MainApp")
        self.config_manager = ConfigManager()
        self.ui_manager = None
        
        # إعداد معالج الأخطاء العام
        sys.excepthook = self._handle_exception
    
    def _handle_exception(self, exc_type, exc_value, exc_traceback):
        """معالج الأخطاء العام"""
        if issubclass(exc_type, KeyboardInterrupt):
            # السماح بـ Ctrl+C
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # تسجيل الخطأ
        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        self.logger.critical(f"خطأ غير معالج: {error_msg}")
        
        # إظهار رسالة للمستخدم
        try:
            import tkinter.messagebox as messagebox
            messagebox.showerror(
                "خطأ في التطبيق",
                f"حدث خطأ غير متوقع:\n{exc_value}\n\nيرجى مراجعة ملف السجل للتفاصيل."
            )
        except:
            print(f"خطأ حرج: {exc_value}")
    
    def _parse_arguments(self):
        """تحليل معاملات سطر الأوامر"""
        parser = argparse.ArgumentParser(
            description="Python to EXE Converter Pro v3.0",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
أمثلة الاستخدام:
  %(prog)s                          # تشغيل الواجهة الرسومية
  %(prog)s --ui tkinter             # استخدام واجهة Tkinter
  %(prog)s --ui pyqt                # استخدام واجهة PyQt
  %(prog)s --theme dark             # استخدام الثيم الداكن
  %(prog)s --cli file.py            # تحويل من سطر الأوامر
  %(prog)s --version                # عرض الإصدار
            """
        )
        
        # خيارات الواجهة
        parser.add_argument(
            '--ui', '--framework',
            choices=['tkinter', 'pyqt', 'web'],
            default='tkinter',
            help='إطار عمل الواجهة (افتراضي: tkinter)'
        )
        
        parser.add_argument(
            '--theme',
            choices=['light', 'dark', 'auto', 'glass', 'neon'],
            default='dark',
            help='ثيم الواجهة (افتراضي: dark)'
        )
        
        # خيارات سطر الأوامر
        parser.add_argument(
            '--cli',
            metavar='FILE',
            help='تحويل ملف من سطر الأوامر'
        )
        
        parser.add_argument(
            '--output', '-o',
            metavar='DIR',
            default='dist',
            help='مجلد الإخراج (افتراضي: dist)'
        )
        
        parser.add_argument(
            '--onefile',
            action='store_true',
            help='إنشاء ملف واحد'
        )
        
        parser.add_argument(
            '--windowed',
            action='store_true',
            help='تطبيق نافذة بدون وحدة تحكم'
        )
        
        parser.add_argument(
            '--icon',
            metavar='FILE',
            help='ملف الأيقونة'
        )
        
        # خيارات أخرى
        parser.add_argument(
            '--debug',
            action='store_true',
            help='تفعيل وضع التصحيح'
        )
        
        parser.add_argument(
            '--log-level',
            choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
            default='INFO',
            help='مستوى السجل (افتراضي: INFO)'
        )
        
        parser.add_argument(
            '--config',
            metavar='FILE',
            help='ملف الإعدادات'
        )
        
        parser.add_argument(
            '--version',
            action='version',
            version='Python to EXE Converter Pro v3.0.0'
        )
        
        return parser.parse_args()
    
    def _setup_logging(self, log_level: str, debug: bool):
        """إعداد نظام السجلات"""
        if debug:
            level = LogLevel.DEBUG
        else:
            level = LogLevel[log_level]
        
        self.logger.set_level(level)
        self.logger.info(f"تم تعيين مستوى السجل إلى: {level.name}")
    
    def _check_dependencies(self):
        """فحص التبعيات المطلوبة"""
        missing_deps = []
        
        # فحص PyInstaller
        try:
            import PyInstaller
            self.logger.info(f"PyInstaller {PyInstaller.__version__} متوفر")
        except ImportError:
            missing_deps.append("PyInstaller")
        
        # فحص Pillow للأيقونات
        try:
            import PIL
            self.logger.info(f"Pillow متوفر")
        except ImportError:
            self.logger.warning("Pillow غير متوفر - قد تكون هناك مشاكل مع الأيقونات")
        
        if missing_deps:
            error_msg = f"التبعيات المفقودة: {', '.join(missing_deps)}"
            self.logger.error(error_msg)
            
            try:
                import tkinter.messagebox as messagebox
                messagebox.showerror(
                    "تبعيات مفقودة",
                    f"{error_msg}\n\nيرجى تثبيت التبعيات المطلوبة:\npip install {' '.join(missing_deps)}"
                )
            except:
                print(f"خطأ: {error_msg}")
            
            return False
        
        return True
    
    def _run_cli_mode(self, args):
        """تشغيل وضع سطر الأوامر"""
        from core.converter import PyToExeConverter, ConversionOptions, ConversionMode
        
        self.logger.info("تشغيل وضع سطر الأوامر")
        
        # إعداد خيارات التحويل
        options = ConversionOptions(
            source_file=args.cli,
            output_dir=args.output,
            icon_file=args.icon,
            mode=ConversionMode.ONEFILE if args.onefile else ConversionMode.ONEDIR,
            windowed=args.windowed,
            debug=args.debug
        )
        
        # إنشاء المحول
        converter = PyToExeConverter(self.logger)
        
        def progress_callback(stage: str, progress: int):
            print(f"[{progress:3d}%] {stage}")
        
        try:
            # بدء التحويل
            print(f"🚀 بدء تحويل: {args.cli}")
            result = converter.convert(options, progress_callback)
            
            print(f"✅ تم التحويل بنجاح!")
            print(f"📁 الملف الناتج: {result['output_file']}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في التحويل: {e}")
            return False
    
    def _run_gui_mode(self, args):
        """تشغيل وضع الواجهة الرسومية"""
        self.logger.info("تشغيل وضع الواجهة الرسومية")
        
        # تحديد إطار العمل
        framework = UIFramework(args.ui)
        theme = Theme(args.theme)
        
        try:
            # إنشاء مدير الواجهة
            self.ui_manager = create_ui_manager(framework, theme)
            
            # إنشاء النافذة الرئيسية
            self.ui_manager.create_main_window(
                title="🚀 Python to EXE Converter Pro v3.0",
                width=self.config_manager.get('ui.window_width', 1400),
                height=self.config_manager.get('ui.window_height', 900)
            )
            
            # تشغيل التطبيق
            self.ui_manager.run()
            
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في الواجهة الرسومية: {e}")
            return False
        finally:
            if self.ui_manager:
                self.ui_manager.shutdown()
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            # تحليل المعاملات
            args = self._parse_arguments()
            
            # إعداد السجلات
            self._setup_logging(args.log_level, args.debug)
            
            self.logger.info("🚀 بدء تشغيل Python to EXE Converter Pro v3.0")
            
            # فحص التبعيات
            if not self._check_dependencies():
                return False
            
            # تحديد وضع التشغيل
            if args.cli:
                return self._run_cli_mode(args)
            else:
                return self._run_gui_mode(args)
                
        except KeyboardInterrupt:
            self.logger.info("تم إيقاف التطبيق بواسطة المستخدم")
            return True
        except Exception as e:
            self.logger.critical(f"خطأ حرج في التطبيق: {e}")
            return False
        finally:
            self.logger.info("تم إنهاء التطبيق")

def main():
    """نقطة الدخول الرئيسية"""
    app = PyToExeConverterApp()
    success = app.run()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
