#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python to EXE Converter Pro v3.0 - Theme Manager
مدير الثيمات المتقدم

نظام ثيمات ديناميكي متقدم يدعم:
- ثيمات متعددة قابلة للتخصيص
- انتقالات سلسة بين الثيمات
- ثيمات تكيفية حسب الوقت
- تخصيص الألوان والخطوط
"""

import json
import os
import time
from pathlib import Path
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass, asdict
from enum import Enum
import tkinter as tk
from tkinter import ttk
import threading

class ThemeType(Enum):
    """أنواع الثيمات"""
    LIGHT = "light"
    DARK = "dark"
    AUTO = "auto"
    GLASS = "glass"
    NEON = "neon"
    CYBERPUNK = "cyberpunk"
    NATURE = "nature"
    OCEAN = "ocean"
    SUNSET = "sunset"

@dataclass
class ColorScheme:
    """مخطط الألوان"""
    # ألوان أساسية
    primary: str
    secondary: str
    accent: str
    
    # ألوان الخلفية
    bg_primary: str
    bg_secondary: str
    bg_tertiary: str
    
    # ألوان النص
    text_primary: str
    text_secondary: str
    text_muted: str
    
    # ألوان الحالة
    success: str
    warning: str
    error: str
    info: str
    
    # ألوان التفاعل
    hover: str
    active: str
    disabled: str
    
    # ألوان الحدود والظلال
    border: str
    shadow: str
    highlight: str

@dataclass
class ThemeConfig:
    """إعدادات الثيم"""
    name: str
    type: ThemeType
    colors: ColorScheme
    fonts: Dict[str, Any]
    animations: Dict[str, Any]
    effects: Dict[str, Any]
    custom_properties: Dict[str, Any]

class ThemeManager:
    """مدير الثيمات المتقدم"""
    
    def __init__(self, themes_dir: str = "ui/themes"):
        self.themes_dir = Path(themes_dir)
        self.themes_dir.mkdir(parents=True, exist_ok=True)
        
        self.themes: Dict[str, ThemeConfig] = {}
        self.current_theme: Optional[ThemeConfig] = None
        self.theme_callbacks: List[Callable] = []
        
        # إعدادات الانتقال
        self.transition_duration = 300  # مللي ثانية
        self.transition_steps = 20
        self.is_transitioning = False
        
        # إعدادات الثيم التكيفي
        self.auto_theme_enabled = False
        self.auto_theme_thread = None
        
        # تحميل الثيمات المدمجة
        self._load_builtin_themes()
        
        # تحميل الثيمات المخصصة
        self._load_custom_themes()
    
    def _load_builtin_themes(self):
        """تحميل الثيمات المدمجة"""
        # ثيم داكن
        dark_theme = ThemeConfig(
            name="Dark Pro",
            type=ThemeType.DARK,
            colors=ColorScheme(
                primary="#3b82f6",
                secondary="#6366f1", 
                accent="#8b5cf6",
                bg_primary="#0a0a0f",
                bg_secondary="#1a1a2e",
                bg_tertiary="#16213e",
                text_primary="#ffffff",
                text_secondary="#e2e8f0",
                text_muted="#94a3b8",
                success="#10b981",
                warning="#f59e0b",
                error="#ef4444",
                info="#06b6d4",
                hover="#374151",
                active="#4b5563",
                disabled="#6b7280",
                border="#374151",
                shadow="#000000",
                highlight="#3b82f6"
            ),
            fonts={
                "primary": ("Segoe UI", 10),
                "heading": ("Segoe UI", 12, "bold"),
                "code": ("Consolas", 10),
                "small": ("Segoe UI", 8)
            },
            animations={
                "enabled": True,
                "duration": 200,
                "easing": "ease-in-out"
            },
            effects={
                "shadows": True,
                "blur": False,
                "transparency": 0.95
            },
            custom_properties={}
        )
        
        # ثيم فاتح
        light_theme = ThemeConfig(
            name="Light Pro",
            type=ThemeType.LIGHT,
            colors=ColorScheme(
                primary="#2563eb",
                secondary="#3b82f6",
                accent="#6366f1",
                bg_primary="#ffffff",
                bg_secondary="#f8fafc",
                bg_tertiary="#e2e8f0",
                text_primary="#1f2937",
                text_secondary="#374151",
                text_muted="#6b7280",
                success="#059669",
                warning="#d97706",
                error="#dc2626",
                info="#0284c7",
                hover="#f3f4f6",
                active="#e5e7eb",
                disabled="#d1d5db",
                border="#d1d5db",
                shadow="#00000020",
                highlight="#2563eb"
            ),
            fonts={
                "primary": ("Segoe UI", 10),
                "heading": ("Segoe UI", 12, "bold"),
                "code": ("Consolas", 10),
                "small": ("Segoe UI", 8)
            },
            animations={
                "enabled": True,
                "duration": 200,
                "easing": "ease-in-out"
            },
            effects={
                "shadows": True,
                "blur": False,
                "transparency": 1.0
            },
            custom_properties={}
        )
        
        # ثيم نيون
        neon_theme = ThemeConfig(
            name="Neon Cyber",
            type=ThemeType.NEON,
            colors=ColorScheme(
                primary="#00ffff",
                secondary="#ff00ff",
                accent="#00ff00",
                bg_primary="#000000",
                bg_secondary="#0f0f0f",
                bg_tertiary="#1a1a1a",
                text_primary="#ffffff",
                text_secondary="#cccccc",
                text_muted="#888888",
                success="#00ff00",
                warning="#ffff00",
                error="#ff0000",
                info="#00ffff",
                hover="#333333",
                active="#444444",
                disabled="#666666",
                border="#333333",
                shadow="#00ffff40",
                highlight="#00ffff"
            ),
            fonts={
                "primary": ("Courier New", 10),
                "heading": ("Courier New", 12, "bold"),
                "code": ("Courier New", 10),
                "small": ("Courier New", 8)
            },
            animations={
                "enabled": True,
                "duration": 150,
                "easing": "ease-out"
            },
            effects={
                "shadows": True,
                "blur": True,
                "transparency": 0.9,
                "glow": True
            },
            custom_properties={
                "glow_intensity": 0.8,
                "scan_lines": True
            }
        )
        
        # ثيم سايبربانك
        cyberpunk_theme = ThemeConfig(
            name="Cyberpunk 2077",
            type=ThemeType.CYBERPUNK,
            colors=ColorScheme(
                primary="#fcee09",
                secondary="#ff2a6d",
                accent="#01012b",
                bg_primary="#0d1117",
                bg_secondary="#161b22",
                bg_tertiary="#21262d",
                text_primary="#f0f6fc",
                text_secondary="#8b949e",
                text_muted="#6e7681",
                success="#238636",
                warning="#d29922",
                error="#f85149",
                info="#58a6ff",
                hover="#30363d",
                active="#484f58",
                disabled="#6e7681",
                border="#30363d",
                shadow="#fcee0940",
                highlight="#fcee09"
            ),
            fonts={
                "primary": ("Segoe UI", 10),
                "heading": ("Segoe UI", 12, "bold"),
                "code": ("Fira Code", 10),
                "small": ("Segoe UI", 8)
            },
            animations={
                "enabled": True,
                "duration": 250,
                "easing": "ease-in-out"
            },
            effects={
                "shadows": True,
                "blur": False,
                "transparency": 0.95,
                "glitch": True
            },
            custom_properties={
                "glitch_intensity": 0.3,
                "neon_borders": True
            }
        )
        
        # ثيم الطبيعة
        nature_theme = ThemeConfig(
            name="Nature Green",
            type=ThemeType.NATURE,
            colors=ColorScheme(
                primary="#22c55e",
                secondary="#16a34a",
                accent="#15803d",
                bg_primary="#f0fdf4",
                bg_secondary="#dcfce7",
                bg_tertiary="#bbf7d0",
                text_primary="#14532d",
                text_secondary="#166534",
                text_muted="#16a34a",
                success="#22c55e",
                warning="#eab308",
                error="#ef4444",
                info="#06b6d4",
                hover="#d1fae5",
                active="#a7f3d0",
                disabled="#9ca3af",
                border="#86efac",
                shadow="#22c55e20",
                highlight="#22c55e"
            ),
            fonts={
                "primary": ("Segoe UI", 10),
                "heading": ("Segoe UI", 12, "bold"),
                "code": ("Consolas", 10),
                "small": ("Segoe UI", 8)
            },
            animations={
                "enabled": True,
                "duration": 300,
                "easing": "ease-in-out"
            },
            effects={
                "shadows": True,
                "blur": False,
                "transparency": 1.0,
                "organic": True
            },
            custom_properties={
                "leaf_patterns": True,
                "gradient_backgrounds": True
            }
        )
        
        # تسجيل الثيمات
        self.themes["dark"] = dark_theme
        self.themes["light"] = light_theme
        self.themes["neon"] = neon_theme
        self.themes["cyberpunk"] = cyberpunk_theme
        self.themes["nature"] = nature_theme
        
        # تعيين الثيم الافتراضي
        self.current_theme = dark_theme
    
    def _load_custom_themes(self):
        """تحميل الثيمات المخصصة"""
        themes_file = self.themes_dir / "custom_themes.json"
        
        if themes_file.exists():
            try:
                with open(themes_file, 'r', encoding='utf-8') as f:
                    custom_themes_data = json.load(f)
                
                for theme_name, theme_data in custom_themes_data.items():
                    try:
                        # تحويل البيانات لكائن ThemeConfig
                        colors = ColorScheme(**theme_data['colors'])
                        theme_config = ThemeConfig(
                            name=theme_data['name'],
                            type=ThemeType(theme_data['type']),
                            colors=colors,
                            fonts=theme_data.get('fonts', {}),
                            animations=theme_data.get('animations', {}),
                            effects=theme_data.get('effects', {}),
                            custom_properties=theme_data.get('custom_properties', {})
                        )
                        
                        self.themes[theme_name] = theme_config
                        
                    except Exception as e:
                        print(f"خطأ في تحميل الثيم المخصص {theme_name}: {e}")
                        
            except Exception as e:
                print(f"خطأ في تحميل الثيمات المخصصة: {e}")
    
    def save_custom_themes(self):
        """حفظ الثيمات المخصصة"""
        custom_themes = {}
        
        for theme_name, theme_config in self.themes.items():
            if theme_name not in ["dark", "light", "neon", "cyberpunk", "nature"]:
                custom_themes[theme_name] = asdict(theme_config)
        
        if custom_themes:
            themes_file = self.themes_dir / "custom_themes.json"
            try:
                with open(themes_file, 'w', encoding='utf-8') as f:
                    json.dump(custom_themes, f, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"خطأ في حفظ الثيمات المخصصة: {e}")
    
    def get_available_themes(self) -> List[str]:
        """الحصول على قائمة الثيمات المتاحة"""
        return list(self.themes.keys())
    
    def get_theme(self, theme_name: str) -> Optional[ThemeConfig]:
        """الحصول على ثيم معين"""
        return self.themes.get(theme_name)
    
    def set_theme(self, theme_name: str, animate: bool = True) -> bool:
        """تعيين ثيم جديد"""
        if theme_name not in self.themes:
            return False
        
        new_theme = self.themes[theme_name]
        
        if animate and not self.is_transitioning:
            self._animate_theme_transition(new_theme)
        else:
            self._apply_theme_immediately(new_theme)
        
        return True
    
    def _apply_theme_immediately(self, theme: ThemeConfig):
        """تطبيق الثيم فوراً"""
        old_theme = self.current_theme
        self.current_theme = theme
        
        # إشعار المستمعين
        self._notify_theme_change(old_theme, theme)
    
    def _animate_theme_transition(self, new_theme: ThemeConfig):
        """تحريك انتقال الثيم"""
        if self.is_transitioning:
            return
        
        self.is_transitioning = True
        old_theme = self.current_theme
        
        def transition_step(step: int):
            if step <= self.transition_steps:
                # حساب نسبة التقدم
                progress = step / self.transition_steps
                
                # إنشاء ثيم انتقالي
                transition_theme = self._interpolate_themes(old_theme, new_theme, progress)
                
                # تطبيق الثيم الانتقالي
                self.current_theme = transition_theme
                self._notify_theme_change(old_theme, transition_theme, transitioning=True)
                
                # جدولة الخطوة التالية
                if step < self.transition_steps:
                    threading.Timer(
                        self.transition_duration / 1000 / self.transition_steps,
                        lambda: transition_step(step + 1)
                    ).start()
                else:
                    # انتهاء الانتقال
                    self.current_theme = new_theme
                    self.is_transitioning = False
                    self._notify_theme_change(old_theme, new_theme)
        
        transition_step(0)
    
    def _interpolate_themes(self, theme1: ThemeConfig, theme2: ThemeConfig, progress: float) -> ThemeConfig:
        """تداخل بين ثيمين"""
        # تداخل الألوان
        interpolated_colors = self._interpolate_colors(theme1.colors, theme2.colors, progress)
        
        # إنشاء ثيم انتقالي
        return ThemeConfig(
            name=f"Transition_{progress:.2f}",
            type=theme2.type,
            colors=interpolated_colors,
            fonts=theme2.fonts,
            animations=theme2.animations,
            effects=theme2.effects,
            custom_properties=theme2.custom_properties
        )
    
    def _interpolate_colors(self, colors1: ColorScheme, colors2: ColorScheme, progress: float) -> ColorScheme:
        """تداخل الألوان"""
        def interpolate_color(color1: str, color2: str, t: float) -> str:
            # تحويل الألوان من hex إلى RGB
            def hex_to_rgb(hex_color: str) -> tuple:
                hex_color = hex_color.lstrip('#')
                return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
            
            def rgb_to_hex(rgb: tuple) -> str:
                return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"
            
            try:
                rgb1 = hex_to_rgb(color1)
                rgb2 = hex_to_rgb(color2)
                
                # تداخل كل مكون RGB
                interpolated_rgb = tuple(
                    int(rgb1[i] + (rgb2[i] - rgb1[i]) * t) for i in range(3)
                )
                
                return rgb_to_hex(interpolated_rgb)
            except:
                return color2  # في حالة الخطأ، استخدم اللون الثاني
        
        # تداخل جميع الألوان
        return ColorScheme(
            primary=interpolate_color(colors1.primary, colors2.primary, progress),
            secondary=interpolate_color(colors1.secondary, colors2.secondary, progress),
            accent=interpolate_color(colors1.accent, colors2.accent, progress),
            bg_primary=interpolate_color(colors1.bg_primary, colors2.bg_primary, progress),
            bg_secondary=interpolate_color(colors1.bg_secondary, colors2.bg_secondary, progress),
            bg_tertiary=interpolate_color(colors1.bg_tertiary, colors2.bg_tertiary, progress),
            text_primary=interpolate_color(colors1.text_primary, colors2.text_primary, progress),
            text_secondary=interpolate_color(colors1.text_secondary, colors2.text_secondary, progress),
            text_muted=interpolate_color(colors1.text_muted, colors2.text_muted, progress),
            success=interpolate_color(colors1.success, colors2.success, progress),
            warning=interpolate_color(colors1.warning, colors2.warning, progress),
            error=interpolate_color(colors1.error, colors2.error, progress),
            info=interpolate_color(colors1.info, colors2.info, progress),
            hover=interpolate_color(colors1.hover, colors2.hover, progress),
            active=interpolate_color(colors1.active, colors2.active, progress),
            disabled=interpolate_color(colors1.disabled, colors2.disabled, progress),
            border=interpolate_color(colors1.border, colors2.border, progress),
            shadow=interpolate_color(colors1.shadow, colors2.shadow, progress),
            highlight=interpolate_color(colors1.highlight, colors2.highlight, progress)
        )
    
    def add_theme_callback(self, callback: Callable):
        """إضافة callback لتغيير الثيم"""
        self.theme_callbacks.append(callback)
    
    def remove_theme_callback(self, callback: Callable):
        """إزالة callback"""
        if callback in self.theme_callbacks:
            self.theme_callbacks.remove(callback)
    
    def _notify_theme_change(self, old_theme: Optional[ThemeConfig], 
                           new_theme: ThemeConfig, transitioning: bool = False):
        """إشعار المستمعين بتغيير الثيم"""
        for callback in self.theme_callbacks:
            try:
                callback(old_theme, new_theme, transitioning)
            except Exception as e:
                print(f"خطأ في theme callback: {e}")
    
    def create_custom_theme(self, name: str, base_theme: str, modifications: Dict[str, Any]) -> bool:
        """إنشاء ثيم مخصص"""
        if base_theme not in self.themes:
            return False
        
        # نسخ الثيم الأساسي
        base = self.themes[base_theme]
        custom_theme = ThemeConfig(
            name=name,
            type=base.type,
            colors=ColorScheme(**asdict(base.colors)),
            fonts=base.fonts.copy(),
            animations=base.animations.copy(),
            effects=base.effects.copy(),
            custom_properties=base.custom_properties.copy()
        )
        
        # تطبيق التعديلات
        for key, value in modifications.items():
            if hasattr(custom_theme.colors, key):
                setattr(custom_theme.colors, key, value)
            elif key in custom_theme.fonts:
                custom_theme.fonts[key] = value
            elif key in custom_theme.animations:
                custom_theme.animations[key] = value
            elif key in custom_theme.effects:
                custom_theme.effects[key] = value
            else:
                custom_theme.custom_properties[key] = value
        
        # حفظ الثيم المخصص
        self.themes[name] = custom_theme
        self.save_custom_themes()
        
        return True
    
    def enable_auto_theme(self, light_theme: str = "light", dark_theme: str = "dark"):
        """تفعيل الثيم التكيفي"""
        self.auto_theme_enabled = True
        
        def auto_theme_worker():
            while self.auto_theme_enabled:
                current_hour = time.localtime().tm_hour
                
                # تحديد الثيم حسب الوقت
                if 6 <= current_hour < 18:  # النهار
                    target_theme = light_theme
                else:  # الليل
                    target_theme = dark_theme
                
                # تغيير الثيم إذا لزم الأمر
                if self.current_theme.name != self.themes[target_theme].name:
                    self.set_theme(target_theme)
                
                # انتظار ساعة قبل الفحص التالي
                time.sleep(3600)
        
        self.auto_theme_thread = threading.Thread(target=auto_theme_worker, daemon=True)
        self.auto_theme_thread.start()
    
    def disable_auto_theme(self):
        """إلغاء تفعيل الثيم التكيفي"""
        self.auto_theme_enabled = False
    
    def export_theme(self, theme_name: str, file_path: str) -> bool:
        """تصدير ثيم لملف"""
        if theme_name not in self.themes:
            return False
        
        try:
            theme_data = asdict(self.themes[theme_name])
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(theme_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"خطأ في تصدير الثيم: {e}")
            return False
    
    def import_theme(self, file_path: str) -> Optional[str]:
        """استيراد ثيم من ملف"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                theme_data = json.load(f)
            
            # تحويل البيانات لكائن ThemeConfig
            colors = ColorScheme(**theme_data['colors'])
            theme_config = ThemeConfig(
                name=theme_data['name'],
                type=ThemeType(theme_data['type']),
                colors=colors,
                fonts=theme_data.get('fonts', {}),
                animations=theme_data.get('animations', {}),
                effects=theme_data.get('effects', {}),
                custom_properties=theme_data.get('custom_properties', {})
            )
            
            # إضافة الثيم
            theme_name = theme_config.name.lower().replace(' ', '_')
            self.themes[theme_name] = theme_config
            self.save_custom_themes()
            
            return theme_name
            
        except Exception as e:
            print(f"خطأ في استيراد الثيم: {e}")
            return None

# مثيل عام لمدير الثيمات
theme_manager = ThemeManager()

# تصدير المكونات الرئيسية
__all__ = [
    'ThemeType',
    'ColorScheme', 
    'ThemeConfig',
    'ThemeManager',
    'theme_manager'
]
