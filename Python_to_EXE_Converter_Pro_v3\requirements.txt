# Python to EXE Converter Pro v3.0 Requirements
# متطلبات Python to EXE Converter Pro v3.0

# Core Dependencies - التبعيات الأساسية
PyInstaller>=6.0.0          # محرك التحويل الرئيسي
Pillow>=10.0.0              # معالجة الصور والأيقونات

# UI Frameworks - أطر عمل الواجهة
# Tkinter مدمج مع Python
PyQt6>=6.5.0                # واجهة PyQt (اختياري)
PySide6>=6.5.0              # واجهة PySide (بديل PyQt)

# Web UI Dependencies - تبعيات الواجهة الويب (اختياري)
Flask>=2.3.0                # إطار عمل الويب
Flask-SocketIO>=5.3.0       # التواصل المباشر
eventlet>=0.33.0            # خادم الويب

# Development Tools - أدوات التطوير
pytest>=7.4.0              # إطار الاختبارات
pytest-cov>=4.1.0          # تغطية الاختبارات
black>=23.7.0               # تنسيق الكود
flake8>=6.0.0               # فحص جودة الكود
mypy>=1.5.0                 # فحص الأنواع

# Documentation - التوثيق
Sphinx>=7.1.0               # مولد التوثيق
sphinx-rtd-theme>=1.3.0     # ثيم التوثيق

# Optional AI Features - ميزات الذكاء الاصطناعي (اختياري)
# openai>=0.27.0            # OpenAI API
# requests>=2.31.0          # طلبات HTTP

# Performance & Optimization - الأداء والتحسين
psutil>=5.9.0               # معلومات النظام
memory-profiler>=0.61.0     # مراقبة الذاكرة

# Security - الأمان
cryptography>=41.0.0        # التشفير
keyring>=24.2.0             # إدارة كلمات المرور

# Utilities - الأدوات المساعدة
colorama>=0.4.6             # ألوان الطرفية
tqdm>=4.66.0                # شريط التقدم
click>=8.1.0                # واجهة سطر الأوامر
watchdog>=3.0.0             # مراقبة الملفات

# Platform Specific - خاص بالمنصة
# Windows
pywin32>=306; sys_platform == "win32"
wmi>=1.5.1; sys_platform == "win32"

# macOS
pyobjc>=9.2; sys_platform == "darwin"

# Linux
python-dbus>=1.3.2; sys_platform == "linux"
