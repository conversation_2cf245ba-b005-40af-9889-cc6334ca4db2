#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python to EXE Converter Pro v3.0 - Advanced Code Analyzer Plugin
إضافة محلل الكود المتقدم

محلل كود ذكي متقدم يدعم:
- تحليل عميق للكود
- كشف الأنماط والمشاكل
- اقتراحات التحسين الذكية
- تحليل الأداء والأمان
"""

import ast
import os
import re
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import time
import threading

from .. import Plugin, PluginInfo, PluginType

class IssueType(Enum):
    """أنواع المشاكل"""
    PERFORMANCE = "performance"
    SECURITY = "security"
    MAINTAINABILITY = "maintainability"
    COMPATIBILITY = "compatibility"
    STYLE = "style"

class Severity(Enum):
    """مستويات الخطورة"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class CodeIssue:
    """مشكلة في الكود"""
    type: IssueType
    severity: Severity
    line_number: int
    column: int
    message: str
    suggestion: str
    code_snippet: str
    fix_example: Optional[str] = None

@dataclass
class AnalysisReport:
    """تقرير التحليل"""
    file_path: str
    total_lines: int
    total_issues: int
    issues_by_type: Dict[str, int]
    issues_by_severity: Dict[str, int]
    issues: List[CodeIssue]
    overall_score: float
    recommendations: List[str]
    analysis_time: float

class AdvancedCodeAnalyzer(Plugin):
    """محلل الكود المتقدم"""
    
    def __init__(self):
        info = PluginInfo(
            name="advanced_code_analyzer",
            version="1.0.0",
            description="محلل كود متقدم مع ذكاء اصطناعي",
            author="Python to EXE Converter Pro Team",
            plugin_type=PluginType.CODE_ANALYZER,
            dependencies=[],
            min_app_version="3.0.0"
        )
        super().__init__(info)
        
        # قواعد التحليل المتقدمة
        self.analysis_patterns = {
            'performance': {
                'inefficient_loops': {
                    'pattern': r'for\s+\w+\s+in\s+range\s*\(\s*len\s*\(',
                    'message': 'حلقة غير فعالة - استخدم enumerate() بدلاً من range(len())',
                    'severity': Severity.MEDIUM,
                    'fix': 'for i, item in enumerate(items):'
                },
                'string_concatenation': {
                    'pattern': r'\w+\s*\+=\s*["\'].*["\']',
                    'message': 'ربط النصوص غير فعال - استخدم join() أو f-strings',
                    'severity': Severity.MEDIUM,
                    'fix': 'result = "".join(parts) أو f"{var1}{var2}"'
                },
                'repeated_calculations': {
                    'pattern': r'(\w+\.\w+\([^)]*\)).*\1',
                    'message': 'حسابات متكررة - احفظ النتيجة في متغير',
                    'severity': Severity.LOW,
                    'fix': 'result = expensive_function(); use result'
                }
            },
            'security': {
                'eval_usage': {
                    'pattern': r'eval\s*\(',
                    'message': 'استخدام eval() خطير جداً - تجنبه تماماً',
                    'severity': Severity.CRITICAL,
                    'fix': 'استخدم ast.literal_eval() للبيانات الآمنة'
                },
                'exec_usage': {
                    'pattern': r'exec\s*\(',
                    'message': 'استخدام exec() خطير - تجنبه أو تأكد من التحقق',
                    'severity': Severity.HIGH,
                    'fix': 'تجنب exec() أو استخدم compile() مع فحص صارم'
                },
                'shell_injection': {
                    'pattern': r'subprocess\.\w+\([^)]*shell\s*=\s*True',
                    'message': 'خطر حقن الأوامر - تجنب shell=True',
                    'severity': Severity.HIGH,
                    'fix': 'استخدم قائمة الأوامر بدلاً من shell=True'
                },
                'hardcoded_passwords': {
                    'pattern': r'password\s*=\s*["\'][^"\']+["\']',
                    'message': 'كلمة مرور مكتوبة في الكود - استخدم متغيرات البيئة',
                    'severity': Severity.HIGH,
                    'fix': 'password = os.getenv("PASSWORD")'
                }
            },
            'maintainability': {
                'long_functions': {
                    'pattern': r'def\s+\w+.*?(?=\ndef|\nclass|\Z)',
                    'message': 'دالة طويلة جداً - فكر في تقسيمها',
                    'severity': Severity.MEDIUM,
                    'fix': 'قسم الدالة لدوال أصغر ومتخصصة'
                },
                'magic_numbers': {
                    'pattern': r'\b(?<![\w.])\d{2,}\b(?![\w.])',
                    'message': 'أرقام سحرية - استخدم ثوابت مسماة',
                    'severity': Severity.LOW,
                    'fix': 'MAX_RETRIES = 5; for i in range(MAX_RETRIES):'
                },
                'deep_nesting': {
                    'pattern': r'(\s{4,}){4,}',
                    'message': 'تداخل عميق - فكر في إعادة هيكلة الكود',
                    'severity': Severity.MEDIUM,
                    'fix': 'استخدم early returns أو استخرج دوال منفصلة'
                }
            },
            'compatibility': {
                'python2_print': {
                    'pattern': r'print\s+[^(]',
                    'message': 'صيغة print من Python 2 - استخدم print()',
                    'severity': Severity.MEDIUM,
                    'fix': 'print("message") بدلاً من print "message"'
                },
                'deprecated_imports': {
                    'pattern': r'import\s+(imp|optparse)\b',
                    'message': 'مكتبة مهجورة - استخدم البديل الحديث',
                    'severity': Severity.MEDIUM,
                    'fix': 'استخدم importlib بدلاً من imp، argparse بدلاً من optparse'
                }
            }
        }
        
        # قاعدة بيانات التوصيات
        self.recommendations_db = {
            'high_complexity': [
                "🔧 قسم الدوال الطويلة لدوال أصغر ومتخصصة",
                "📚 استخدم التوثيق (docstrings) لشرح الدوال المعقدة",
                "🧪 أضف اختبارات وحدة للدوال المهمة"
            ],
            'security_issues': [
                "🔒 راجع جميع نقاط الأمان المذكورة",
                "🛡️ استخدم مكتبات التشفير المعتمدة",
                "🔍 فحص المدخلات قبل معالجتها"
            ],
            'performance_issues': [
                "⚡ استخدم مكتبات محسنة مثل NumPy للحسابات الثقيلة",
                "💾 فكر في استخدام التخزين المؤقت للعمليات المكلفة",
                "🔄 راجع الخوارزميات المستخدمة للتحسين"
            ]
        }
        
        # إحصائيات التحليل
        self.analysis_stats = {
            'files_analyzed': 0,
            'total_issues_found': 0,
            'analysis_time_total': 0.0
        }
    
    def initialize(self) -> bool:
        """تهيئة الإضافة"""
        self.logger.info("تم تهيئة محلل الكود المتقدم")
        return True
    
    def activate(self) -> bool:
        """تفعيل الإضافة"""
        # تسجيل الخطافات
        self.register_hook("analyze_code", self._analyze_code_hook)
        self.register_hook("get_analysis_report", self._get_analysis_report_hook)
        
        self.logger.info("تم تفعيل محلل الكود المتقدم")
        return True
    
    def deactivate(self) -> bool:
        """إلغاء تفعيل الإضافة"""
        self.logger.info("تم إلغاء تفعيل محلل الكود المتقدم")
        return True
    
    def cleanup(self) -> bool:
        """تنظيف الإضافة"""
        return True
    
    def _analyze_code_hook(self, file_path: str, options: Dict[str, Any] = None):
        """خطاف تحليل الكود"""
        return self.analyze_file(file_path, options or {})
    
    def _get_analysis_report_hook(self, file_path: str):
        """خطاف الحصول على تقرير التحليل"""
        return self.get_detailed_report(file_path)
    
    def analyze_file(self, file_path: str, options: Dict[str, Any] = None) -> AnalysisReport:
        """تحليل ملف Python"""
        start_time = time.time()
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"الملف غير موجود: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # تحليل AST
            try:
                tree = ast.parse(content)
            except SyntaxError as e:
                return self._create_syntax_error_report(file_path, str(e))
            
            # تحليل شامل
            issues = []
            lines = content.split('\n')
            
            # تحليل الأنماط
            issues.extend(self._analyze_patterns(content, lines))
            
            # تحليل AST
            issues.extend(self._analyze_ast(tree, lines))
            
            # تحليل البنية
            issues.extend(self._analyze_structure(tree, content))
            
            # إنشاء التقرير
            analysis_time = time.time() - start_time
            report = self._create_report(file_path, content, issues, analysis_time)
            
            # تحديث الإحصائيات
            self._update_stats(report, analysis_time)
            
            return report
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الملف {file_path}: {e}")
            raise
    
    def _analyze_patterns(self, content: str, lines: List[str]) -> List[CodeIssue]:
        """تحليل الأنماط في الكود"""
        issues = []
        
        for category, patterns in self.analysis_patterns.items():
            for pattern_name, pattern_info in patterns.items():
                matches = re.finditer(pattern_info['pattern'], content, re.MULTILINE)
                
                for match in matches:
                    line_num = content[:match.start()].count('\n') + 1
                    col_num = match.start() - content.rfind('\n', 0, match.start())
                    
                    # استخراج مقطع الكود
                    if line_num <= len(lines):
                        code_snippet = lines[line_num - 1].strip()
                    else:
                        code_snippet = match.group(0)
                    
                    issue = CodeIssue(
                        type=IssueType(category),
                        severity=pattern_info['severity'],
                        line_number=line_num,
                        column=col_num,
                        message=pattern_info['message'],
                        suggestion=pattern_info['fix'],
                        code_snippet=code_snippet,
                        fix_example=pattern_info.get('fix')
                    )
                    
                    issues.append(issue)
        
        return issues
    
    def _analyze_ast(self, tree: ast.AST, lines: List[str]) -> List[CodeIssue]:
        """تحليل AST للكود"""
        issues = []
        
        for node in ast.walk(tree):
            # فحص الدوال الطويلة
            if isinstance(node, ast.FunctionDef):
                if hasattr(node, 'end_lineno') and hasattr(node, 'lineno'):
                    func_length = node.end_lineno - node.lineno
                    if func_length > 50:
                        issue = CodeIssue(
                            type=IssueType.MAINTAINABILITY,
                            severity=Severity.MEDIUM,
                            line_number=node.lineno,
                            column=node.col_offset,
                            message=f"الدالة '{node.name}' طويلة جداً ({func_length} سطر)",
                            suggestion="قسم الدالة لدوال أصغر ومتخصصة",
                            code_snippet=f"def {node.name}(...):",
                            fix_example="def main_function():\n    helper1()\n    helper2()"
                        )
                        issues.append(issue)
            
            # فحص الفئات الكبيرة
            elif isinstance(node, ast.ClassDef):
                methods_count = sum(1 for n in node.body if isinstance(n, ast.FunctionDef))
                if methods_count > 20:
                    issue = CodeIssue(
                        type=IssueType.MAINTAINABILITY,
                        severity=Severity.MEDIUM,
                        line_number=node.lineno,
                        column=node.col_offset,
                        message=f"الفئة '{node.name}' تحتوي على عدد كبير من الطرق ({methods_count})",
                        suggestion="فكر في تقسيم الفئة أو استخدام التركيب",
                        code_snippet=f"class {node.name}:",
                        fix_example="class MainClass:\n    def __init__(self):\n        self.helper = HelperClass()"
                    )
                    issues.append(issue)
            
            # فحص استخدام المتغيرات العامة
            elif isinstance(node, ast.Global):
                for name in node.names:
                    issue = CodeIssue(
                        type=IssueType.MAINTAINABILITY,
                        severity=Severity.LOW,
                        line_number=node.lineno,
                        column=node.col_offset,
                        message=f"استخدام متغير عام '{name}' - قلل الاعتماد على المتغيرات العامة",
                        suggestion="استخدم معاملات الدوال أو فئات لتمرير البيانات",
                        code_snippet=f"global {name}",
                        fix_example="def function(param): return modified_param"
                    )
                    issues.append(issue)
        
        return issues
    
    def _analyze_structure(self, tree: ast.AST, content: str) -> List[CodeIssue]:
        """تحليل بنية الكود"""
        issues = []
        
        # حساب التعقيد الدوري
        complexity_issues = self._calculate_cyclomatic_complexity(tree)
        issues.extend(complexity_issues)
        
        # فحص التداخل العميق
        nesting_issues = self._check_deep_nesting(tree)
        issues.extend(nesting_issues)
        
        return issues
    
    def _calculate_cyclomatic_complexity(self, tree: ast.AST) -> List[CodeIssue]:
        """حساب التعقيد الدوري"""
        issues = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                complexity = self._get_function_complexity(node)
                if complexity > 10:
                    severity = Severity.HIGH if complexity > 15 else Severity.MEDIUM
                    issue = CodeIssue(
                        type=IssueType.MAINTAINABILITY,
                        severity=severity,
                        line_number=node.lineno,
                        column=node.col_offset,
                        message=f"الدالة '{node.name}' معقدة جداً (تعقيد دوري: {complexity})",
                        suggestion="قسم الدالة لدوال أصغر أو بسط المنطق",
                        code_snippet=f"def {node.name}(...):",
                        fix_example="def complex_function():\n    if condition:\n        return simple_case()\n    return complex_case()"
                    )
                    issues.append(issue)
        
        return issues
    
    def _get_function_complexity(self, func_node: ast.FunctionDef) -> int:
        """حساب التعقيد الدوري لدالة"""
        complexity = 1  # نقطة بداية
        
        for node in ast.walk(func_node):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(node, ast.ExceptHandler):
                complexity += 1
            elif isinstance(node, ast.BoolOp):
                complexity += len(node.values) - 1
        
        return complexity
    
    def _check_deep_nesting(self, tree: ast.AST) -> List[CodeIssue]:
        """فحص التداخل العميق"""
        issues = []
        
        def get_nesting_level(node, level=0):
            max_level = level
            for child in ast.iter_child_nodes(node):
                if isinstance(child, (ast.If, ast.While, ast.For, ast.With, ast.Try)):
                    child_level = get_nesting_level(child, level + 1)
                    max_level = max(max_level, child_level)
                else:
                    child_level = get_nesting_level(child, level)
                    max_level = max(max_level, child_level)
            return max_level
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                nesting_level = get_nesting_level(node)
                if nesting_level > 4:
                    issue = CodeIssue(
                        type=IssueType.MAINTAINABILITY,
                        severity=Severity.MEDIUM,
                        line_number=node.lineno,
                        column=node.col_offset,
                        message=f"تداخل عميق في الدالة '{node.name}' (مستوى {nesting_level})",
                        suggestion="استخدم early returns أو استخرج دوال منفصلة",
                        code_snippet=f"def {node.name}(...):",
                        fix_example="def function():\n    if not condition:\n        return\n    # main logic here"
                    )
                    issues.append(issue)
        
        return issues
    
    def _create_report(self, file_path: str, content: str, issues: List[CodeIssue], analysis_time: float) -> AnalysisReport:
        """إنشاء تقرير التحليل"""
        lines = content.split('\n')
        
        # تصنيف المشاكل
        issues_by_type = {}
        issues_by_severity = {}
        
        for issue in issues:
            # حسب النوع
            type_key = issue.type.value
            issues_by_type[type_key] = issues_by_type.get(type_key, 0) + 1
            
            # حسب الخطورة
            severity_key = issue.severity.value
            issues_by_severity[severity_key] = issues_by_severity.get(severity_key, 0) + 1
        
        # حساب النقاط الإجمالية
        overall_score = self._calculate_overall_score(issues, len(lines))
        
        # إنشاء التوصيات
        recommendations = self._generate_recommendations(issues)
        
        return AnalysisReport(
            file_path=file_path,
            total_lines=len(lines),
            total_issues=len(issues),
            issues_by_type=issues_by_type,
            issues_by_severity=issues_by_severity,
            issues=issues,
            overall_score=overall_score,
            recommendations=recommendations,
            analysis_time=analysis_time
        )
    
    def _calculate_overall_score(self, issues: List[CodeIssue], total_lines: int) -> float:
        """حساب النقاط الإجمالية للكود"""
        if total_lines == 0:
            return 0.0
        
        # نقاط الخصم حسب الخطورة
        severity_weights = {
            Severity.LOW: 1,
            Severity.MEDIUM: 3,
            Severity.HIGH: 7,
            Severity.CRITICAL: 15
        }
        
        total_penalty = sum(severity_weights[issue.severity] for issue in issues)
        
        # حساب النقاط (من 100)
        base_score = 100
        penalty_per_line = total_penalty / total_lines * 10
        
        score = max(0, base_score - penalty_per_line)
        return round(score, 2)
    
    def _generate_recommendations(self, issues: List[CodeIssue]) -> List[str]:
        """إنشاء التوصيات"""
        recommendations = []
        
        # فحص أنواع المشاكل
        has_security = any(issue.type == IssueType.SECURITY for issue in issues)
        has_performance = any(issue.type == IssueType.PERFORMANCE for issue in issues)
        has_maintainability = any(issue.type == IssueType.MAINTAINABILITY for issue in issues)
        
        if has_security:
            recommendations.extend(self.recommendations_db['security_issues'])
        
        if has_performance:
            recommendations.extend(self.recommendations_db['performance_issues'])
        
        if has_maintainability:
            recommendations.extend(self.recommendations_db['high_complexity'])
        
        # توصيات عامة
        if len(issues) > 20:
            recommendations.append("📊 عدد كبير من المشاكل - فكر في إعادة هيكلة الكود")
        
        if not recommendations:
            recommendations.append("✨ الكود يبدو جيداً! استمر في الممارسات الجيدة")
        
        return recommendations
    
    def _create_syntax_error_report(self, file_path: str, error_msg: str) -> AnalysisReport:
        """إنشاء تقرير لخطأ في بناء الجملة"""
        syntax_issue = CodeIssue(
            type=IssueType.COMPATIBILITY,
            severity=Severity.CRITICAL,
            line_number=1,
            column=0,
            message=f"خطأ في بناء الجملة: {error_msg}",
            suggestion="تصحيح خطأ بناء الجملة قبل المتابعة",
            code_snippet="خطأ في بناء الجملة"
        )
        
        return AnalysisReport(
            file_path=file_path,
            total_lines=0,
            total_issues=1,
            issues_by_type={'compatibility': 1},
            issues_by_severity={'critical': 1},
            issues=[syntax_issue],
            overall_score=0.0,
            recommendations=["🚨 يجب تصحيح خطأ بناء الجملة أولاً"],
            analysis_time=0.0
        )
    
    def _update_stats(self, report: AnalysisReport, analysis_time: float):
        """تحديث إحصائيات التحليل"""
        self.analysis_stats['files_analyzed'] += 1
        self.analysis_stats['total_issues_found'] += report.total_issues
        self.analysis_stats['analysis_time_total'] += analysis_time
    
    def get_detailed_report(self, file_path: str) -> Dict[str, Any]:
        """الحصول على تقرير مفصل"""
        report = self.analyze_file(file_path)
        return asdict(report)
    
    def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التحليل"""
        stats = self.analysis_stats.copy()
        if stats['files_analyzed'] > 0:
            stats['average_issues_per_file'] = stats['total_issues_found'] / stats['files_analyzed']
            stats['average_analysis_time'] = stats['analysis_time_total'] / stats['files_analyzed']
        else:
            stats['average_issues_per_file'] = 0
            stats['average_analysis_time'] = 0
        
        return stats

# إنشاء مثيل الإضافة
def create_plugin():
    return AdvancedCodeAnalyzer()
