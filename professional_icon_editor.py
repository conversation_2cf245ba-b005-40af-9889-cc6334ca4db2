#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محرر الأيقونات الاحترافي - Professional Icon Editor
محرر متقدم يضاهي البرامج المتخصصة مع ذكاء اصطناعي
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance, ImageOps
from PIL.ImageTk import PhotoImage
import os
import json
import math
import threading
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from ai_image_generator import AIImageGenerator, GeneratedImage

@dataclass
class Layer:
    """طبقة في المحرر"""
    id: str
    name: str
    image: Image.Image
    visible: bool = True
    locked: bool = False
    opacity: float = 1.0
    blend_mode: str = "normal"
    x: int = 0
    y: int = 0

@dataclass
class Tool:
    """أداة في المحرر"""
    name: str
    icon: str
    cursor: str
    settings: Dict

class ProfessionalIconEditor:
    """محرر الأيقونات الاحترافي"""
    
    def __init__(self, parent, analysis=None, callback=None):
        self.parent = parent
        self.analysis = analysis
        self.callback = callback
        
        # إعدادات المحرر
        self.canvas_size = 512
        self.zoom_level = 1.0
        self.grid_size = 16
        self.show_grid = True
        
        # الطبقات والأدوات
        self.layers = []
        self.current_layer = None
        self.current_tool = "brush"
        self.current_color = "#000000"
        self.brush_size = 10
        
        # نظام التراجع
        self.history = []
        self.history_index = -1
        self.max_history = 50
        
        # الذكاء الاصطناعي
        self.ai_generator = AIImageGenerator(callback=self.ai_callback)
        self.ai_suggestions = []
        
        self.setup_editor()
    
    def setup_editor(self):
        """إعداد واجهة المحرر الاحترافي"""
        # النافذة الرئيسية
        self.editor_window = tk.Toplevel(self.parent)
        self.editor_window.title("🎨 محرر الأيقونات الاحترافي - Professional Icon Editor")
        self.editor_window.geometry("1400x900")
        self.editor_window.configure(bg='#2b2b2b')
        
        # شريط القوائم
        self.create_menu_bar()
        
        # شريط الأدوات الرئيسي
        self.create_main_toolbar()
        
        # المنطقة الرئيسية
        main_frame = tk.Frame(self.editor_window, bg='#2b2b2b')
        main_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # لوحة الأدوات (يسار)
        self.create_tools_panel(main_frame)
        
        # منطقة الرسم (وسط)
        self.create_canvas_area(main_frame)
        
        # لوحة الخصائص والطبقات (يمين)
        self.create_properties_panel(main_frame)
        
        # شريط الحالة
        self.create_status_bar()
        
        # إنشاء طبقة افتراضية
        self.create_new_layer("Background")
        
        # تحميل اقتراحات الذكاء الاصطناعي إذا كان التحليل متوفراً
        if self.analysis:
            self.load_ai_suggestions()
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.editor_window)
        self.editor_window.config(menu=menubar)
        
        # قائمة ملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="جديد", command=self.new_project, accelerator="Ctrl+N")
        file_menu.add_command(label="فتح", command=self.open_project, accelerator="Ctrl+O")
        file_menu.add_command(label="حفظ", command=self.save_project, accelerator="Ctrl+S")
        file_menu.add_command(label="حفظ باسم", command=self.save_as_project)
        file_menu.add_separator()
        file_menu.add_command(label="تصدير كأيقونة", command=self.export_icon)
        file_menu.add_command(label="تصدير كصورة", command=self.export_image)
        
        # قائمة تحرير
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="تحرير", menu=edit_menu)
        edit_menu.add_command(label="تراجع", command=self.undo, accelerator="Ctrl+Z")
        edit_menu.add_command(label="إعادة", command=self.redo, accelerator="Ctrl+Y")
        edit_menu.add_separator()
        edit_menu.add_command(label="نسخ", command=self.copy_layer, accelerator="Ctrl+C")
        edit_menu.add_command(label="لصق", command=self.paste_layer, accelerator="Ctrl+V")
        edit_menu.add_command(label="حذف", command=self.delete_layer, accelerator="Delete")
        
        # قائمة طبقة
        layer_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="طبقة", menu=layer_menu)
        layer_menu.add_command(label="طبقة جديدة", command=lambda: self.create_new_layer())
        layer_menu.add_command(label="تكرار الطبقة", command=self.duplicate_layer)
        layer_menu.add_command(label="دمج لأسفل", command=self.merge_down)
        layer_menu.add_command(label="تسطيح الصورة", command=self.flatten_image)
        
        # قائمة فلاتر
        filter_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="فلاتر", menu=filter_menu)
        filter_menu.add_command(label="ضبابية", command=lambda: self.apply_filter("blur"))
        filter_menu.add_command(label="حدة", command=lambda: self.apply_filter("sharpen"))
        filter_menu.add_command(label="نقش", command=lambda: self.apply_filter("emboss"))
        filter_menu.add_command(label="كشف الحواف", command=lambda: self.apply_filter("edge"))
        
        # قائمة الذكاء الاصطناعي
        ai_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ذكاء اصطناعي", menu=ai_menu)
        ai_menu.add_command(label="توليد اقتراحات", command=self.generate_ai_suggestions)
        ai_menu.add_command(label="تحسين الصورة", command=self.enhance_with_ai)
        ai_menu.add_command(label="إزالة الخلفية", command=self.remove_background_ai)
        
        # ربط اختصارات لوحة المفاتيح
        self.editor_window.bind('<Control-n>', lambda e: self.new_project())
        self.editor_window.bind('<Control-o>', lambda e: self.open_project())
        self.editor_window.bind('<Control-s>', lambda e: self.save_project())
        self.editor_window.bind('<Control-z>', lambda e: self.undo())
        self.editor_window.bind('<Control-y>', lambda e: self.redo())
    
    def create_main_toolbar(self):
        """إنشاء شريط الأدوات الرئيسي"""
        toolbar = tk.Frame(self.editor_window, bg='#3c3c3c', height=50)
        toolbar.pack(fill='x', padx=5, pady=(5, 0))
        toolbar.pack_propagate(False)
        
        # أزرار الملف
        file_frame = tk.Frame(toolbar, bg='#3c3c3c')
        file_frame.pack(side='left', padx=10, pady=5)
        
        tk.Button(file_frame, text="📄", font=('Segoe UI', 12), command=self.new_project,
                 bg='#4c4c4c', fg='white', relief='flat', width=3).pack(side='left', padx=2)
        tk.Button(file_frame, text="📁", font=('Segoe UI', 12), command=self.open_project,
                 bg='#4c4c4c', fg='white', relief='flat', width=3).pack(side='left', padx=2)
        tk.Button(file_frame, text="💾", font=('Segoe UI', 12), command=self.save_project,
                 bg='#4c4c4c', fg='white', relief='flat', width=3).pack(side='left', padx=2)
        
        # فاصل
        tk.Frame(toolbar, bg='#5c5c5c', width=2).pack(side='left', fill='y', padx=10)
        
        # أزرار التراجع
        undo_frame = tk.Frame(toolbar, bg='#3c3c3c')
        undo_frame.pack(side='left', padx=10, pady=5)
        
        tk.Button(undo_frame, text="↶", font=('Segoe UI', 12), command=self.undo,
                 bg='#4c4c4c', fg='white', relief='flat', width=3).pack(side='left', padx=2)
        tk.Button(undo_frame, text="↷", font=('Segoe UI', 12), command=self.redo,
                 bg='#4c4c4c', fg='white', relief='flat', width=3).pack(side='left', padx=2)
        
        # فاصل
        tk.Frame(toolbar, bg='#5c5c5c', width=2).pack(side='left', fill='y', padx=10)
        
        # أزرار الذكاء الاصطناعي
        ai_frame = tk.Frame(toolbar, bg='#3c3c3c')
        ai_frame.pack(side='left', padx=10, pady=5)
        
        tk.Button(ai_frame, text="🧠", font=('Segoe UI', 12), command=self.generate_ai_suggestions,
                 bg='#6366f1', fg='white', relief='flat', width=4).pack(side='left', padx=2)
        tk.Button(ai_frame, text="✨", font=('Segoe UI', 12), command=self.enhance_with_ai,
                 bg='#8b5cf6', fg='white', relief='flat', width=4).pack(side='left', padx=2)
        
        # معلومات الزوم
        zoom_frame = tk.Frame(toolbar, bg='#3c3c3c')
        zoom_frame.pack(side='right', padx=10, pady=5)
        
        self.zoom_label = tk.Label(zoom_frame, text=f"🔍 {int(self.zoom_level * 100)}%",
                                  bg='#3c3c3c', fg='white', font=('Segoe UI', 10))
        self.zoom_label.pack(side='right')
    
    def create_tools_panel(self, parent):
        """إنشاء لوحة الأدوات"""
        tools_frame = tk.Frame(parent, bg='#3c3c3c', width=200)
        tools_frame.pack(side='left', fill='y', padx=(0, 5))
        tools_frame.pack_propagate(False)
        
        # عنوان الأدوات
        tk.Label(tools_frame, text="🛠️ صندوق الأدوات", font=('Segoe UI', 12, 'bold'),
                bg='#3c3c3c', fg='white').pack(pady=10)
        
        # أدوات الرسم
        drawing_frame = tk.LabelFrame(tools_frame, text="أدوات الرسم", bg='#3c3c3c', fg='white')
        drawing_frame.pack(fill='x', padx=10, pady=5)
        
        drawing_tools = [
            ("🖌️", "brush", "فرشاة"),
            ("✏️", "pencil", "قلم رصاص"),
            ("🖍️", "eraser", "ممحاة"),
            ("🪣", "bucket", "دلو الطلاء"),
            ("💧", "dropper", "قطارة الألوان")
        ]
        
        self.tool_buttons = {}
        for icon, tool_id, tooltip in drawing_tools:
            btn = tk.Button(drawing_frame, text=icon, font=('Segoe UI', 14),
                           command=lambda t=tool_id: self.select_tool(t),
                           bg='#4c4c4c', fg='white', relief='flat', width=4, height=2)
            btn.pack(side='left', padx=2, pady=2)
            self.tool_buttons[tool_id] = btn
        
        # أدوات التحديد
        selection_frame = tk.LabelFrame(tools_frame, text="أدوات التحديد", bg='#3c3c3c', fg='white')
        selection_frame.pack(fill='x', padx=10, pady=5)
        
        selection_tools = [
            ("⬜", "select_rect", "تحديد مستطيل"),
            ("⭕", "select_circle", "تحديد دائري"),
            ("🪄", "magic_wand", "العصا السحرية"),
            ("✂️", "lasso", "أداة اللاسو")
        ]
        
        for icon, tool_id, tooltip in selection_tools:
            btn = tk.Button(selection_frame, text=icon, font=('Segoe UI', 14),
                           command=lambda t=tool_id: self.select_tool(t),
                           bg='#4c4c4c', fg='white', relief='flat', width=4, height=2)
            btn.pack(side='left', padx=2, pady=2)
            self.tool_buttons[tool_id] = btn
        
        # أدوات الأشكال
        shapes_frame = tk.LabelFrame(tools_frame, text="الأشكال", bg='#3c3c3c', fg='white')
        shapes_frame.pack(fill='x', padx=10, pady=5)
        
        shape_tools = [
            ("📏", "line", "خط"),
            ("⬜", "rectangle", "مستطيل"),
            ("⭕", "circle", "دائرة"),
            ("🔺", "triangle", "مثلث"),
            ("⭐", "star", "نجمة")
        ]
        
        for icon, tool_id, tooltip in shape_tools:
            btn = tk.Button(shapes_frame, text=icon, font=('Segoe UI', 14),
                           command=lambda t=tool_id: self.select_tool(t),
                           bg='#4c4c4c', fg='white', relief='flat', width=4, height=2)
            btn.pack(side='left', padx=2, pady=2)
            self.tool_buttons[tool_id] = btn
        
        # إعدادات الأداة
        settings_frame = tk.LabelFrame(tools_frame, text="إعدادات الأداة", bg='#3c3c3c', fg='white')
        settings_frame.pack(fill='x', padx=10, pady=5)
        
        # حجم الفرشاة
        tk.Label(settings_frame, text="حجم الفرشاة:", bg='#3c3c3c', fg='white').pack(anchor='w')
        self.brush_size_var = tk.IntVar(value=self.brush_size)
        brush_scale = tk.Scale(settings_frame, from_=1, to=100, orient='horizontal',
                              variable=self.brush_size_var, bg='#3c3c3c', fg='white',
                              command=self.update_brush_size)
        brush_scale.pack(fill='x', padx=5)
        
        # الشفافية
        tk.Label(settings_frame, text="الشفافية:", bg='#3c3c3c', fg='white').pack(anchor='w')
        self.opacity_var = tk.IntVar(value=100)
        opacity_scale = tk.Scale(settings_frame, from_=1, to=100, orient='horizontal',
                                variable=self.opacity_var, bg='#3c3c3c', fg='white')
        opacity_scale.pack(fill='x', padx=5)
        
        # اختيار اللون
        color_frame = tk.Frame(settings_frame, bg='#3c3c3c')
        color_frame.pack(fill='x', pady=5)
        
        tk.Label(color_frame, text="اللون:", bg='#3c3c3c', fg='white').pack(side='left')
        self.color_display = tk.Label(color_frame, bg=self.current_color, width=4, height=2,
                                     relief='solid', bd=1)
        self.color_display.pack(side='right', padx=5)
        self.color_display.bind('<Button-1>', self.choose_color)
        
        # اقتراحات الذكاء الاصطناعي
        ai_frame = tk.LabelFrame(tools_frame, text="🧠 اقتراحات الذكاء الاصطناعي", 
                                bg='#3c3c3c', fg='white')
        ai_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # قائمة الاقتراحات
        self.ai_listbox = tk.Listbox(ai_frame, bg='#4c4c4c', fg='white', height=6,
                                    selectbackground='#6366f1')
        self.ai_listbox.pack(fill='both', expand=True, padx=5, pady=5)
        self.ai_listbox.bind('<Double-Button-1>', self.apply_ai_suggestion)
        
        # أزرار الذكاء الاصطناعي
        ai_buttons_frame = tk.Frame(ai_frame, bg='#3c3c3c')
        ai_buttons_frame.pack(fill='x', padx=5, pady=5)
        
        tk.Button(ai_buttons_frame, text="🧠 توليد", command=self.generate_ai_suggestions,
                 bg='#6366f1', fg='white', relief='flat').pack(side='left', padx=2)
        tk.Button(ai_buttons_frame, text="✅ تطبيق", command=self.apply_ai_suggestion,
                 bg='#10b981', fg='white', relief='flat').pack(side='right', padx=2)
        
        # تحديد الأداة الافتراضية
        self.select_tool("brush")
    
    def create_canvas_area(self, parent):
        """إنشاء منطقة الرسم"""
        canvas_frame = tk.Frame(parent, bg='#2b2b2b')
        canvas_frame.pack(side='left', fill='both', expand=True)
        
        # شريط أدوات الرسم
        canvas_toolbar = tk.Frame(canvas_frame, bg='#3c3c3c', height=40)
        canvas_toolbar.pack(fill='x', pady=(0, 5))
        canvas_toolbar.pack_propagate(False)
        
        # أزرار الزوم
        zoom_frame = tk.Frame(canvas_toolbar, bg='#3c3c3c')
        zoom_frame.pack(side='left', padx=10, pady=5)
        
        tk.Button(zoom_frame, text="🔍+", command=self.zoom_in,
                 bg='#4c4c4c', fg='white', relief='flat', width=4).pack(side='left', padx=2)
        tk.Button(zoom_frame, text="🔍-", command=self.zoom_out,
                 bg='#4c4c4c', fg='white', relief='flat', width=4).pack(side='left', padx=2)
        tk.Button(zoom_frame, text="🎯", command=self.zoom_fit,
                 bg='#4c4c4c', fg='white', relief='flat', width=4).pack(side='left', padx=2)
        
        # إعدادات الشبكة
        grid_frame = tk.Frame(canvas_toolbar, bg='#3c3c3c')
        grid_frame.pack(side='left', padx=20, pady=5)
        
        self.grid_var = tk.BooleanVar(value=self.show_grid)
        tk.Checkbutton(grid_frame, text="إظهار الشبكة", variable=self.grid_var,
                      command=self.toggle_grid, bg='#3c3c3c', fg='white',
                      selectcolor='#4c4c4c').pack(side='left')
        
        # منطقة الرسم مع شريط التمرير
        canvas_container = tk.Frame(canvas_frame, bg='#2b2b2b')
        canvas_container.pack(fill='both', expand=True)
        
        # Canvas للرسم
        self.canvas = tk.Canvas(canvas_container, bg='white', width=self.canvas_size,
                               height=self.canvas_size, scrollregion=(0, 0, self.canvas_size, self.canvas_size))
        
        # أشرطة التمرير
        h_scrollbar = tk.Scrollbar(canvas_container, orient='horizontal', command=self.canvas.xview)
        v_scrollbar = tk.Scrollbar(canvas_container, orient='vertical', command=self.canvas.yview)
        
        self.canvas.configure(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)
        
        # تخطيط أشرطة التمرير والـ Canvas
        self.canvas.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')
        
        # ربط أحداث الماوس
        self.canvas.bind('<Button-1>', self.on_canvas_click)
        self.canvas.bind('<B1-Motion>', self.on_canvas_drag)
        self.canvas.bind('<ButtonRelease-1>', self.on_canvas_release)
        self.canvas.bind('<MouseWheel>', self.on_mouse_wheel)
        
        # رسم الشبكة
        self.draw_grid()
    
    def create_properties_panel(self, parent):
        """إنشاء لوحة الخصائص والطبقات"""
        props_frame = tk.Frame(parent, bg='#3c3c3c', width=250)
        props_frame.pack(side='right', fill='y', padx=(5, 0))
        props_frame.pack_propagate(False)
        
        # تبويبات
        notebook = ttk.Notebook(props_frame)
        notebook.pack(fill='both', expand=True, padx=5, pady=5)
        
        # تبويب الطبقات
        layers_frame = tk.Frame(notebook, bg='#3c3c3c')
        notebook.add(layers_frame, text="الطبقات")
        
        self.create_layers_panel(layers_frame)
        
        # تبويب الخصائص
        properties_frame = tk.Frame(notebook, bg='#3c3c3c')
        notebook.add(properties_frame, text="الخصائص")
        
        self.create_properties_controls(properties_frame)
        
        # تبويب التاريخ
        history_frame = tk.Frame(notebook, bg='#3c3c3c')
        notebook.add(history_frame, text="التاريخ")
        
        self.create_history_panel(history_frame)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = tk.Frame(self.editor_window, bg='#3c3c3c', height=25)
        self.status_bar.pack(fill='x', side='bottom')
        self.status_bar.pack_propagate(False)
        
        self.status_label = tk.Label(self.status_bar, text="جاهز", bg='#3c3c3c', fg='white',
                                    font=('Segoe UI', 9))
        self.status_label.pack(side='left', padx=10)
        
        # معلومات الماوس
        self.mouse_pos_label = tk.Label(self.status_bar, text="", bg='#3c3c3c', fg='white',
                                       font=('Segoe UI', 9))
        self.mouse_pos_label.pack(side='right', padx=10)

    # ==================== وظائف الطبقات ====================

    def create_layers_panel(self, parent):
        """إنشاء لوحة الطبقات"""
        # أزرار الطبقات
        buttons_frame = tk.Frame(parent, bg='#3c3c3c')
        buttons_frame.pack(fill='x', padx=5, pady=5)

        tk.Button(buttons_frame, text="➕", command=lambda: self.create_new_layer(),
                 bg='#4c4c4c', fg='white', relief='flat', width=3).pack(side='left', padx=2)
        tk.Button(buttons_frame, text="🗑️", command=self.delete_layer,
                 bg='#4c4c4c', fg='white', relief='flat', width=3).pack(side='left', padx=2)
        tk.Button(buttons_frame, text="📋", command=self.duplicate_layer,
                 bg='#4c4c4c', fg='white', relief='flat', width=3).pack(side='left', padx=2)

        # قائمة الطبقات
        self.layers_listbox = tk.Listbox(parent, bg='#4c4c4c', fg='white',
                                        selectbackground='#6366f1', height=10)
        self.layers_listbox.pack(fill='both', expand=True, padx=5, pady=5)
        self.layers_listbox.bind('<<ListboxSelect>>', self.on_layer_select)

        # إعدادات الطبقة
        layer_settings = tk.Frame(parent, bg='#3c3c3c')
        layer_settings.pack(fill='x', padx=5, pady=5)

        # شفافية الطبقة
        tk.Label(layer_settings, text="شفافية الطبقة:", bg='#3c3c3c', fg='white').pack(anchor='w')
        self.layer_opacity_var = tk.DoubleVar(value=1.0)
        opacity_scale = tk.Scale(layer_settings, from_=0.0, to=1.0, resolution=0.1,
                                orient='horizontal', variable=self.layer_opacity_var,
                                bg='#3c3c3c', fg='white', command=self.update_layer_opacity)
        opacity_scale.pack(fill='x')

        # أوضاع المزج
        tk.Label(layer_settings, text="وضع المزج:", bg='#3c3c3c', fg='white').pack(anchor='w')
        self.blend_mode_var = tk.StringVar(value="normal")
        blend_combo = ttk.Combobox(layer_settings, textvariable=self.blend_mode_var,
                                  values=["normal", "multiply", "screen", "overlay", "soft_light"])
        blend_combo.pack(fill='x', pady=2)
        blend_combo.bind('<<ComboboxSelected>>', self.update_blend_mode)

    def create_properties_controls(self, parent):
        """إنشاء عناصر تحكم الخصائص"""
        # خصائص الصورة
        image_frame = tk.LabelFrame(parent, text="خصائص الصورة", bg='#3c3c3c', fg='white')
        image_frame.pack(fill='x', padx=5, pady=5)

        tk.Label(image_frame, text=f"الحجم: {self.canvas_size}x{self.canvas_size}",
                bg='#3c3c3c', fg='white').pack(anchor='w')
        tk.Label(image_frame, text="التنسيق: RGBA", bg='#3c3c3c', fg='white').pack(anchor='w')

        # أزرار تغيير الحجم
        resize_frame = tk.Frame(image_frame, bg='#3c3c3c')
        resize_frame.pack(fill='x', pady=5)

        tk.Button(resize_frame, text="تغيير الحجم", command=self.resize_canvas,
                 bg='#4c4c4c', fg='white', relief='flat').pack(side='left', padx=2)
        tk.Button(resize_frame, text="قص", command=self.crop_canvas,
                 bg='#4c4c4c', fg='white', relief='flat').pack(side='left', padx=2)

        # تأثيرات سريعة
        effects_frame = tk.LabelFrame(parent, text="تأثيرات سريعة", bg='#3c3c3c', fg='white')
        effects_frame.pack(fill='x', padx=5, pady=5)

        effects = [
            ("ضبابية", lambda: self.apply_filter("blur")),
            ("حدة", lambda: self.apply_filter("sharpen")),
            ("نقش", lambda: self.apply_filter("emboss")),
            ("سطوع", self.adjust_brightness),
            ("تباين", self.adjust_contrast),
            ("تشبع", self.adjust_saturation)
        ]

        for effect_name, command in effects:
            tk.Button(effects_frame, text=effect_name, command=command,
                     bg='#4c4c4c', fg='white', relief='flat', width=10).pack(pady=2, fill='x')

    def create_history_panel(self, parent):
        """إنشاء لوحة التاريخ"""
        tk.Label(parent, text="تاريخ العمليات:", bg='#3c3c3c', fg='white',
                font=('Segoe UI', 10, 'bold')).pack(pady=5)

        self.history_listbox = tk.Listbox(parent, bg='#4c4c4c', fg='white',
                                         selectbackground='#6366f1')
        self.history_listbox.pack(fill='both', expand=True, padx=5, pady=5)
        self.history_listbox.bind('<Double-Button-1>', self.goto_history_state)

    # ==================== وظائف الأدوات ====================

    def select_tool(self, tool_name):
        """تحديد أداة"""
        # إعادة تعيين ألوان الأزرار
        for btn in self.tool_buttons.values():
            btn.configure(bg='#4c4c4c')

        # تمييز الأداة المحددة
        if tool_name in self.tool_buttons:
            self.tool_buttons[tool_name].configure(bg='#6366f1')

        self.current_tool = tool_name
        self.update_status(f"تم تحديد أداة: {tool_name}")

    def choose_color(self, event=None):
        """اختيار لون"""
        color = colorchooser.askcolor(title="اختر لون")[1]
        if color:
            self.current_color = color
            self.color_display.configure(bg=color)

    def update_brush_size(self, value):
        """تحديث حجم الفرشاة"""
        self.brush_size = int(value)

    # ==================== وظائف الرسم ====================

    def on_canvas_click(self, event):
        """النقر على اللوحة"""
        x, y = self.canvas.canvasx(event.x), self.canvas.canvasy(event.y)

        if self.current_tool == "brush":
            self.start_brush_stroke(x, y)
        elif self.current_tool == "pencil":
            self.start_pencil_stroke(x, y)
        elif self.current_tool == "eraser":
            self.start_eraser_stroke(x, y)
        elif self.current_tool == "bucket":
            self.bucket_fill(x, y)
        elif self.current_tool == "dropper":
            self.pick_color(x, y)

        self.last_x, self.last_y = x, y

    def on_canvas_drag(self, event):
        """سحب على اللوحة"""
        x, y = self.canvas.canvasx(event.x), self.canvas.canvasy(event.y)

        if self.current_tool in ["brush", "pencil", "eraser"]:
            self.continue_stroke(x, y)

        self.last_x, self.last_y = x, y
        self.update_mouse_position(x, y)

    def on_canvas_release(self, event):
        """تحرير الماوس"""
        if self.current_tool in ["brush", "pencil", "eraser"]:
            self.end_stroke()

    def start_brush_stroke(self, x, y):
        """بدء ضربة فرشاة"""
        if self.current_layer:
            self.save_state("Brush Stroke")
            # رسم نقطة
            self.draw_brush_point(x, y)

    def continue_stroke(self, x, y):
        """متابعة الضربة"""
        if hasattr(self, 'last_x') and self.current_layer:
            # رسم خط من النقطة السابقة إلى الحالية
            self.draw_brush_line(self.last_x, self.last_y, x, y)

    def draw_brush_point(self, x, y):
        """رسم نقطة فرشاة"""
        if self.current_layer:
            # رسم على طبقة PIL
            draw = ImageDraw.Draw(self.current_layer.image)
            radius = self.brush_size // 2
            color = self.current_color

            draw.ellipse([x-radius, y-radius, x+radius, y+radius], fill=color)
            self.update_canvas_display()

    def draw_brush_line(self, x1, y1, x2, y2):
        """رسم خط فرشاة"""
        if self.current_layer:
            draw = ImageDraw.Draw(self.current_layer.image)
            color = self.current_color

            draw.line([x1, y1, x2, y2], fill=color, width=self.brush_size)
            self.update_canvas_display()

    # ==================== وظائف الطبقات ====================

    def create_new_layer(self, name=None):
        """إنشاء طبقة جديدة"""
        if name is None:
            name = f"Layer {len(self.layers) + 1}"

        # إنشاء صورة فارغة للطبقة
        layer_image = Image.new('RGBA', (self.canvas_size, self.canvas_size), (0, 0, 0, 0))

        # إنشاء كائن الطبقة
        layer = Layer(
            id=f"layer_{len(self.layers)}",
            name=name,
            image=layer_image
        )

        self.layers.append(layer)
        self.current_layer = layer

        self.update_layers_list()
        self.update_canvas_display()
        self.save_state(f"New Layer: {name}")

    def delete_layer(self):
        """حذف الطبقة الحالية"""
        if self.current_layer and len(self.layers) > 1:
            self.layers.remove(self.current_layer)
            self.current_layer = self.layers[-1] if self.layers else None

            self.update_layers_list()
            self.update_canvas_display()
            self.save_state("Delete Layer")

    def duplicate_layer(self):
        """تكرار الطبقة الحالية"""
        if self.current_layer:
            # نسخ الطبقة
            new_image = self.current_layer.image.copy()
            new_layer = Layer(
                id=f"layer_{len(self.layers)}",
                name=f"{self.current_layer.name} Copy",
                image=new_image,
                visible=self.current_layer.visible,
                opacity=self.current_layer.opacity
            )

            # إدراج بعد الطبقة الحالية
            current_index = self.layers.index(self.current_layer)
            self.layers.insert(current_index + 1, new_layer)
            self.current_layer = new_layer

            self.update_layers_list()
            self.update_canvas_display()
            self.save_state("Duplicate Layer")

    def update_layers_list(self):
        """تحديث قائمة الطبقات"""
        self.layers_listbox.delete(0, tk.END)

        for i, layer in enumerate(reversed(self.layers)):
            visibility = "👁️" if layer.visible else "🙈"
            lock = "🔒" if layer.locked else ""
            display_text = f"{visibility}{lock} {layer.name}"
            self.layers_listbox.insert(0, display_text)

            # تمييز الطبقة الحالية
            if layer == self.current_layer:
                self.layers_listbox.selection_set(len(self.layers) - 1 - i)

    def on_layer_select(self, event):
        """عند تحديد طبقة"""
        selection = self.layers_listbox.curselection()
        if selection:
            # تحويل الفهرس (القائمة معكوسة)
            layer_index = len(self.layers) - 1 - selection[0]
            if 0 <= layer_index < len(self.layers):
                self.current_layer = self.layers[layer_index]
                self.layer_opacity_var.set(self.current_layer.opacity)

    def update_layer_opacity(self, value):
        """تحديث شفافية الطبقة"""
        if self.current_layer:
            self.current_layer.opacity = float(value)
            self.update_canvas_display()

    def update_blend_mode(self, event):
        """تحديث وضع المزج"""
        if self.current_layer:
            self.current_layer.blend_mode = self.blend_mode_var.get()
            self.update_canvas_display()

    # ==================== وظائف العرض ====================

    def update_canvas_display(self):
        """تحديث عرض اللوحة"""
        # دمج جميع الطبقات
        final_image = Image.new('RGBA', (self.canvas_size, self.canvas_size), (255, 255, 255, 0))

        for layer in self.layers:
            if layer.visible:
                # تطبيق الشفافية
                layer_image = layer.image.copy()
                if layer.opacity < 1.0:
                    # تطبيق الشفافية
                    alpha = layer_image.split()[-1]
                    alpha = alpha.point(lambda p: int(p * layer.opacity))
                    layer_image.putalpha(alpha)

                # دمج الطبقة
                final_image = Image.alpha_composite(final_image, layer_image)

        # تحويل لعرض في tkinter
        display_image = final_image.copy()
        if self.zoom_level != 1.0:
            new_size = int(self.canvas_size * self.zoom_level)
            display_image = display_image.resize((new_size, new_size), Image.Resampling.NEAREST)

        self.photo = PhotoImage(display_image)

        # مسح اللوحة وعرض الصورة الجديدة
        self.canvas.delete("image")
        self.canvas.create_image(0, 0, anchor='nw', image=self.photo, tags="image")

        # تحديث منطقة التمرير
        if self.zoom_level != 1.0:
            new_size = int(self.canvas_size * self.zoom_level)
            self.canvas.configure(scrollregion=(0, 0, new_size, new_size))

    def draw_grid(self):
        """رسم الشبكة"""
        if self.show_grid:
            self.canvas.delete("grid")

            grid_size = int(self.grid_size * self.zoom_level)
            canvas_size = int(self.canvas_size * self.zoom_level)

            # خطوط عمودية
            for x in range(0, canvas_size, grid_size):
                self.canvas.create_line(x, 0, x, canvas_size, fill='#cccccc', tags="grid")

            # خطوط أفقية
            for y in range(0, canvas_size, grid_size):
                self.canvas.create_line(0, y, canvas_size, y, fill='#cccccc', tags="grid")

    def toggle_grid(self):
        """تبديل عرض الشبكة"""
        self.show_grid = self.grid_var.get()
        if self.show_grid:
            self.draw_grid()
        else:
            self.canvas.delete("grid")

    # ==================== وظائف الزوم ====================

    def zoom_in(self):
        """تكبير"""
        self.zoom_level = min(self.zoom_level * 1.5, 10.0)
        self.update_zoom()

    def zoom_out(self):
        """تصغير"""
        self.zoom_level = max(self.zoom_level / 1.5, 0.1)
        self.update_zoom()

    def zoom_fit(self):
        """ملائمة الحجم"""
        self.zoom_level = 1.0
        self.update_zoom()

    def update_zoom(self):
        """تحديث الزوم"""
        self.zoom_label.configure(text=f"🔍 {int(self.zoom_level * 100)}%")
        self.update_canvas_display()
        self.draw_grid()

    def on_mouse_wheel(self, event):
        """عجلة الماوس للزوم"""
        if event.state & 0x4:  # Ctrl مضغوط
            if event.delta > 0:
                self.zoom_in()
            else:
                self.zoom_out()

    # ==================== وظائف التراجع ====================

    def save_state(self, action_name):
        """حفظ حالة للتراجع"""
        # حفظ حالة جميع الطبقات
        state = {
            'action': action_name,
            'layers': [],
            'current_layer_id': self.current_layer.id if self.current_layer else None
        }

        for layer in self.layers:
            layer_state = {
                'id': layer.id,
                'name': layer.name,
                'image': layer.image.copy(),
                'visible': layer.visible,
                'locked': layer.locked,
                'opacity': layer.opacity,
                'blend_mode': layer.blend_mode
            }
            state['layers'].append(layer_state)

        # إضافة للتاريخ
        if self.history_index < len(self.history) - 1:
            # حذف الحالات اللاحقة
            self.history = self.history[:self.history_index + 1]

        self.history.append(state)
        self.history_index += 1

        # الحد الأقصى للتاريخ
        if len(self.history) > self.max_history:
            self.history.pop(0)
            self.history_index -= 1

        self.update_history_list()

    def undo(self):
        """تراجع"""
        if self.history_index > 0:
            self.history_index -= 1
            self.restore_state(self.history[self.history_index])

    def redo(self):
        """إعادة"""
        if self.history_index < len(self.history) - 1:
            self.history_index += 1
            self.restore_state(self.history[self.history_index])

    def restore_state(self, state):
        """استعادة حالة"""
        # مسح الطبقات الحالية
        self.layers.clear()

        # استعادة الطبقات
        for layer_state in state['layers']:
            layer = Layer(
                id=layer_state['id'],
                name=layer_state['name'],
                image=layer_state['image'].copy(),
                visible=layer_state['visible'],
                locked=layer_state['locked'],
                opacity=layer_state['opacity'],
                blend_mode=layer_state['blend_mode']
            )
            self.layers.append(layer)

        # استعادة الطبقة الحالية
        current_layer_id = state['current_layer_id']
        self.current_layer = None
        for layer in self.layers:
            if layer.id == current_layer_id:
                self.current_layer = layer
                break

        if not self.current_layer and self.layers:
            self.current_layer = self.layers[0]

        self.update_layers_list()
        self.update_canvas_display()
        self.update_history_list()

    def update_history_list(self):
        """تحديث قائمة التاريخ"""
        self.history_listbox.delete(0, tk.END)

        for i, state in enumerate(self.history):
            marker = "→ " if i == self.history_index else "   "
            self.history_listbox.insert(tk.END, f"{marker}{state['action']}")

    # ==================== وظائف مساعدة ====================

    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_label.configure(text=message)

    def update_mouse_position(self, x, y):
        """تحديث موقع الماوس"""
        self.mouse_pos_label.configure(text=f"X: {int(x)}, Y: {int(y)}")

    def ai_callback(self, message):
        """استقبال رسائل من الذكاء الاصطناعي"""
        self.update_status(message)

    # ==================== وظائف الذكاء الاصطناعي ====================

    def load_ai_suggestions(self):
        """تحميل اقتراحات الذكاء الاصطناعي"""
        if self.analysis:
            threading.Thread(target=self._load_ai_suggestions_thread, daemon=True).start()

    def _load_ai_suggestions_thread(self):
        """تحميل الاقتراحات في thread منفصل"""
        try:
            self.ai_suggestions = self.ai_generator.generate_images_for_analysis(self.analysis)
            self.editor_window.after(0, self.update_ai_suggestions_list)
        except Exception as e:
            self.editor_window.after(0, lambda: self.update_status(f"خطأ في تحميل الاقتراحات: {e}"))

    def generate_ai_suggestions(self):
        """توليد اقتراحات جديدة"""
        if not self.analysis:
            messagebox.showwarning("تحذير", "لا يوجد تحليل للكود متاح")
            return

        self.update_status("🧠 توليد اقتراحات الذكاء الاصطناعي...")
        threading.Thread(target=self._generate_ai_suggestions_thread, daemon=True).start()

    def _generate_ai_suggestions_thread(self):
        """توليد الاقتراحات في thread منفصل"""
        try:
            new_suggestions = self.ai_generator.generate_images_for_analysis(self.analysis, count=8)
            self.ai_suggestions.extend(new_suggestions)
            self.editor_window.after(0, self.update_ai_suggestions_list)
            self.editor_window.after(0, lambda: self.update_status("✅ تم توليد اقتراحات جديدة"))
        except Exception as e:
            self.editor_window.after(0, lambda: self.update_status(f"❌ خطأ في التوليد: {e}"))

    def update_ai_suggestions_list(self):
        """تحديث قائمة اقتراحات الذكاء الاصطناعي"""
        self.ai_listbox.delete(0, tk.END)

        for i, suggestion in enumerate(self.ai_suggestions):
            confidence_stars = "⭐" * int(suggestion.confidence * 5)
            style_icon = self.get_style_icon(suggestion.prompt.style)
            display_text = f"{style_icon} {suggestion.prompt.style} {confidence_stars}"
            self.ai_listbox.insert(tk.END, display_text)

    def get_style_icon(self, style):
        """الحصول على أيقونة النمط"""
        style_icons = {
            "minimalist": "📱",
            "3D": "🎲",
            "professional": "💼",
            "vibrant": "🌈",
            "elegant": "✨",
            "cartoon": "🎨"
        }

        for key, icon in style_icons.items():
            if key in style.lower():
                return icon
        return "🖼️"

    def apply_ai_suggestion(self, event=None):
        """تطبيق اقتراح الذكاء الاصطناعي"""
        selection = self.ai_listbox.curselection()
        if not selection or not self.ai_suggestions:
            messagebox.showwarning("تحذير", "يرجى اختيار اقتراح أولاً")
            return

        index = selection[0]
        if index >= len(self.ai_suggestions):
            return

        suggestion = self.ai_suggestions[index]

        # إنشاء طبقة جديدة مع الصورة المقترحة
        layer_name = f"AI: {suggestion.prompt.style}"
        self.create_new_layer(layer_name)

        # نسخ الصورة المقترحة إلى الطبقة
        if self.current_layer:
            # تغيير حجم الصورة لتناسب اللوحة
            resized_image = suggestion.image.copy()
            if resized_image.size != (self.canvas_size, self.canvas_size):
                resized_image = resized_image.resize((self.canvas_size, self.canvas_size), Image.Resampling.LANCZOS)

            self.current_layer.image = resized_image
            self.update_canvas_display()
            self.save_state(f"Apply AI Suggestion: {suggestion.prompt.style}")

            self.update_status(f"✅ تم تطبيق اقتراح: {suggestion.prompt.style}")

    def enhance_with_ai(self):
        """تحسين الصورة بالذكاء الاصطناعي"""
        if not self.current_layer:
            messagebox.showwarning("تحذير", "لا توجد طبقة محددة")
            return

        self.update_status("✨ تحسين الصورة بالذكاء الاصطناعي...")

        # تطبيق تحسينات متقدمة
        enhanced_image = self.current_layer.image.copy()

        # تحسين الحدة
        enhanced_image = enhanced_image.filter(ImageFilter.UnsharpMask(radius=1, percent=150, threshold=3))

        # تحسين التباين
        enhancer = ImageEnhance.Contrast(enhanced_image)
        enhanced_image = enhancer.enhance(1.2)

        # تحسين الألوان
        enhancer = ImageEnhance.Color(enhanced_image)
        enhanced_image = enhancer.enhance(1.1)

        # تطبيق التحسين
        self.current_layer.image = enhanced_image
        self.update_canvas_display()
        self.save_state("AI Enhancement")

        self.update_status("✅ تم تحسين الصورة")

    def remove_background_ai(self):
        """إزالة الخلفية بالذكاء الاصطناعي"""
        if not self.current_layer:
            messagebox.showwarning("تحذير", "لا توجد طبقة محددة")
            return

        self.update_status("🎯 إزالة الخلفية بالذكاء الاصطناعي...")

        try:
            # خوارزمية بسيطة لإزالة الخلفية
            image = self.current_layer.image.copy()

            # تحويل إلى RGB للمعالجة
            if image.mode == 'RGBA':
                rgb_image = Image.new('RGB', image.size, (255, 255, 255))
                rgb_image.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
            else:
                rgb_image = image.convert('RGB')

            # اكتشاف لون الخلفية من الزوايا
            width, height = rgb_image.size
            corner_colors = [
                rgb_image.getpixel((0, 0)),
                rgb_image.getpixel((width-1, 0)),
                rgb_image.getpixel((0, height-1)),
                rgb_image.getpixel((width-1, height-1))
            ]

            # حساب متوسط لون الخلفية
            avg_color = tuple(sum(colors) // len(colors) for colors in zip(*corner_colors))

            # إنشاء قناع للخلفية
            mask = Image.new('L', image.size, 0)

            for x in range(width):
                for y in range(height):
                    pixel = rgb_image.getpixel((x, y))
                    # حساب المسافة اللونية
                    distance = sum(abs(a - b) for a, b in zip(pixel, avg_color))

                    # إذا كان اللون قريب من لون الخلفية
                    if distance < 50:  # عتبة التشابه
                        mask.putpixel((x, y), 0)  # شفاف
                    else:
                        mask.putpixel((x, y), 255)  # معتم

            # تطبيق القناع
            result = Image.new('RGBA', image.size, (0, 0, 0, 0))
            result.paste(image, mask=mask)

            self.current_layer.image = result
            self.update_canvas_display()
            self.save_state("Remove Background")

            self.update_status("✅ تم إزالة الخلفية")

        except Exception as e:
            self.update_status(f"❌ خطأ في إزالة الخلفية: {e}")

    # ==================== وظائف الفلاتر ====================

    def apply_filter(self, filter_type):
        """تطبيق فلتر"""
        if not self.current_layer:
            messagebox.showwarning("تحذير", "لا توجد طبقة محددة")
            return

        self.save_state(f"Apply Filter: {filter_type}")

        image = self.current_layer.image.copy()

        if filter_type == "blur":
            image = image.filter(ImageFilter.GaussianBlur(radius=2))
        elif filter_type == "sharpen":
            image = image.filter(ImageFilter.SHARPEN)
        elif filter_type == "emboss":
            image = image.filter(ImageFilter.EMBOSS)
        elif filter_type == "edge":
            image = image.filter(ImageFilter.FIND_EDGES)
        elif filter_type == "smooth":
            image = image.filter(ImageFilter.SMOOTH)
        elif filter_type == "detail":
            image = image.filter(ImageFilter.DETAIL)

        self.current_layer.image = image
        self.update_canvas_display()
        self.update_status(f"✅ تم تطبيق فلتر: {filter_type}")

    def adjust_brightness(self):
        """ضبط السطوع"""
        if not self.current_layer:
            return

        # نافذة ضبط السطوع
        dialog = tk.Toplevel(self.editor_window)
        dialog.title("ضبط السطوع")
        dialog.geometry("300x150")
        dialog.configure(bg='#3c3c3c')

        tk.Label(dialog, text="السطوع:", bg='#3c3c3c', fg='white').pack(pady=10)

        brightness_var = tk.DoubleVar(value=1.0)
        scale = tk.Scale(dialog, from_=0.1, to=3.0, resolution=0.1, orient='horizontal',
                        variable=brightness_var, bg='#3c3c3c', fg='white')
        scale.pack(fill='x', padx=20)

        def apply_brightness():
            enhancer = ImageEnhance.Brightness(self.current_layer.image)
            self.current_layer.image = enhancer.enhance(brightness_var.get())
            self.update_canvas_display()

        def preview_brightness(value):
            apply_brightness()

        scale.configure(command=preview_brightness)

        button_frame = tk.Frame(dialog, bg='#3c3c3c')
        button_frame.pack(pady=10)

        tk.Button(button_frame, text="تطبيق", command=lambda: [self.save_state("Adjust Brightness"), dialog.destroy()],
                 bg='#10b981', fg='white').pack(side='left', padx=5)
        tk.Button(button_frame, text="إلغاء", command=dialog.destroy,
                 bg='#ef4444', fg='white').pack(side='left', padx=5)

    def adjust_contrast(self):
        """ضبط التباين"""
        if not self.current_layer:
            return

        dialog = tk.Toplevel(self.editor_window)
        dialog.title("ضبط التباين")
        dialog.geometry("300x150")
        dialog.configure(bg='#3c3c3c')

        tk.Label(dialog, text="التباين:", bg='#3c3c3c', fg='white').pack(pady=10)

        contrast_var = tk.DoubleVar(value=1.0)
        scale = tk.Scale(dialog, from_=0.1, to=3.0, resolution=0.1, orient='horizontal',
                        variable=contrast_var, bg='#3c3c3c', fg='white')
        scale.pack(fill='x', padx=20)

        def apply_contrast():
            enhancer = ImageEnhance.Contrast(self.current_layer.image)
            self.current_layer.image = enhancer.enhance(contrast_var.get())
            self.update_canvas_display()

        scale.configure(command=lambda v: apply_contrast())

        button_frame = tk.Frame(dialog, bg='#3c3c3c')
        button_frame.pack(pady=10)

        tk.Button(button_frame, text="تطبيق", command=lambda: [self.save_state("Adjust Contrast"), dialog.destroy()],
                 bg='#10b981', fg='white').pack(side='left', padx=5)
        tk.Button(button_frame, text="إلغاء", command=dialog.destroy,
                 bg='#ef4444', fg='white').pack(side='left', padx=5)

    def adjust_saturation(self):
        """ضبط التشبع"""
        if not self.current_layer:
            return

        dialog = tk.Toplevel(self.editor_window)
        dialog.title("ضبط التشبع")
        dialog.geometry("300x150")
        dialog.configure(bg='#3c3c3c')

        tk.Label(dialog, text="التشبع:", bg='#3c3c3c', fg='white').pack(pady=10)

        saturation_var = tk.DoubleVar(value=1.0)
        scale = tk.Scale(dialog, from_=0.0, to=3.0, resolution=0.1, orient='horizontal',
                        variable=saturation_var, bg='#3c3c3c', fg='white')
        scale.pack(fill='x', padx=20)

        def apply_saturation():
            enhancer = ImageEnhance.Color(self.current_layer.image)
            self.current_layer.image = enhancer.enhance(saturation_var.get())
            self.update_canvas_display()

        scale.configure(command=lambda v: apply_saturation())

        button_frame = tk.Frame(dialog, bg='#3c3c3c')
        button_frame.pack(pady=10)

        tk.Button(button_frame, text="تطبيق", command=lambda: [self.save_state("Adjust Saturation"), dialog.destroy()],
                 bg='#10b981', fg='white').pack(side='left', padx=5)
        tk.Button(button_frame, text="إلغاء", command=dialog.destroy,
                 bg='#ef4444', fg='white').pack(side='left', padx=5)

    # ==================== وظائف الملف ====================

    def new_project(self):
        """مشروع جديد"""
        # مسح الطبقات الحالية
        self.layers.clear()
        self.current_layer = None
        self.history.clear()
        self.history_index = -1

        # إنشاء طبقة افتراضية
        self.create_new_layer("Background")

        self.update_status("📄 مشروع جديد")

    def open_project(self):
        """فتح مشروع"""
        file_path = filedialog.askopenfilename(
            title="فتح مشروع",
            filetypes=[
                ("Project files", "*.json"),
                ("Image files", "*.png *.jpg *.jpeg"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                if file_path.endswith('.json'):
                    self.load_project(file_path)
                else:
                    self.load_image_as_layer(file_path)
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في فتح الملف: {e}")

    def save_project(self):
        """حفظ المشروع"""
        file_path = filedialog.asksaveasfilename(
            title="حفظ المشروع",
            defaultextension=".json",
            filetypes=[("Project files", "*.json"), ("All files", "*.*")]
        )

        if file_path:
            try:
                self.save_project_to_file(file_path)
                self.update_status(f"💾 تم حفظ المشروع: {os.path.basename(file_path)}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ المشروع: {e}")

    def save_as_project(self):
        """حفظ باسم"""
        self.save_project()

    def export_icon(self):
        """تصدير كأيقونة"""
        file_path = filedialog.asksaveasfilename(
            title="تصدير أيقونة",
            defaultextension=".ico",
            filetypes=[("Icon files", "*.ico"), ("PNG files", "*.png")]
        )

        if file_path:
            try:
                # دمج جميع الطبقات
                final_image = self.get_final_image()

                if file_path.endswith('.ico'):
                    # إنشاء أحجام متعددة للأيقونة
                    sizes = [16, 32, 48, 64, 128, 256]
                    images = []

                    for size in sizes:
                        resized = final_image.copy()
                        resized.thumbnail((size, size), Image.Resampling.LANCZOS)

                        # إنشاء صورة مربعة
                        square = Image.new('RGBA', (size, size), (0, 0, 0, 0))
                        x = (size - resized.width) // 2
                        y = (size - resized.height) // 2
                        square.paste(resized, (x, y), resized)

                        images.append(square)

                    # حفظ كملف ICO
                    images[0].save(file_path, format='ICO', sizes=[(img.width, img.height) for img in images])
                else:
                    # حفظ كـ PNG
                    final_image.save(file_path, format='PNG')

                self.update_status(f"🎯 تم تصدير الأيقونة: {os.path.basename(file_path)}")

                # إشعار التطبيق الرئيسي
                if self.callback:
                    self.callback(file_path)

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تصدير الأيقونة: {e}")

    def export_image(self):
        """تصدير كصورة"""
        file_path = filedialog.asksaveasfilename(
            title="تصدير صورة",
            defaultextension=".png",
            filetypes=[
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                final_image = self.get_final_image()
                final_image.save(file_path)
                self.update_status(f"🖼️ تم تصدير الصورة: {os.path.basename(file_path)}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تصدير الصورة: {e}")

    def get_final_image(self):
        """الحصول على الصورة النهائية المدمجة"""
        final_image = Image.new('RGBA', (self.canvas_size, self.canvas_size), (0, 0, 0, 0))

        for layer in self.layers:
            if layer.visible:
                layer_image = layer.image.copy()
                if layer.opacity < 1.0:
                    alpha = layer_image.split()[-1]
                    alpha = alpha.point(lambda p: int(p * layer.opacity))
                    layer_image.putalpha(alpha)

                final_image = Image.alpha_composite(final_image, layer_image)

        return final_image

    # ==================== وظائف مساعدة إضافية ====================

    def load_image_as_layer(self, file_path):
        """تحميل صورة كطبقة"""
        try:
            image = Image.open(file_path)

            # تحويل إلى RGBA
            if image.mode != 'RGBA':
                image = image.convert('RGBA')

            # تغيير الحجم إذا لزم الأمر
            if image.size != (self.canvas_size, self.canvas_size):
                image = image.resize((self.canvas_size, self.canvas_size), Image.Resampling.LANCZOS)

            # إنشاء طبقة جديدة
            layer_name = f"Image: {os.path.basename(file_path)}"
            self.create_new_layer(layer_name)

            if self.current_layer:
                self.current_layer.image = image
                self.update_canvas_display()
                self.save_state(f"Load Image: {layer_name}")

            self.update_status(f"📁 تم تحميل الصورة: {os.path.basename(file_path)}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الصورة: {e}")

    def save_project_to_file(self, file_path):
        """حفظ المشروع في ملف"""
        project_data = {
            'version': '1.0',
            'canvas_size': self.canvas_size,
            'layers': []
        }

        # حفظ بيانات الطبقات
        for layer in self.layers:
            # تحويل الصورة إلى base64
            buffer = io.BytesIO()
            layer.image.save(buffer, format='PNG')
            image_data = base64.b64encode(buffer.getvalue()).decode()

            layer_data = {
                'id': layer.id,
                'name': layer.name,
                'visible': layer.visible,
                'locked': layer.locked,
                'opacity': layer.opacity,
                'blend_mode': layer.blend_mode,
                'x': layer.x,
                'y': layer.y,
                'image_data': image_data
            }
            project_data['layers'].append(layer_data)

        # حفظ في ملف JSON
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(project_data, f, indent=2, ensure_ascii=False)

    def load_project(self, file_path):
        """تحميل مشروع من ملف"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                project_data = json.load(f)

            # مسح المشروع الحالي
            self.layers.clear()
            self.current_layer = None
            self.history.clear()
            self.history_index = -1

            # تحميل إعدادات المشروع
            self.canvas_size = project_data.get('canvas_size', 512)

            # تحميل الطبقات
            for layer_data in project_data['layers']:
                # تحويل الصورة من base64
                image_data = base64.b64decode(layer_data['image_data'])
                image = Image.open(io.BytesIO(image_data))

                # إنشاء الطبقة
                layer = Layer(
                    id=layer_data['id'],
                    name=layer_data['name'],
                    image=image,
                    visible=layer_data['visible'],
                    locked=layer_data['locked'],
                    opacity=layer_data['opacity'],
                    blend_mode=layer_data['blend_mode'],
                    x=layer_data.get('x', 0),
                    y=layer_data.get('y', 0)
                )

                self.layers.append(layer)

            # تحديد الطبقة الحالية
            if self.layers:
                self.current_layer = self.layers[0]

            self.update_layers_list()
            self.update_canvas_display()

            self.update_status(f"📁 تم تحميل المشروع: {os.path.basename(file_path)}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المشروع: {e}")

    def copy_layer(self):
        """نسخ الطبقة"""
        if self.current_layer:
            # حفظ بيانات الطبقة في الحافظة (مؤقتاً في متغير)
            self.clipboard_layer = {
                'image': self.current_layer.image.copy(),
                'name': self.current_layer.name,
                'opacity': self.current_layer.opacity,
                'blend_mode': self.current_layer.blend_mode
            }
            self.update_status("📋 تم نسخ الطبقة")

    def paste_layer(self):
        """لصق الطبقة"""
        if hasattr(self, 'clipboard_layer') and self.clipboard_layer:
            # إنشاء طبقة جديدة من البيانات المنسوخة
            new_layer = Layer(
                id=f"layer_{len(self.layers)}",
                name=f"{self.clipboard_layer['name']} Copy",
                image=self.clipboard_layer['image'].copy(),
                opacity=self.clipboard_layer['opacity'],
                blend_mode=self.clipboard_layer['blend_mode']
            )

            self.layers.append(new_layer)
            self.current_layer = new_layer

            self.update_layers_list()
            self.update_canvas_display()
            self.save_state("Paste Layer")

            self.update_status("📋 تم لصق الطبقة")
        else:
            messagebox.showwarning("تحذير", "لا توجد طبقة منسوخة")

    def merge_down(self):
        """دمج مع الطبقة السفلى"""
        if not self.current_layer or len(self.layers) < 2:
            messagebox.showwarning("تحذير", "يجب وجود طبقتين على الأقل للدمج")
            return

        current_index = self.layers.index(self.current_layer)
        if current_index == 0:
            messagebox.showwarning("تحذير", "لا يمكن دمج الطبقة السفلى")
            return

        # الطبقة السفلى
        lower_layer = self.layers[current_index - 1]

        # دمج الطبقة الحالية مع السفلى
        merged_image = Image.alpha_composite(lower_layer.image, self.current_layer.image)

        # تحديث الطبقة السفلى
        lower_layer.image = merged_image
        lower_layer.name = f"{lower_layer.name} + {self.current_layer.name}"

        # حذف الطبقة الحالية
        self.layers.remove(self.current_layer)
        self.current_layer = lower_layer

        self.update_layers_list()
        self.update_canvas_display()
        self.save_state("Merge Down")

        self.update_status("🔗 تم دمج الطبقات")

    def flatten_image(self):
        """تسطيح الصورة (دمج جميع الطبقات)"""
        if len(self.layers) < 2:
            messagebox.showwarning("تحذير", "لا توجد طبقات متعددة للتسطيح")
            return

        # دمج جميع الطبقات
        final_image = self.get_final_image()

        # مسح جميع الطبقات
        self.layers.clear()

        # إنشاء طبقة واحدة مسطحة
        flattened_layer = Layer(
            id="flattened",
            name="Flattened",
            image=final_image
        )

        self.layers.append(flattened_layer)
        self.current_layer = flattened_layer

        self.update_layers_list()
        self.update_canvas_display()
        self.save_state("Flatten Image")

        self.update_status("📄 تم تسطيح الصورة")

    def resize_canvas(self):
        """تغيير حجم اللوحة"""
        dialog = tk.Toplevel(self.editor_window)
        dialog.title("تغيير حجم اللوحة")
        dialog.geometry("300x200")
        dialog.configure(bg='#3c3c3c')

        tk.Label(dialog, text="الحجم الجديد:", bg='#3c3c3c', fg='white').pack(pady=10)

        size_var = tk.IntVar(value=self.canvas_size)
        size_entry = tk.Entry(dialog, textvariable=size_var, font=('Segoe UI', 12))
        size_entry.pack(pady=5)

        def apply_resize():
            new_size = size_var.get()
            if new_size > 0:
                self.canvas_size = new_size

                # تغيير حجم جميع الطبقات
                for layer in self.layers:
                    layer.image = layer.image.resize((new_size, new_size), Image.Resampling.LANCZOS)

                self.update_canvas_display()
                self.save_state(f"Resize Canvas: {new_size}x{new_size}")
                dialog.destroy()

        button_frame = tk.Frame(dialog, bg='#3c3c3c')
        button_frame.pack(pady=20)

        tk.Button(button_frame, text="تطبيق", command=apply_resize,
                 bg='#10b981', fg='white').pack(side='left', padx=5)
        tk.Button(button_frame, text="إلغاء", command=dialog.destroy,
                 bg='#ef4444', fg='white').pack(side='left', padx=5)

    def crop_canvas(self):
        """قص اللوحة"""
        messagebox.showinfo("قريباً", "ميزة القص ستكون متاحة قريباً")

    def goto_history_state(self, event):
        """الانتقال لحالة في التاريخ"""
        selection = self.history_listbox.curselection()
        if selection:
            target_index = selection[0]
            if 0 <= target_index < len(self.history):
                self.history_index = target_index
                self.restore_state(self.history[target_index])

    def end_stroke(self):
        """إنهاء الضربة"""
        # يمكن إضافة منطق إضافي هنا
        pass

    def start_pencil_stroke(self, x, y):
        """بدء ضربة قلم رصاص"""
        self.start_brush_stroke(x, y)  # نفس منطق الفرشاة

    def start_eraser_stroke(self, x, y):
        """بدء ضربة ممحاة"""
        if self.current_layer:
            self.save_state("Eraser Stroke")
            # رسم بلون شفاف
            draw = ImageDraw.Draw(self.current_layer.image)
            radius = self.brush_size // 2
            draw.ellipse([x-radius, y-radius, x+radius, y+radius], fill=(0, 0, 0, 0))
            self.update_canvas_display()

    def bucket_fill(self, x, y):
        """تعبئة بالدلو"""
        if self.current_layer:
            self.save_state("Bucket Fill")
            # تنفيذ بسيط لتعبئة الدلو
            # يمكن تحسينه لاحقاً
            self.update_status("🪣 تعبئة الدلو - قيد التطوير")

    def pick_color(self, x, y):
        """اختيار لون من الصورة"""
        if self.current_layer:
            try:
                # الحصول على اللون من الموقع
                color = self.current_layer.image.getpixel((int(x), int(y)))
                if len(color) >= 3:
                    # تحويل إلى hex
                    hex_color = f"#{color[0]:02x}{color[1]:02x}{color[2]:02x}"
                    self.current_color = hex_color
                    self.color_display.configure(bg=hex_color)
                    self.update_status(f"🎨 تم اختيار اللون: {hex_color}")
            except:
                self.update_status("❌ فشل في اختيار اللون")

# ==================== دالة التشغيل ====================

def open_professional_icon_editor(parent, analysis=None, callback=None):
    """فتح محرر الأيقونات الاحترافي"""
    return ProfessionalIconEditor(parent, analysis, callback)

    # ==================== وظائف الذكاء الاصطناعي ====================

    def load_ai_suggestions(self):
        """تحميل اقتراحات الذكاء الاصطناعي"""
        if self.analysis:
            threading.Thread(target=self._load_ai_suggestions_thread, daemon=True).start()

    def _load_ai_suggestions_thread(self):
        """تحميل الاقتراحات في thread منفصل"""
        try:
            self.ai_suggestions = self.ai_generator.generate_images_for_analysis(self.analysis)
            self.editor_window.after(0, self.update_ai_suggestions_list)
        except Exception as e:
            self.editor_window.after(0, lambda: self.update_status(f"خطأ في تحميل الاقتراحات: {e}"))

    def generate_ai_suggestions(self):
        """توليد اقتراحات جديدة"""
        if not self.analysis:
            messagebox.showwarning("تحذير", "لا يوجد تحليل للكود متاح")
            return

        self.update_status("🧠 توليد اقتراحات الذكاء الاصطناعي...")
        threading.Thread(target=self._generate_ai_suggestions_thread, daemon=True).start()

    def _generate_ai_suggestions_thread(self):
        """توليد الاقتراحات في thread منفصل"""
        try:
            new_suggestions = self.ai_generator.generate_images_for_analysis(self.analysis, count=8)
            self.ai_suggestions.extend(new_suggestions)
            self.editor_window.after(0, self.update_ai_suggestions_list)
            self.editor_window.after(0, lambda: self.update_status("✅ تم توليد اقتراحات جديدة"))
        except Exception as e:
            self.editor_window.after(0, lambda: self.update_status(f"❌ خطأ في التوليد: {e}"))

    def update_ai_suggestions_list(self):
        """تحديث قائمة اقتراحات الذكاء الاصطناعي"""
        self.ai_listbox.delete(0, tk.END)

        for i, suggestion in enumerate(self.ai_suggestions):
            confidence_stars = "⭐" * int(suggestion.confidence * 5)
            style_icon = self.get_style_icon(suggestion.prompt.style)
            display_text = f"{style_icon} {suggestion.prompt.style} {confidence_stars}"
            self.ai_listbox.insert(tk.END, display_text)

    def get_style_icon(self, style):
        """الحصول على أيقونة النمط"""
        style_icons = {
            "minimalist": "📱",
            "3D": "🎲",
            "professional": "💼",
            "vibrant": "🌈",
            "elegant": "✨",
            "cartoon": "🎨"
        }

        for key, icon in style_icons.items():
            if key in style.lower():
                return icon
        return "🖼️"

    def apply_ai_suggestion(self, event=None):
        """تطبيق اقتراح الذكاء الاصطناعي"""
        selection = self.ai_listbox.curselection()
        if not selection or not self.ai_suggestions:
            messagebox.showwarning("تحذير", "يرجى اختيار اقتراح أولاً")
            return

        index = selection[0]
        if index >= len(self.ai_suggestions):
            return

        suggestion = self.ai_suggestions[index]

        # إنشاء طبقة جديدة مع الصورة المقترحة
        layer_name = f"AI: {suggestion.prompt.style}"
        self.create_new_layer(layer_name)

        # نسخ الصورة المقترحة إلى الطبقة
        if self.current_layer:
            # تغيير حجم الصورة لتناسب اللوحة
            resized_image = suggestion.image.copy()
            if resized_image.size != (self.canvas_size, self.canvas_size):
                resized_image = resized_image.resize((self.canvas_size, self.canvas_size), Image.Resampling.LANCZOS)

            self.current_layer.image = resized_image
            self.update_canvas_display()
            self.save_state(f"Apply AI Suggestion: {suggestion.prompt.style}")

            self.update_status(f"✅ تم تطبيق اقتراح: {suggestion.prompt.style}")

    def enhance_with_ai(self):
        """تحسين الصورة بالذكاء الاصطناعي"""
        if not self.current_layer:
            messagebox.showwarning("تحذير", "لا توجد طبقة محددة")
            return

        self.update_status("✨ تحسين الصورة بالذكاء الاصطناعي...")

        # تطبيق تحسينات متقدمة
        enhanced_image = self.current_layer.image.copy()

        # تحسين الحدة
        enhanced_image = enhanced_image.filter(ImageFilter.UnsharpMask(radius=1, percent=150, threshold=3))

        # تحسين التباين
        enhancer = ImageEnhance.Contrast(enhanced_image)
        enhanced_image = enhancer.enhance(1.2)

        # تحسين الألوان
        enhancer = ImageEnhance.Color(enhanced_image)
        enhanced_image = enhancer.enhance(1.1)

        # تطبيق التحسين
        self.current_layer.image = enhanced_image
        self.update_canvas_display()
        self.save_state("AI Enhancement")

        self.update_status("✅ تم تحسين الصورة")

    def remove_background_ai(self):
        """إزالة الخلفية بالذكاء الاصطناعي"""
        if not self.current_layer:
            messagebox.showwarning("تحذير", "لا توجد طبقة محددة")
            return

        self.update_status("🎯 إزالة الخلفية بالذكاء الاصطناعي...")

        try:
            # خوارزمية بسيطة لإزالة الخلفية
            image = self.current_layer.image.copy()

            # تحويل إلى RGB للمعالجة
            if image.mode == 'RGBA':
                rgb_image = Image.new('RGB', image.size, (255, 255, 255))
                rgb_image.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
            else:
                rgb_image = image.convert('RGB')

            # اكتشاف لون الخلفية من الزوايا
            width, height = rgb_image.size
            corner_colors = [
                rgb_image.getpixel((0, 0)),
                rgb_image.getpixel((width-1, 0)),
                rgb_image.getpixel((0, height-1)),
                rgb_image.getpixel((width-1, height-1))
            ]

            # حساب متوسط لون الخلفية
            avg_color = tuple(sum(colors) // len(colors) for colors in zip(*corner_colors))

            # إنشاء قناع للخلفية
            mask = Image.new('L', image.size, 0)

            for x in range(width):
                for y in range(height):
                    pixel = rgb_image.getpixel((x, y))
                    # حساب المسافة اللونية
                    distance = sum(abs(a - b) for a, b in zip(pixel, avg_color))

                    # إذا كان اللون قريب من لون الخلفية
                    if distance < 50:  # عتبة التشابه
                        mask.putpixel((x, y), 0)  # شفاف
                    else:
                        mask.putpixel((x, y), 255)  # معتم

            # تطبيق القناع
            result = Image.new('RGBA', image.size, (0, 0, 0, 0))
            result.paste(image, mask=mask)

            self.current_layer.image = result
            self.update_canvas_display()
            self.save_state("Remove Background")

            self.update_status("✅ تم إزالة الخلفية")

        except Exception as e:
            self.update_status(f"❌ خطأ في إزالة الخلفية: {e}")

    # ==================== وظائف الفلاتر ====================

    def apply_filter(self, filter_type):
        """تطبيق فلتر"""
        if not self.current_layer:
            messagebox.showwarning("تحذير", "لا توجد طبقة محددة")
            return

        self.save_state(f"Apply Filter: {filter_type}")

        image = self.current_layer.image.copy()

        if filter_type == "blur":
            image = image.filter(ImageFilter.GaussianBlur(radius=2))
        elif filter_type == "sharpen":
            image = image.filter(ImageFilter.SHARPEN)
        elif filter_type == "emboss":
            image = image.filter(ImageFilter.EMBOSS)
        elif filter_type == "edge":
            image = image.filter(ImageFilter.FIND_EDGES)
        elif filter_type == "smooth":
            image = image.filter(ImageFilter.SMOOTH)
        elif filter_type == "detail":
            image = image.filter(ImageFilter.DETAIL)

        self.current_layer.image = image
        self.update_canvas_display()
        self.update_status(f"✅ تم تطبيق فلتر: {filter_type}")

    def adjust_brightness(self):
        """ضبط السطوع"""
        if not self.current_layer:
            return

        # نافذة ضبط السطوع
        dialog = tk.Toplevel(self.editor_window)
        dialog.title("ضبط السطوع")
        dialog.geometry("300x150")
        dialog.configure(bg='#3c3c3c')

        tk.Label(dialog, text="السطوع:", bg='#3c3c3c', fg='white').pack(pady=10)

        brightness_var = tk.DoubleVar(value=1.0)
        scale = tk.Scale(dialog, from_=0.1, to=3.0, resolution=0.1, orient='horizontal',
                        variable=brightness_var, bg='#3c3c3c', fg='white')
        scale.pack(fill='x', padx=20)

        def apply_brightness():
            enhancer = ImageEnhance.Brightness(self.current_layer.image)
            self.current_layer.image = enhancer.enhance(brightness_var.get())
            self.update_canvas_display()

        def preview_brightness(value):
            apply_brightness()

        scale.configure(command=preview_brightness)

        button_frame = tk.Frame(dialog, bg='#3c3c3c')
        button_frame.pack(pady=10)

        tk.Button(button_frame, text="تطبيق", command=lambda: [self.save_state("Adjust Brightness"), dialog.destroy()],
                 bg='#10b981', fg='white').pack(side='left', padx=5)
        tk.Button(button_frame, text="إلغاء", command=dialog.destroy,
                 bg='#ef4444', fg='white').pack(side='left', padx=5)

    def adjust_contrast(self):
        """ضبط التباين"""
        if not self.current_layer:
            return

        dialog = tk.Toplevel(self.editor_window)
        dialog.title("ضبط التباين")
        dialog.geometry("300x150")
        dialog.configure(bg='#3c3c3c')

        tk.Label(dialog, text="التباين:", bg='#3c3c3c', fg='white').pack(pady=10)

        contrast_var = tk.DoubleVar(value=1.0)
        scale = tk.Scale(dialog, from_=0.1, to=3.0, resolution=0.1, orient='horizontal',
                        variable=contrast_var, bg='#3c3c3c', fg='white')
        scale.pack(fill='x', padx=20)

        def apply_contrast():
            enhancer = ImageEnhance.Contrast(self.current_layer.image)
            self.current_layer.image = enhancer.enhance(contrast_var.get())
            self.update_canvas_display()

        scale.configure(command=lambda v: apply_contrast())

        button_frame = tk.Frame(dialog, bg='#3c3c3c')
        button_frame.pack(pady=10)

        tk.Button(button_frame, text="تطبيق", command=lambda: [self.save_state("Adjust Contrast"), dialog.destroy()],
                 bg='#10b981', fg='white').pack(side='left', padx=5)
        tk.Button(button_frame, text="إلغاء", command=dialog.destroy,
                 bg='#ef4444', fg='white').pack(side='left', padx=5)

    def adjust_saturation(self):
        """ضبط التشبع"""
        if not self.current_layer:
            return

        dialog = tk.Toplevel(self.editor_window)
        dialog.title("ضبط التشبع")
        dialog.geometry("300x150")
        dialog.configure(bg='#3c3c3c')

        tk.Label(dialog, text="التشبع:", bg='#3c3c3c', fg='white').pack(pady=10)

        saturation_var = tk.DoubleVar(value=1.0)
        scale = tk.Scale(dialog, from_=0.0, to=3.0, resolution=0.1, orient='horizontal',
                        variable=saturation_var, bg='#3c3c3c', fg='white')
        scale.pack(fill='x', padx=20)

        def apply_saturation():
            enhancer = ImageEnhance.Color(self.current_layer.image)
            self.current_layer.image = enhancer.enhance(saturation_var.get())
            self.update_canvas_display()

        scale.configure(command=lambda v: apply_saturation())

        button_frame = tk.Frame(dialog, bg='#3c3c3c')
        button_frame.pack(pady=10)

        tk.Button(button_frame, text="تطبيق", command=lambda: [self.save_state("Adjust Saturation"), dialog.destroy()],
                 bg='#10b981', fg='white').pack(side='left', padx=5)
        tk.Button(button_frame, text="إلغاء", command=dialog.destroy,
                 bg='#ef4444', fg='white').pack(side='left', padx=5)

    # ==================== وظائف الملف ====================

    def new_project(self):
        """مشروع جديد"""
        # مسح الطبقات الحالية
        self.layers.clear()
        self.current_layer = None
        self.history.clear()
        self.history_index = -1

        # إنشاء طبقة افتراضية
        self.create_new_layer("Background")

        self.update_status("📄 مشروع جديد")

    def open_project(self):
        """فتح مشروع"""
        file_path = filedialog.askopenfilename(
            title="فتح مشروع",
            filetypes=[
                ("Project files", "*.json"),
                ("Image files", "*.png *.jpg *.jpeg"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                if file_path.endswith('.json'):
                    self.load_project(file_path)
                else:
                    self.load_image_as_layer(file_path)
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في فتح الملف: {e}")

    def save_project(self):
        """حفظ المشروع"""
        file_path = filedialog.asksaveasfilename(
            title="حفظ المشروع",
            defaultextension=".json",
            filetypes=[("Project files", "*.json"), ("All files", "*.*")]
        )

        if file_path:
            try:
                self.save_project_to_file(file_path)
                self.update_status(f"💾 تم حفظ المشروع: {os.path.basename(file_path)}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ المشروع: {e}")

    def export_icon(self):
        """تصدير كأيقونة"""
        file_path = filedialog.asksaveasfilename(
            title="تصدير أيقونة",
            defaultextension=".ico",
            filetypes=[("Icon files", "*.ico"), ("PNG files", "*.png")]
        )

        if file_path:
            try:
                # دمج جميع الطبقات
                final_image = self.get_final_image()

                if file_path.endswith('.ico'):
                    # إنشاء أحجام متعددة للأيقونة
                    sizes = [16, 32, 48, 64, 128, 256]
                    images = []

                    for size in sizes:
                        resized = final_image.copy()
                        resized.thumbnail((size, size), Image.Resampling.LANCZOS)

                        # إنشاء صورة مربعة
                        square = Image.new('RGBA', (size, size), (0, 0, 0, 0))
                        x = (size - resized.width) // 2
                        y = (size - resized.height) // 2
                        square.paste(resized, (x, y), resized)

                        images.append(square)

                    # حفظ كملف ICO
                    images[0].save(file_path, format='ICO', sizes=[(img.width, img.height) for img in images])
                else:
                    # حفظ كـ PNG
                    final_image.save(file_path, format='PNG')

                self.update_status(f"🎯 تم تصدير الأيقونة: {os.path.basename(file_path)}")

                # إشعار التطبيق الرئيسي
                if self.callback:
                    self.callback(file_path)

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تصدير الأيقونة: {e}")

    def get_final_image(self):
        """الحصول على الصورة النهائية المدمجة"""
        final_image = Image.new('RGBA', (self.canvas_size, self.canvas_size), (0, 0, 0, 0))

        for layer in self.layers:
            if layer.visible:
                layer_image = layer.image.copy()
                if layer.opacity < 1.0:
                    alpha = layer_image.split()[-1]
                    alpha = alpha.point(lambda p: int(p * layer.opacity))
                    layer_image.putalpha(alpha)

                final_image = Image.alpha_composite(final_image, layer_image)

        return final_image
