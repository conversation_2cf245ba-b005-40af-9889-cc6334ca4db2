#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python to EXE Converter Pro v3.0 - UI Module
وحدة واجهة المستخدم

نظام واجهة مستخدم متقدم يدعم:
- أطر عمل متعددة (Tkinter, PyQt, Web)
- ثيمات قابلة للتخصيص
- مكونات قابلة لإعادة الاستخدام
- تجربة مستخدم محسنة
"""

from enum import Enum
from typing import Optional, Dict, Any, Callable
from abc import ABC, abstractmethod

class UIFramework(Enum):
    """أطر عمل الواجهة المدعومة"""
    TKINTER = "tkinter"
    PYQT = "pyqt"
    WEB = "web"

class Theme(Enum):
    """الثيمات المتاحة"""
    LIGHT = "light"
    DARK = "dark"
    AUTO = "auto"
    GLASS = "glass"
    NEON = "neon"

class UIComponent(ABC):
    """فئة أساسية لمكونات الواجهة"""
    
    def __init__(self, name: str, parent=None):
        self.name = name
        self.parent = parent
        self.children = []
        self.properties = {}
        self.event_handlers = {}
    
    @abstractmethod
    def create(self):
        """إنشاء المكون"""
        pass
    
    @abstractmethod
    def update(self, **kwargs):
        """تحديث المكون"""
        pass
    
    @abstractmethod
    def destroy(self):
        """تدمير المكون"""
        pass
    
    def add_child(self, child):
        """إضافة مكون فرعي"""
        self.children.append(child)
        child.parent = self
    
    def remove_child(self, child):
        """إزالة مكون فرعي"""
        if child in self.children:
            self.children.remove(child)
            child.parent = None
    
    def set_property(self, key: str, value: Any):
        """تعيين خاصية"""
        self.properties[key] = value
    
    def get_property(self, key: str, default: Any = None):
        """الحصول على خاصية"""
        return self.properties.get(key, default)
    
    def bind_event(self, event: str, handler: Callable):
        """ربط معالج حدث"""
        self.event_handlers[event] = handler
    
    def trigger_event(self, event: str, *args, **kwargs):
        """تشغيل حدث"""
        if event in self.event_handlers:
            self.event_handlers[event](*args, **kwargs)

class UIManager(ABC):
    """مدير واجهة المستخدم الأساسي"""
    
    def __init__(self, framework: UIFramework, theme: Theme = Theme.DARK):
        self.framework = framework
        self.theme = theme
        self.components = {}
        self.main_window = None
        self.is_initialized = False
    
    @abstractmethod
    def initialize(self):
        """تهيئة النظام"""
        pass
    
    @abstractmethod
    def create_main_window(self, title: str, width: int, height: int):
        """إنشاء النافذة الرئيسية"""
        pass
    
    @abstractmethod
    def run(self):
        """تشغيل حلقة الأحداث"""
        pass
    
    @abstractmethod
    def shutdown(self):
        """إغلاق النظام"""
        pass
    
    def register_component(self, name: str, component: UIComponent):
        """تسجيل مكون"""
        self.components[name] = component
    
    def get_component(self, name: str) -> Optional[UIComponent]:
        """الحصول على مكون"""
        return self.components.get(name)
    
    def set_theme(self, theme: Theme):
        """تغيير الثيم"""
        self.theme = theme
        # إعادة تطبيق الثيم على جميع المكونات
        for component in self.components.values():
            if hasattr(component, 'apply_theme'):
                component.apply_theme(theme)

# Factory لإنشاء مدير الواجهة المناسب
def create_ui_manager(framework: UIFramework, theme: Theme = Theme.DARK) -> UIManager:
    """إنشاء مدير واجهة مستخدم"""
    if framework == UIFramework.TKINTER:
        from .frameworks.tkinter_ui import TkinterUIManager
        return TkinterUIManager(theme)
    elif framework == UIFramework.PYQT:
        from .frameworks.qt_ui import QtUIManager
        return QtUIManager(theme)
    elif framework == UIFramework.WEB:
        from .frameworks.web_ui import WebUIManager
        return WebUIManager(theme)
    else:
        raise ValueError(f"إطار عمل غير مدعوم: {framework}")

# تصدير المكونات الرئيسية
__all__ = [
    'UIFramework',
    'Theme', 
    'UIComponent',
    'UIManager',
    'create_ui_manager'
]
