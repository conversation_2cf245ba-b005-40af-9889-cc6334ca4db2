#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
استوديو الصور الذكي (AI Image Studio)
توليد، تحرير، وتحويل الصور والأيقونات باستخدام الذكاء الاصطناعي
"""

import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, colorchooser
from PIL import Image, ImageDraw, ImageFilter, ImageEnhance, ImageOps
from PIL.ImageTk import PhotoImage
import io
import threading

# --- تكامل الذكاء الاصطناعي ---
try:
    from rembg import remove
    HAS_REMBG = True
except ImportError:
    HAS_REMBG = False

try:
    from ai_image_generator import AIImageGenerator
    HAS_AI_GENERATOR = True
except ImportError:
    HAS_AI_GENERATOR = False

class AIImageStudio:
    """استوديو متكامل قائم على الذكاء الاصطناعي للصور والأيقونات"""

    def __init__(self, parent, callback=None):
        self.parent = parent
        self.callback = callback
        self.original_image = None
        self.processed_image = None

        # مكونات الذكاء الاصطناعي
        self.ai_generator = AIImageGenerator(callback=print) if HAS_AI_GENERATOR else None

        self.setup_studio()

    def setup_studio(self):
        """إعداد واجهة الاستوديو الذكي"""
        self.studio_frame = tk.Frame(self.parent, bg='#1a1a2e')
        self.studio_frame.pack(fill='both', expand=True, padx=10, pady=10)

        title_label = tk.Label(self.studio_frame, text="🎨 استوديو الصور الذكي (AI Image Studio)", font=('Segoe UI', 16, 'bold'), fg='#ffffff', bg='#1a1a2e')
        title_label.pack(pady=(0, 15))

        main_layout = tk.Frame(self.studio_frame, bg='#1a1a2e')
        main_layout.pack(fill='both', expand=True)

        # --- اللوحة اليسرى: أدوات الذكاء الاصطناعي والتحرير ---
        self.create_tools_panel(main_layout)

        # --- اللوحة الوسطى: منطقة العرض الرئيسية ---
        self.create_canvas_panel(main_layout)

        # --- اللوحة اليمنى: المعاينة والتصدير ---
        self.create_export_panel(main_layout)

    def create_tools_panel(self, parent):
        """إنشاء لوحة الأدوات (يسار)"""
        tools_panel = tk.LabelFrame(parent, text="🛠️ صندوق الأدوات الذكي", font=('Segoe UI', 11, 'bold'), fg='#ffffff', bg='#16213e', bd=2, relief='solid')
        tools_panel.pack(side='left', fill='y', padx=(0, 10))

        # --- قسم توليد الصور بالذكاء الاصطناعي ---
        ai_gen_frame = tk.LabelFrame(tools_panel, text="🧠 توليد صورة من نص", font=('Segoe UI', 9), fg='#ffffff', bg='#16213e')
        ai_gen_frame.pack(fill='x', padx=10, pady=10)

        self.prompt_entry = tk.Entry(ai_gen_frame, font=('Segoe UI', 9), bg='#2a2a3e', fg='#ffffff', relief='flat', insertbackground='white')
        self.prompt_entry.insert(0, "مثال: أيقونة زجاجية لتطبيق طقس")
        self.prompt_entry.pack(fill='x', padx=5, pady=5)

        generate_btn = tk.Button(ai_gen_frame, text="🎨 توليد!", command=self.generate_from_prompt, font=('Segoe UI', 9, 'bold'), fg='#ffffff', bg='#ec4899', activebackground='#f472b6', relief='flat', bd=0, pady=5)
        generate_btn.pack(fill='x', padx=5, pady=5)

        # --- قسم التحسين الذكي ---
        ai_enhance_frame = tk.LabelFrame(tools_panel, text="✨ التحسين الذكي", font=('Segoe UI', 9), fg='#ffffff', bg='#16213e')
        ai_enhance_frame.pack(fill='x', padx=10, pady=10)

        tools = [
            ("🎯 إزالة الخلفية بذكاء", self.ai_remove_background, '#06b6d4'),
            ("🌟 تحسين الجودة بذكاء", self.ai_enhance_quality, '#06b6d4'),
            ("📐 قص ذكي للمحتوى", self.ai_smart_crop, '#06b6d4')
        ]
        for name, command, color in tools:
            btn = tk.Button(ai_enhance_frame, text=name, command=command, font=('Segoe UI', 8), fg='#ffffff', bg=color, activebackground=self.lighten_color(color), relief='flat', bd=0, pady=4)
            btn.pack(pady=2, padx=5, fill='x')

        # --- قسم التأثيرات الفنية ---
        effects_frame = tk.LabelFrame(tools_panel, text="🎨 تأثيرات فنية", font=('Segoe UI', 9), fg='#ffffff', bg='#16213e')
        effects_frame.pack(fill='x', padx=10, pady=10)
        
        effects = [
            ("💎 تأثير زجاجي", self.add_glass_effect, '#8b5cf6')
        ]
        for name, command, color in effects:
            btn = tk.Button(effects_frame, text=name, command=command, font=('Segoe UI', 8), fg='#ffffff', bg=color, activebackground=self.lighten_color(color), relief='flat', bd=0, pady=4)
            btn.pack(pady=2, padx=5, fill='x')

        effects_frames = [
            ("⚡ تأثير نيون", self.add_neon_effect, '#8b5cf6'),
            ("🎨 تأثير كرتوني", self.add_cartoon_effect, '#f97316'),
            ("🖌️ تأثير زيتي", self.add_oil_painting_effect, '#f97316')
        ]
        for name, command, color in effects:
            btn = tk.Button(effects_frame, text=name, command=command, font=('Segoe UI', 8), fg='#ffffff', bg=color, activebackground=self.lighten_color(color), relief='flat', bd=0, pady=4)
            btn.pack(pady=2, padx=5, fill='x')
            
            if name == "⚡ تأثير نيون":
                tk.Label(effects_frame, text="   توهج:", font=('Segoe UI', 8), fg='#ffffff', bg='#16213e').pack(anchor=tk.W, padx=5)
                self.neon_glow_var = tk.IntVar(value=5)
                tk.Scale(effects_frame, from_=1, to=15, resolution=1, orient='horizontal', variable=self.neon_glow_var, bg='#16213e', fg='#ffffff', highlightthickness=0, command=self.update_neon_effect).pack(fill='x', padx=5)

        if name == "🖌️ تأثير زيتي":
            tk.Label(effects_frame, text="   حجم:", font=('Segoe UI', 8), fg='#ffffff', bg='#16213e').pack(anchor=tk.W, padx=5)
            self.oil_size_var = tk.IntVar(value=7)
            tk.Scale(effects_frame, from_=3, to=15, resolution=2, orient='horizontal', variable=self.oil_size_var, bg='#16213e', fg='#ffffff', highlightthickness=0, command=self.update_oil_effect).pack(fill='x', padx=5)


    def update_oil_effect(self, value=None):
        """تحديث تأثير الرسم الزيتي"""
        if not self.processed_image: return
        try:
            # تطبيق الفلتر عدة مرات لزيادة التأثير
            self.processed_image = self.processed_image.filter(ImageFilter.MedianFilter(size=self.oil_size_var.get()))
            self.update_previews()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إضافة تأثير الرسم الزيتي: {e}")

    def update_neon_effect(self, value=None):
        """تحديث تأثير النيون"""
        if not self.processed_image: return
        try:
            # تطبيق الفلتر بعد تحديث القيمة
            self.add_neon_effect(glow_radius=self.neon_glow_var.get())
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إضافة تأثير النيون: {e}")
    
     # --- قسم الضبط اليدوي ---
        adjust_frame = tk.LabelFrame(tools_panel, text="⚙️ ضبط يدوي", font=('Segoe UI', 9), fg='#ffffff', bg='#16213e')
        adjust_frame.pack(fill='x', padx=10, pady=10)

        self.brightness_var = tk.DoubleVar(value=1.0)
        tk.Label(adjust_frame, text="☀️ السطوع:", font=('Segoe UI', 8), fg='#ffffff', bg='#16213e').pack(anchor='w', padx=5)
        tk.Scale(adjust_frame, from_=0.5, to=2.0, resolution=0.1, orient='horizontal', variable=self.brightness_var, bg='#16213e', fg='#ffffff', highlightthickness=0, command=self.apply_adjustments).pack(fill='x', padx=5)

        # --- أزرار الإجراءات ---
        actions_frame = tk.Frame(tools_panel, bg='#16213e')
        actions_frame.pack(fill='x', padx=10, pady=20, side='bottom')

        reset_btn = tk.Button(actions_frame, text="🔄 إعادة تعيين", command=self.reset_image, font=('Segoe UI', 9), fg='#ffffff', bg='#ef4444', activebackground='#f87171', relief='flat', bd=0, pady=5)
        reset_btn.pack(fill='x', pady=2)

    def create_canvas_panel(self, parent):
        """إنشاء منطقة العرض (وسط)"""
        canvas_panel = tk.LabelFrame(parent, text="🖼️ منطقة العمل", font=('Segoe UI', 11, 'bold'), fg='#ffffff', bg='#16213e', bd=2, relief='solid')
        canvas_panel.pack(side='left', fill='both', expand=True, padx=(0, 10))

        # منطقة السحب والإفلات
        self.drop_area = tk.Frame(canvas_panel, bg='#2a2a3e', relief='solid', bd=2)
        self.drop_area.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.drop_label = tk.Label(self.drop_area, text="🖼️\n\nاسحب صورة هنا أو انقر للاختيار\nأو قم بتوليد صورة من النص", font=('Segoe UI', 12), fg='#888888', bg='#2a2a3e', justify='center')
        self.drop_label.pack(expand=True)
        
        self.drop_area.bind("<Button-1>", self.browse_image)
        self.drop_label.bind("<Button-1>", self.browse_image)

    def create_export_panel(self, parent):
        """إنشاء لوحة المعاينة والتصدير (يمين)"""
        export_panel = tk.LabelFrame(parent, text="🎯 المعاينة والتصدير", font=('Segoe UI', 11, 'bold'), fg='#ffffff', bg='#16213e', bd=2, relief='solid')
        export_panel.pack(side='right', fill='y')

        # معاينة الأيقونة النهائية
        icon_frame = tk.LabelFrame(export_panel, text="🎯 معاينة الأيقونة", font=('Segoe UI', 9), fg='#ffffff', bg='#16213e')
        icon_frame.pack(fill='x', expand=True, padx=10, pady=10)

        sizes_label = tk.Label(icon_frame, text="📏 أحجام مختلفة:", font=('Segoe UI', 9), fg='#ffffff', bg='#16213e')
        sizes_label.pack()

        self.icon_previews_frame = tk.Frame(icon_frame, bg='#16213e')
        self.icon_previews_frame.pack(pady=10)

        self.icon_info_label = tk.Label(icon_frame, text="ℹ️ قم بتحميل صورة لبدء التحويل", font=('Segoe UI', 8), fg='#888888', bg='#16213e', wraplength=200)
        self.icon_info_label.pack(pady=10)

        # زر التحويل إلى أيقونة
        convert_btn = tk.Button(export_panel, text="🔄 تحويل إلى أيقونة", command=self.convert_to_icon, font=('Segoe UI', 10, 'bold'), fg='#ffffff', bg='#10b981', activebackground='#34d399', relief='flat', bd=0, pady=8)
        convert_btn.pack(fill='x', padx=10, pady=10)

        # قسم الحفظ
        save_frame = tk.LabelFrame(export_panel, text="💾 حفظ", font=('Segoe UI', 9), fg='#ffffff', bg='#16213e')
        save_frame.pack(fill='x', padx=10, pady=10)

        self.save_icon_btn = tk.Button(save_frame, text="حفظ كأيقونة (.ico)", command=lambda: self.export_image('ico'), state='disabled', font=('Segoe UI', 9), fg='#ffffff', bg='#f59e0b', activebackground='#fbbf24', relief='flat', bd=0, pady=5)
        self.save_icon_btn.pack(fill='x', pady=2, padx=5)

        self.save_png_btn = tk.Button(save_frame, text="حفظ كصورة (.png)", command=lambda: self.export_image('png'), state='disabled', font=('Segoe UI', 9), fg='#ffffff', bg='#f59e0b', activebackground='#fbbf24', relief='flat', bd=0, pady=5)
        self.save_png_btn.pack(fill='x', pady=2, padx=5)

    # --- وظائف الذكاء الاصطناعي ---

    def generate_from_prompt(self):
        """توليد صورة من وصف نصي باستخدام الذكاء الاصطناعي"""
        if not HAS_AI_GENERATOR or not self.ai_generator:
            messagebox.showerror("خطأ", "مكون توليد الصور بالذكاء الاصطناعي غير متوفر.")
            return

        prompt = self.prompt_entry.get()
        if not prompt or prompt.startswith("مثال:"):
            messagebox.showwarning("تحذير", "يرجى كتابة وصف للصورة المطلوبة.")
            return

        self.drop_label.configure(text="🧠\n\nجاري التوليد...\nقد يستغرق الأمر بعض الوقت")
        self.parent.update_idletasks()

        def run_generation():
            try:
                # استخدام دالة توليد متقدمة من ai_image_generator
                generated_image = self.ai_generator.generate_image_from_text(prompt, style='minimal')
                
                if generated_image:
                    self.parent.after(0, lambda: self.set_image(generated_image, f"مولدة من: {prompt}"))
                else:
                    self.parent.after(0, lambda: messagebox.showerror("خطأ", "فشل توليد الصورة. حاول مرة أخرى."))
            except Exception as e:
                self.parent.after(0, lambda: messagebox.showerror("خطأ", f"حدث خطأ أثناء التوليد: {e}"))
            finally:
                self.parent.after(0, lambda: self.drop_label.configure(text="✅\n\nتم التوليد بنجاح!"))

        threading.Thread(target=run_generation, daemon=True).start()

    def ai_remove_background(self):
        """إزالة الخلفية باستخدام نموذج AI (rembg)"""
        if not self.processed_image:
            return
        if not HAS_REMBG:
            messagebox.showerror("خطأ", "مكتبة 'rembg' لإزالة الخلفية غير مثبتة.\nقم بتثبيتها بـ: pip install rembg")
            return

        try:
            # تحويل الصورة إلى bytes للمعالجة
            img_byte_arr = io.BytesIO()
            self.processed_image.save(img_byte_arr, format='PNG')
            img_byte_arr = img_byte_arr.getvalue()

            # إزالة الخلفية
            result_bytes = remove(img_byte_arr)

            # تحديث الصورة
            self.processed_image = Image.open(io.BytesIO(result_bytes))
            self.update_previews()
            messagebox.showinfo("نجاح", "تمت إزالة الخلفية بنجاح.")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إزالة الخلفية: {e}")

    def ai_enhance_quality(self):
        """تحسين جودة الصورة بذكاء"""
        if not self.processed_image:
            return

        try:
            # 1. تحسين الحدة
            enhanced = self.processed_image.filter(ImageFilter.UnsharpMask(radius=1.5, percent=150, threshold=3))
            # 2. تحسين التباين
            enhancer = ImageEnhance.Contrast(enhanced)
            enhanced = enhancer.enhance(1.15)
            # 3. تحسين الألوان
            enhancer = ImageEnhance.Color(enhanced)
            enhanced = enhancer.enhance(1.1)

            self.processed_image = enhanced
            self.update_previews()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحسين الجودة: {e}")

    def ai_smart_crop(self):
        """قص ذكي للمحتوى غير الشفاف"""
        if not self.processed_image:
            return
        try:
            bbox = self.processed_image.getbbox()
            if bbox:
                self.processed_image = self.processed_image.crop(bbox)
                self.update_previews()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في القص الذكي: {e}")

    # --- وظائف مساعدة ---

    def browse_image(self, event=None):
        """تصفح واختيار صورة"""
        file_path = filedialog.askopenfilename(filetypes=[("Image files", "*.png *.jpg *.jpeg *.bmp *.gif"), ("All files", "*.*")])
        if file_path:
            self.load_image(file_path)
    
    def load_image(self, file_path):
        """تحميل صورة من ملف"""
        try:
            image = Image.open(file_path).convert('RGBA')
            self.set_image(image, os.path.basename(file_path))
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الصورة: {e}")

    def set_image(self, image, name):
        """تحديد صورة جديدة للعمل عليها"""
        self.original_image = image
        self.processed_image = image.copy()
        self.update_previews()
        self.drop_label.configure(text=f"✅ تم تحميل:\n{name}")
        self.reset_adjustments()
        self.save_png_btn.config(state='normal')

    def update_previews(self):
        """تحديث جميع المعاينات"""
        if not self.processed_image:
            return

        # تحديث لوحة العرض الرئيسية
        self.display_image_on_canvas(self.processed_image)

        # تحديث معاينات الأيقونات (إذا تم إنشاؤها)
        if hasattr(self, 'final_icon_images') and self.final_icon_images:
            self.display_icon_previews(self.final_icon_images)
    
    def display_image_on_canvas(self, image):
        """عرض الصورة في منطقة العمل الرئيسية"""
        # مسح المحتوى القديم
        for widget in self.drop_area.winfo_children():
            widget.destroy()

        canvas_w, canvas_h = self.drop_area.winfo_width(), self.drop_area.winfo_height()
        
        # إنشاء صورة مصغرة تناسب الحجم مع الحفاظ على النسبة
        thumb = image.copy()
        thumb.thumbnail((canvas_w - 20, canvas_h - 20), Image.Resampling.LANCZOS)

        photo = PhotoImage(thumb)
        
        # استخدام Canvas لعرض الخلفية الشطرنجية
        preview_canvas = tk.Canvas(self.drop_area, bg='#2a2a3e', highlightthickness=0)
        preview_canvas.pack(fill='both', expand=True)

        # رسم نمط شطرنجي
        self.create_checkerboard(preview_canvas, canvas_w, canvas_h)

        # وضع الصورة في المنتصف
        x = (canvas_w - thumb.width) // 2
        y = (canvas_h - thumb.height) // 2
        preview_canvas.create_image(x, y, image=photo, anchor='nw')
        preview_canvas.image = photo # حفظ المرجع

    def create_checkerboard(self, canvas, width, height, cell_size=10):
        """رسم خلفية شطرنجية على Canvas"""
        for y in range(0, height, cell_size):
            for x in range(0, width, cell_size):
                if (x // cell_size + y // cell_size) % 2 == 0:
                    color = "#3a3a4e"  # Darker
                else:
                    color = "#4a4a5e"  # Lighter
                canvas.create_rectangle(x, y, x + cell_size, y + cell_size, fill=color, outline="")

    def reset_image(self):
        """إعادة تعيين الصورة إلى حالتها الأصلية"""
        if self.original_image:
            self.processed_image = self.original_image.copy()
            self.reset_adjustments()
            self.update_previews()
            self.save_icon_btn.config(state='disabled')
            self.final_icon_images = None
            # مسح معاينات الأيقونات
            for widget in self.icon_previews_frame.winfo_children():
                widget.destroy()
            self.icon_info_label.configure(text="ℹ️ قم بالتحويل لعرض الأيقونات")

    def reset_adjustments(self):
        """إعادة تعيين قيم الضبط"""
        self.brightness_var.set(1.0)

    def apply_adjustments(self, value=None):
        """تطبيق التعديلات اليدوية"""
        if not self.original_image:
            return
        try:
            image = self.original_image.copy()
            enhancer = ImageEnhance.Brightness(image)
            image = enhancer.enhance(self.brightness_var.get())
            self.processed_image = image
            self.update_previews()
        except Exception as e:
            print(f"Error applying adjustments: {e}")

    def add_glass_effect(self, ):
        """إضافة تأثير زجاجي"""
        if not self.processed_image: return
        try:
            # تأثير ضبابي خفيف
            glass = self.processed_image.filter(ImageFilter.GaussianBlur(radius=2))
            # زيادة السطوع قليلاً
            enhancer = ImageEnhance.Brightness(glass)
            glass = enhancer.enhance(1.1)
            self.processed_image = glass
            self.update_previews()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إضافة التأثير الزجاجي: {e}")

    def add_neon_effect(self, glow_radius=5, contrast=1.8, color_enhance=2.5):
        """إضافة تأثير نيون مع التحكم في قوة التوهج"""
        if not self.processed_image:
            return
        try:
            # زيادة التباين والتشبع بشدة
            enhancer = ImageEnhance.Contrast(self.processed_image)
            neon = enhancer.enhance(contrast)
            enhancer = ImageEnhance.Color(neon)
            neon = enhancer.enhance(color_enhance)
            # إضافة توهج
            glow = neon.filter(ImageFilter.GaussianBlur(radius=glow_radius))
            self.processed_image = Image.alpha_composite(glow, neon)
            self.update_previews()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إضافة تأثير النيون: {e}")

    def add_cartoon_effect(self):
        """إضافة تأثير كرتوني (تبسيط الألوان + تحديد الحواف)"""
        if not self.processed_image: return
        try:
            # 1. تحديد الحواف
            edges = self.processed_image.convert('L').filter(ImageFilter.FIND_EDGES)
            edges = edges.filter(ImageFilter.SMOOTH_MORE)
            edges = edges.point(lambda x: 0 if x < 50 else 255) # زيادة حدة الحواف

            # 2. تبسيط الألوان (Quantization)
            # استخدام عدد قليل من الألوان لإعطاء المظهر الكرتوني
            color_simplified = self.processed_image.quantize(colors=16, method=Image.Quantize.MEDIANCUT).convert('RGBA')

            # 3. دمج الحواف مع الصورة المبسطة
            # نحتاج لقناع معكوس للحواف (الحواف سوداء والباقي أبيض)
            inverted_edges = ImageOps.invert(edges)
            
            # إنشاء خلفية سوداء للحواف
            black_background = Image.new('RGBA', self.processed_image.size, (0, 0, 0, 255))
            
            self.processed_image = Image.composite(black_background, color_simplified, inverted_edges)
            self.update_previews()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إضافة التأثير الكرتوني: {e}")

    def add_oil_painting_effect(self):
        """محاكاة تأثير الرسم الزيتي باستخدام فلاتر التوسيط"""
        if not self.processed_image: return
        try:
            # تطبيق الفلتر عدة مرات لزيادة التأثير
            self.processed_image = self.processed_image.filter(ImageFilter.MedianFilter(size=7))
            self.update_previews()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إضافة تأثير الرسم الزيتي: {e}")
    def convert_to_icon(self):
        """تحويل الصورة المعالجة إلى مجموعة أيقونات"""
        if not self.processed_image:
            messagebox.showwarning("تحذير", "يرجى تحميل أو توليد صورة أولاً")
            return

        try:
            sizes = [16, 24, 32, 48, 64, 128, 256]
            icon_images = []

            for size in sizes:
                resized = self.processed_image.copy()
                resized.thumbnail((size, size), Image.Resampling.LANCZOS)
                square = Image.new('RGBA', (size, size), (0, 0, 0, 0))
                x = (size - resized.width) // 2
                y = (size - resized.height) // 2
                square.paste(resized, (x, y), resized)
                icon_images.append(square)

            self.final_icon_images = icon_images
            self.display_icon_previews(icon_images)
            self.save_icon_btn.config(state='normal')
            self.icon_info_label.configure(text=f"✅ تم إنشاء الأيقونة بنجاح!\n📏 {len(sizes)} حجم مختلف\n💾 جاهزة للحفظ")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحويل الصورة: {e}")

    def display_icon_previews(self, icon_images):
        """عرض معاينات الأيقونات"""
        for widget in self.icon_previews_frame.winfo_children():
            widget.destroy()

        display_sizes = [16, 32, 48, 64]
        for i, size in enumerate(display_sizes):
            icon_index = [16, 24, 32, 48, 64, 128, 256].index(size)
            if icon_index < len(icon_images):
                frame = tk.Frame(self.icon_previews_frame, bg='#16213e')
                frame.pack(side='left', padx=3)

                photo = PhotoImage(icon_images[icon_index])
                label = tk.Label(frame, image=photo, bg='white', relief='solid', bd=1)
                label.image = photo
                label.pack()

                size_label = tk.Label(frame, text=f"{size}px", font=('Segoe UI', 7), fg='#ffffff', bg='#16213e')
                size_label.pack()

    def export_image(self, format_type):
        """حفظ الأيقونة أو الصورة"""
        if format_type == 'ico':
            if not hasattr(self, 'final_icon_images') or not self.final_icon_images:
                messagebox.showwarning("تحذير", "يرجى تحويل الصورة إلى أيقونة أولاً")
                return
            file_path = filedialog.asksaveasfilename(title="حفظ الأيقونة", defaultextension=".ico", filetypes=[("Icon files", "*.ico")])
            if file_path:
                try:
                    self.final_icon_images[0].save(file_path, format='ICO', sizes=[(img.width, img.height) for img in self.final_icon_images])
                    messagebox.showinfo("نجح الحفظ", f"تم حفظ الأيقونة في:\n{file_path}")
                    if self.callback:
                        self.callback(file_path)
                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في حفظ الأيقونة: {e}")
        
        elif format_type == 'png':
            if not self.processed_image:
                messagebox.showwarning("تحذير", "لا توجد صورة للحفظ")
                return
            file_path = filedialog.asksaveasfilename(title="حفظ الصورة", defaultextension=".png", filetypes=[("PNG files", "*.png")])
            if file_path:
                try:
                    self.processed_image.save(file_path, format='PNG')
                    messagebox.showinfo("نجح الحفظ", f"تم حفظ الصورة في:\n{file_path}")
                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في حفظ الصورة: {e}")

    def lighten_color(self, hex_color):
        """تفتيح لون hex"""
        h = hex_color.lstrip('#')
        rgb = tuple(int(h[i:i+2], 16) for i in (0, 2, 4))
        light_rgb = tuple(min(255, c + 30) for c in rgb)
        return f'#{light_rgb[0]:02x}{light_rgb[1]:02x}{light_rgb[2]:02x}'