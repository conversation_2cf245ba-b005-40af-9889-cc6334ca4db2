#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python to EXE Converter Pro v3.0 - Dependency Service
خدمة إدارة التبعيات

خدمة متقدمة لتحليل وإدارة تبعيات المشاريع مع:
- تحليل ذكي للتبعيات
- كشف المكتبات المستخدمة
- اقتراحات التحسين
- إدارة الإصدارات
"""

import ast
import os
import sys
import subprocess
import pkg_resources
import importlib.util
from pathlib import Path
from typing import Dict, List, Set, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import re

from . import Service
from ..core.logger import Logger
from ..core.exceptions import DependencyError

class DependencyType(Enum):
    """أنواع التبعيات"""
    STANDARD = "standard"      # مكتبات Python الأساسية
    THIRD_PARTY = "third_party"  # مكتبات خارجية
    LOCAL = "local"           # ملفات محلية
    BUILTIN = "builtin"       # مكتبات مدمجة

@dataclass
class DependencyInfo:
    """معلومات التبعية"""
    name: str
    version: Optional[str] = None
    type: DependencyType = DependencyType.THIRD_PARTY
    required: bool = True
    size: Optional[int] = None
    description: str = ""
    alternatives: List[str] = None
    
    def __post_init__(self):
        if self.alternatives is None:
            self.alternatives = []

class DependencyService(Service):
    """خدمة إدارة التبعيات"""
    
    def __init__(self):
        super().__init__("DependencyService")
        
        # قاعدة بيانات المكتبات الشائعة
        self.common_libraries = {
            # مكتبات الواجهة
            'tkinter': DependencyInfo('tkinter', type=DependencyType.STANDARD, description='واجهة رسومية أساسية'),
            'PyQt5': DependencyInfo('PyQt5', type=DependencyType.THIRD_PARTY, description='واجهة رسومية متقدمة'),
            'PyQt6': DependencyInfo('PyQt6', type=DependencyType.THIRD_PARTY, description='واجهة رسومية متقدمة'),
            'PySide2': DependencyInfo('PySide2', type=DependencyType.THIRD_PARTY, description='واجهة رسومية'),
            'PySide6': DependencyInfo('PySide6', type=DependencyType.THIRD_PARTY, description='واجهة رسومية'),
            'kivy': DependencyInfo('kivy', type=DependencyType.THIRD_PARTY, description='واجهة متعددة المنصات'),
            
            # مكتبات الويب
            'flask': DependencyInfo('flask', type=DependencyType.THIRD_PARTY, description='إطار عمل ويب'),
            'django': DependencyInfo('django', type=DependencyType.THIRD_PARTY, description='إطار عمل ويب متقدم'),
            'fastapi': DependencyInfo('fastapi', type=DependencyType.THIRD_PARTY, description='API سريع'),
            'requests': DependencyInfo('requests', type=DependencyType.THIRD_PARTY, description='طلبات HTTP'),
            
            # مكتبات البيانات
            'pandas': DependencyInfo('pandas', type=DependencyType.THIRD_PARTY, description='تحليل البيانات'),
            'numpy': DependencyInfo('numpy', type=DependencyType.THIRD_PARTY, description='حوسبة علمية'),
            'matplotlib': DependencyInfo('matplotlib', type=DependencyType.THIRD_PARTY, description='رسوم بيانية'),
            'seaborn': DependencyInfo('seaborn', type=DependencyType.THIRD_PARTY, description='رسوم إحصائية'),
            
            # مكتبات الذكاء الاصطناعي
            'tensorflow': DependencyInfo('tensorflow', type=DependencyType.THIRD_PARTY, description='تعلم آلي'),
            'torch': DependencyInfo('torch', type=DependencyType.THIRD_PARTY, description='تعلم عميق'),
            'sklearn': DependencyInfo('sklearn', type=DependencyType.THIRD_PARTY, description='تعلم آلي'),
            
            # مكتبات الألعاب
            'pygame': DependencyInfo('pygame', type=DependencyType.THIRD_PARTY, description='تطوير الألعاب'),
            'arcade': DependencyInfo('arcade', type=DependencyType.THIRD_PARTY, description='ألعاب 2D'),
            
            # مكتبات أساسية
            'os': DependencyInfo('os', type=DependencyType.BUILTIN, description='عمليات النظام'),
            'sys': DependencyInfo('sys', type=DependencyType.BUILTIN, description='معاملات النظام'),
            'json': DependencyInfo('json', type=DependencyType.BUILTIN, description='معالجة JSON'),
            'sqlite3': DependencyInfo('sqlite3', type=DependencyType.BUILTIN, description='قاعدة بيانات SQLite'),
        }
        
        # المكتبات المستبعدة افتراضياً
        self.default_excludes = {
            'test', 'tests', 'testing', 'unittest', 'pytest',
            'setuptools', 'pip', 'wheel', 'distutils'
        }
        
        # ذاكرة التخزين المؤقت
        self.cache = {}
    
    def initialize(self) -> bool:
        """تهيئة الخدمة"""
        try:
            # فحص توفر الأدوات المطلوبة
            self._check_tools()
            self.logger.info("تم تهيئة خدمة التبعيات")
            return True
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة خدمة التبعيات: {e}")
            return False
    
    def start(self) -> bool:
        """بدء الخدمة"""
        self.logger.info("تم بدء خدمة التبعيات")
        return True
    
    def stop(self) -> bool:
        """إيقاف الخدمة"""
        self.logger.info("تم إيقاف خدمة التبعيات")
        return True
    
    def cleanup(self) -> bool:
        """تنظيف الخدمة"""
        self.cache.clear()
        return True
    
    def _check_tools(self):
        """فحص توفر الأدوات المطلوبة"""
        # فحص pip
        try:
            subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                         capture_output=True, check=True)
        except subprocess.CalledProcessError:
            raise DependencyError("pip غير متوفر")
    
    def analyze_file(self, file_path: str) -> Dict[str, List[DependencyInfo]]:
        """تحليل ملف Python لاستخراج التبعيات"""
        if not os.path.exists(file_path):
            raise DependencyError(f"الملف غير موجود: {file_path}")
        
        # فحص الذاكرة المؤقتة
        cache_key = f"{file_path}:{os.path.getmtime(file_path)}"
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # تحليل AST
            tree = ast.parse(content)
            imports = self._extract_imports(tree)
            
            # تصنيف التبعيات
            categorized = self._categorize_dependencies(imports)
            
            # إضافة معلومات إضافية
            enriched = self._enrich_dependencies(categorized)
            
            # حفظ في الذاكرة المؤقتة
            self.cache[cache_key] = enriched
            
            return enriched
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الملف {file_path}: {e}")
            raise DependencyError(f"فشل في تحليل الملف: {e}")
    
    def _extract_imports(self, tree: ast.AST) -> Set[str]:
        """استخراج الاستيرادات من AST"""
        imports = set()
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    # أخذ الجزء الأول من اسم المكتبة
                    module_name = alias.name.split('.')[0]
                    imports.add(module_name)
            
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    # أخذ الجزء الأول من اسم المكتبة
                    module_name = node.module.split('.')[0]
                    imports.add(module_name)
        
        return imports
    
    def _categorize_dependencies(self, imports: Set[str]) -> Dict[str, List[str]]:
        """تصنيف التبعيات حسب النوع"""
        categorized = {
            'builtin': [],
            'standard': [],
            'third_party': [],
            'local': [],
            'unknown': []
        }
        
        for module_name in imports:
            if module_name in self.common_libraries:
                lib_info = self.common_libraries[module_name]
                categorized[lib_info.type.value].append(module_name)
            else:
                # محاولة تحديد النوع
                module_type = self._determine_module_type(module_name)
                categorized[module_type].append(module_name)
        
        return categorized
    
    def _determine_module_type(self, module_name: str) -> str:
        """تحديد نوع المكتبة"""
        # فحص المكتبات المدمجة
        if module_name in sys.builtin_module_names:
            return 'builtin'
        
        # فحص المكتبات الأساسية
        try:
            spec = importlib.util.find_spec(module_name)
            if spec and spec.origin:
                if 'site-packages' in spec.origin:
                    return 'third_party'
                elif spec.origin.startswith(sys.prefix):
                    return 'standard'
                else:
                    return 'local'
        except (ImportError, AttributeError, ValueError):
            pass
        
        return 'unknown'
    
    def _enrich_dependencies(self, categorized: Dict[str, List[str]]) -> Dict[str, List[DependencyInfo]]:
        """إثراء معلومات التبعيات"""
        enriched = {}
        
        for category, modules in categorized.items():
            enriched[category] = []
            
            for module_name in modules:
                if module_name in self.common_libraries:
                    dep_info = self.common_libraries[module_name]
                else:
                    # إنشاء معلومات أساسية
                    dep_info = DependencyInfo(
                        name=module_name,
                        type=DependencyType(category) if category in [t.value for t in DependencyType] else DependencyType.THIRD_PARTY
                    )
                
                # محاولة الحصول على الإصدار
                if dep_info.type == DependencyType.THIRD_PARTY:
                    dep_info.version = self._get_package_version(module_name)
                
                enriched[category].append(dep_info)
        
        return enriched
    
    def _get_package_version(self, package_name: str) -> Optional[str]:
        """الحصول على إصدار المكتبة"""
        try:
            return pkg_resources.get_distribution(package_name).version
        except pkg_resources.DistributionNotFound:
            return None
        except Exception:
            return None
    
    def get_missing_dependencies(self, dependencies: Dict[str, List[DependencyInfo]]) -> List[DependencyInfo]:
        """الحصول على التبعيات المفقودة"""
        missing = []
        
        for category, deps in dependencies.items():
            if category == 'third_party':
                for dep in deps:
                    if not self._is_package_installed(dep.name):
                        missing.append(dep)
        
        return missing
    
    def _is_package_installed(self, package_name: str) -> bool:
        """فحص ما إذا كانت المكتبة مثبتة"""
        try:
            pkg_resources.get_distribution(package_name)
            return True
        except pkg_resources.DistributionNotFound:
            return False
    
    def install_dependencies(self, dependencies: List[DependencyInfo]) -> Dict[str, bool]:
        """تثبيت التبعيات"""
        results = {}
        
        for dep in dependencies:
            if dep.type == DependencyType.THIRD_PARTY:
                try:
                    # تثبيت المكتبة
                    cmd = [sys.executable, '-m', 'pip', 'install', dep.name]
                    if dep.version:
                        cmd[-1] += f"=={dep.version}"
                    
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    results[dep.name] = result.returncode == 0
                    
                    if result.returncode != 0:
                        self.logger.error(f"فشل في تثبيت {dep.name}: {result.stderr}")
                    else:
                        self.logger.info(f"تم تثبيت {dep.name} بنجاح")
                        
                except Exception as e:
                    self.logger.error(f"خطأ في تثبيت {dep.name}: {e}")
                    results[dep.name] = False
            else:
                results[dep.name] = True  # المكتبات المدمجة لا تحتاج تثبيت
        
        return results
    
    def get_optimization_suggestions(self, dependencies: Dict[str, List[DependencyInfo]]) -> List[str]:
        """الحصول على اقتراحات التحسين"""
        suggestions = []
        
        # فحص المكتبات الثقيلة
        heavy_libraries = {'tensorflow', 'torch', 'pandas', 'numpy', 'matplotlib', 'opencv'}
        found_heavy = []
        
        for category, deps in dependencies.items():
            for dep in deps:
                if dep.name.lower() in heavy_libraries:
                    found_heavy.append(dep.name)
        
        if found_heavy:
            suggestions.append(f"🔍 مكتبات ثقيلة مكتشفة: {', '.join(found_heavy)} - فكر في استخدام --onedir")
        
        # فحص مكتبات الواجهة المتعددة
        ui_libraries = {'tkinter', 'PyQt5', 'PyQt6', 'PySide2', 'PySide6', 'kivy'}
        found_ui = []
        
        for category, deps in dependencies.items():
            for dep in deps:
                if dep.name in ui_libraries:
                    found_ui.append(dep.name)
        
        if len(found_ui) > 1:
            suggestions.append(f"⚠️ عدة مكتبات واجهة مكتشفة: {', '.join(found_ui)} - استخدم واحدة فقط")
        
        # اقتراحات عامة
        if len(dependencies.get('third_party', [])) > 10:
            suggestions.append("📦 عدد كبير من المكتبات الخارجية - راجع الضرورية منها فقط")
        
        return suggestions
    
    def generate_requirements_file(self, dependencies: Dict[str, List[DependencyInfo]], output_path: str = "requirements.txt"):
        """إنشاء ملف requirements.txt"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("# Generated by Python to EXE Converter Pro v3.0\n")
                f.write("# Requirements file\n\n")
                
                third_party_deps = dependencies.get('third_party', [])
                
                for dep in third_party_deps:
                    if dep.version:
                        f.write(f"{dep.name}=={dep.version}\n")
                    else:
                        f.write(f"{dep.name}\n")
            
            self.logger.info(f"تم إنشاء ملف المتطلبات: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء ملف المتطلبات: {e}")
            return False
    
    def get_dependency_tree(self, package_name: str) -> Dict[str, Any]:
        """الحصول على شجرة التبعيات لمكتبة معينة"""
        try:
            result = subprocess.run(
                [sys.executable, '-m', 'pip', 'show', package_name],
                capture_output=True, text=True
            )
            
            if result.returncode == 0:
                info = {}
                for line in result.stdout.split('\n'):
                    if ':' in line:
                        key, value = line.split(':', 1)
                        info[key.strip()] = value.strip()
                
                return info
            else:
                return {}
                
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على معلومات {package_name}: {e}")
            return {}
