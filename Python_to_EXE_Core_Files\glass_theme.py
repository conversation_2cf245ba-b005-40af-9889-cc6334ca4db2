# ملف تخصيص الثيم الزجاجي العصري المتقدم - Modern Glass UI
import tkinter as tk
from tkinter import ttk
import math
import random
import time
import threading

class ModernGlassTheme:
    """كلاس لتطبيق الثيم الزجاجي العصري المتقدم - نمط مواقع الإنترنت الحديثة"""

    # نظام ألوان عصري متدرج مع ألوان زاهية
    COLORS = {
        # الألوان الأساسية - نيون وزاهية
        'primary': '#6366f1',           # Indigo نيون
        'primary_light': '#818cf8',     # Indigo فاتح
        'primary_dark': '#4f46e5',      # Indigo غامق

        'secondary': '#ec4899',         # Pink نيون
        'secondary_light': '#f472b6',   # Pink فاتح
        'secondary_dark': '#db2777',    # Pink غامق

        'accent': '#06b6d4',            # Cyan نيون
        'accent_light': '#22d3ee',      # Cyan فاتح
        'accent_dark': '#0891b2',       # Cyan غامق

        'success': '#10b981',           # Emerald نيون
        'success_light': '#34d399',     # Emerald فاتح
        'success_dark': '#059669',      # Emerald غامق

        'warning': '#f59e0b',           # Amber نيون
        'warning_light': '#fbbf24',     # Amber فاتح
        'warning_dark': '#d97706',      # Amber غامق

        'error': '#ef4444',             # Red نيون
        'error_light': '#f87171',       # Red فاتح
        'error_dark': '#dc2626',        # Red غامق

        # خلفيات زجاجية متدرجة
        'background_primary': '#0f0f23',    # أزرق غامق عميق
        'background_secondary': '#1a1a2e',  # بنفسجي غامق
        'background_tertiary': '#16213e',   # أزرق متوسط

        'surface_glass': '#1a1a2e',       # زجاج شفاف
        'surface_glass_hover': '#16213e', # زجاج عند التمرير
        'surface_glass_active': '#0f0f23', # زجاج عند النقر

        # نصوص متدرجة
        'text_primary': '#ffffff',          # أبيض نقي
        'text_secondary': '#e2e8f0',        # رمادي فاتح
        'text_tertiary': '#94a3b8',         # رمادي متوسط
        'text_muted': '#64748b',            # رمادي غامق

        # حدود وظلال
        'border_glass': '#94a3b8',        # حدود زجاجية
        'border_glow': '#6366f1',         # توهج الحدود
        'shadow_primary': '#00000040',      # ظل أساسي
        'shadow_glow': '#6366f130',         # ظل متوهج

        # تدرجات ديناميكية
        'gradient_1': ['#667eea', '#764ba2'], # أزرق-بنفسجي
        'gradient_2': ['#f093fb', '#f5576c'], # وردي-أحمر
        'gradient_3': ['#4facfe', '#00f2fe'], # أزرق-سماوي
        'gradient_4': ['#43e97b', '#38f9d7'], # أخضر-سماوي
        'gradient_5': ['#fa709a', '#fee140'], # وردي-أصفر
        'gradient_6': ['#a8edea', '#fed6e3'], # سماوي-وردي فاتح
        'gradient_7': ['#ff9a9e', '#fecfef'], # وردي متدرج
        'gradient_8': ['#a18cd1', '#fbc2eb'], # بنفسجي-وردي
    }
    
    # تأثيرات الحركة والانيميشن
    ANIMATIONS = {
        'duration_fast': 150,      # مللي ثانية
        'duration_normal': 300,    # مللي ثانية
        'duration_slow': 500,      # مللي ثانية
        'easing_smooth': 'ease-out',
        'bounce_factor': 0.1,
        'glow_intensity': 0.8,
    }

    # أنماط الظلال المتقدمة
    SHADOWS = {
        'soft': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'medium': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        'large': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        'glow': '0 0 20px rgba(99, 102, 241, 0.3), 0 0 40px rgba(99, 102, 241, 0.1)',
        'neon': '0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor',
    }

    @classmethod
    def get_random_gradient(cls):
        """الحصول على تدرج عشوائي"""
        gradients = [cls.COLORS[f'gradient_{i}'] for i in range(1, 9)]
        return random.choice(gradients)

    @classmethod
    def create_dynamic_gradient(cls, canvas, width, height, gradient_colors=None, direction='diagonal'):
        """إنشاء تدرج ديناميكي متحرك"""
        if not gradient_colors:
            gradient_colors = cls.get_random_gradient()

        canvas.delete("gradient")

        if direction == 'diagonal':
            cls._draw_diagonal_gradient(canvas, width, height, gradient_colors)
        elif direction == 'radial':
            cls._draw_radial_gradient(canvas, width, height, gradient_colors)
        elif direction == 'vertical':
            cls._draw_vertical_gradient(canvas, width, height, gradient_colors)
        elif direction == 'horizontal':
            cls._draw_horizontal_gradient(canvas, width, height, gradient_colors)

    @classmethod
    def _draw_diagonal_gradient(cls, canvas, width, height, colors):
        """رسم تدرج قطري"""
        steps = max(width, height)
        for i in range(steps):
            ratio = i / steps
            color = cls._interpolate_color_advanced(colors[0], colors[1], ratio)

            # رسم خطوط قطرية
            x1 = int(i * width / steps)
            y1 = 0
            x2 = width
            y2 = int(i * height / steps)

            canvas.create_line(x1, y1, x2, y2, fill=color, width=2, tags="gradient")

    @classmethod
    def _draw_radial_gradient(cls, canvas, width, height, colors):
        """رسم تدرج دائري"""
        center_x, center_y = width // 2, height // 2
        max_radius = max(width, height) // 2

        for i in range(max_radius, 0, -2):
            ratio = 1 - (i / max_radius)
            color = cls._interpolate_color_advanced(colors[0], colors[1], ratio)

            canvas.create_oval(
                center_x - i, center_y - i,
                center_x + i, center_y + i,
                fill=color, outline=color, tags="gradient"
            )

    @classmethod
    def _draw_vertical_gradient(cls, canvas, width, height, colors):
        """رسم تدرج عمودي محسن"""
        for i in range(height):
            ratio = i / height
            color = cls._interpolate_color_advanced(colors[0], colors[1], ratio)
            canvas.create_line(0, i, width, i, fill=color, tags="gradient")

    @classmethod
    def _draw_horizontal_gradient(cls, canvas, width, height, colors):
        """رسم تدرج أفقي محسن"""
        for i in range(width):
            ratio = i / width
            color = cls._interpolate_color_advanced(colors[0], colors[1], ratio)
            canvas.create_line(i, 0, i, height, fill=color, tags="gradient")

    @classmethod
    def _interpolate_color_advanced(cls, color1, color2, ratio):
        """حساب اللون المتوسط المتقدم مع منحنيات ناعمة"""
        # تطبيق منحنى ناعم للانتقال
        smooth_ratio = cls._ease_in_out_cubic(ratio)

        # تحويل الألوان من hex إلى RGB
        r1, g1, b1 = cls._hex_to_rgb(color1)
        r2, g2, b2 = cls._hex_to_rgb(color2)

        # حساب اللون الجديد
        r = int(r1 + (r2 - r1) * smooth_ratio)
        g = int(g1 + (g2 - g1) * smooth_ratio)
        b = int(b1 + (b2 - b1) * smooth_ratio)

        return f"#{r:02x}{g:02x}{b:02x}"

    @classmethod
    def _ease_in_out_cubic(cls, t):
        """منحنى انتقال ناعم مكعبي"""
        return 4 * t * t * t if t < 0.5 else 1 - pow(-2 * t + 2, 3) / 2

    @staticmethod
    def apply_glass_effect(widget, style='modern', glow=True):
        """تطبيق التأثير الزجاجي العصري المتقدم"""
        if style == 'modern':
            bg_color = ModernGlassTheme.COLORS['surface_glass']
            border_color = ModernGlassTheme.COLORS['border_glass']
        elif style == 'neon':
            bg_color = ModernGlassTheme.COLORS['primary']
            border_color = ModernGlassTheme.COLORS['border_glow']
        else:
            bg_color = ModernGlassTheme.COLORS['surface_glass']
            border_color = ModernGlassTheme.COLORS['border_glass']

        widget.configure(
            bg=bg_color,
            relief='flat',
            bd=0,
            highlightbackground=border_color,
            highlightthickness=1 if glow else 0
        )
    
    @classmethod
    def create_modern_gradient_frame(cls, parent, gradient_type='dynamic', animate=True):
        """إنشاء إطار بتدرج عصري ديناميكي"""
        frame = tk.Frame(parent)
        canvas = tk.Canvas(
            frame,
            highlightthickness=0,
            bg=cls.COLORS['background_primary'],
            relief='flat',
            bd=0
        )
        canvas.pack(fill='both', expand=True)

        # متغيرات الانيميشن
        animation_frame = {'current': 0}
        gradient_colors = cls.get_random_gradient()

        def draw_animated_gradient(event=None):
            width = canvas.winfo_width()
            height = canvas.winfo_height()

            if width <= 1 or height <= 1:
                return

            if gradient_type == 'dynamic':
                # تدرج ديناميكي متحرك
                animation_frame['current'] = (animation_frame['current'] + 1) % 360
                angle = math.radians(animation_frame['current'])

                # تغيير الألوان بناءً على الوقت
                time_factor = math.sin(animation_frame['current'] * 0.01) * 0.5 + 0.5
                dynamic_colors = [
                    cls._blend_colors(gradient_colors[0], cls.COLORS['primary'], time_factor),
                    cls._blend_colors(gradient_colors[1], cls.COLORS['secondary'], time_factor)
                ]

                cls.create_dynamic_gradient(canvas, width, height, dynamic_colors, 'diagonal')

            elif gradient_type == 'aurora':
                # تأثير الشفق القطبي
                cls._draw_aurora_effect(canvas, width, height, animation_frame['current'])

            elif gradient_type == 'wave':
                # تأثير الموجات
                cls._draw_wave_gradient(canvas, width, height, animation_frame['current'])

            # استمرار الانيميشن
            if animate:
                canvas.after(50, draw_animated_gradient)

        def start_animation():
            draw_animated_gradient()

        canvas.bind('<Configure>', draw_animated_gradient)
        canvas.after(100, start_animation)

        return frame, canvas

    @classmethod
    def _draw_aurora_effect(cls, canvas, width, height, frame):
        """رسم تأثير الشفق القطبي"""
        canvas.delete("gradient")

        # ألوان الشفق القطبي
        aurora_colors = [
            cls.COLORS['primary'],
            cls.COLORS['secondary'],
            cls.COLORS['accent'],
            cls.COLORS['success']
        ]

        # رسم طبقات متعددة من الموجات
        for layer in range(4):
            for i in range(0, width, 3):
                wave_height = math.sin((i + frame * 2) * 0.02 + layer) * 30 + height // 2
                wave_height2 = math.sin((i + frame * 2) * 0.015 + layer + 1) * 25 + height // 2

                color_index = layer % len(aurora_colors)
                color = aurora_colors[color_index]

                # إضافة شفافية للطبقات
                alpha = 0.3 - layer * 0.05
                blended_color = cls._add_alpha_effect(color, alpha)

                canvas.create_line(
                    i, wave_height, i, wave_height2,
                    fill=blended_color, width=2, tags="gradient"
                )

    @classmethod
    def _draw_wave_gradient(cls, canvas, width, height, frame):
        """رسم تدرج موجي"""
        canvas.delete("gradient")

        for y in range(0, height, 2):
            # حساب الموجة
            wave_offset = math.sin((y + frame) * 0.02) * 50

            # تدرج الألوان بناءً على الموقع
            ratio = y / height
            base_color = cls._interpolate_color_advanced(
                cls.COLORS['primary'],
                cls.COLORS['secondary'],
                ratio
            )

            # رسم خط الموجة
            for x in range(0, width, 4):
                wave_x = x + wave_offset * math.sin(x * 0.01 + frame * 0.05)
                if 0 <= wave_x < width:
                    canvas.create_oval(
                        wave_x-1, y-1, wave_x+1, y+1,
                        fill=base_color, outline=base_color, tags="gradient"
                    )

    @classmethod
    def _blend_colors(cls, color1, color2, ratio):
        """مزج الألوان مع نسبة معينة"""
        r1, g1, b1 = cls._hex_to_rgb(color1)
        r2, g2, b2 = cls._hex_to_rgb(color2)

        r = int(r1 * (1 - ratio) + r2 * ratio)
        g = int(g1 * (1 - ratio) + g2 * ratio)
        b = int(b1 * (1 - ratio) + b2 * ratio)

        return f"#{r:02x}{g:02x}{b:02x}"

    @classmethod
    def _add_alpha_effect(cls, color, alpha):
        """إضافة تأثير الشفافية (محاكاة)"""
        r, g, b = cls._hex_to_rgb(color)
        bg_r, bg_g, bg_b = cls._hex_to_rgb(cls.COLORS['background_primary'])

        # مزج مع الخلفية لمحاكاة الشفافية
        r = int(r * alpha + bg_r * (1 - alpha))
        g = int(g * alpha + bg_g * (1 - alpha))
        b = int(b * alpha + bg_b * (1 - alpha))

        return f"#{r:02x}{g:02x}{b:02x}"
    
    @staticmethod
    def _hex_to_rgb(hex_color):
        """تحويل اللون من hex إلى RGB"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    @staticmethod
    def _interpolate_color(r1, g1, b1, r2, g2, b2, ratio):
        """حساب اللون المتوسط بين لونين"""
        r = int(r1 + (r2 - r1) * ratio)
        g = int(g1 + (g2 - g1) * ratio)
        b = int(b1 + (b2 - b1) * ratio)
        return f"#{r:02x}{g:02x}{b:02x}"
    
    @classmethod
    def create_modern_glass_button(cls, parent, text, command=None, style='primary', width=20, height=2, icon=None):
        """إنشاء زر زجاجي عصري مع تأثيرات متقدمة"""

        # اختيار نمط الألوان
        if style == 'primary':
            bg_color = cls.COLORS['primary']
            hover_color = cls.COLORS['primary_light']
            active_color = cls.COLORS['primary_dark']
        elif style == 'secondary':
            bg_color = cls.COLORS['secondary']
            hover_color = cls.COLORS['secondary_light']
            active_color = cls.COLORS['secondary_dark']
        elif style == 'accent':
            bg_color = cls.COLORS['accent']
            hover_color = cls.COLORS['accent_light']
            active_color = cls.COLORS['accent_dark']
        elif style == 'success':
            bg_color = cls.COLORS['success']
            hover_color = cls.COLORS['success_light']
            active_color = cls.COLORS['success_dark']
        elif style == 'glass':
            bg_color = cls.COLORS['surface_glass']
            hover_color = cls.COLORS['surface_glass_hover']
            active_color = cls.COLORS['surface_glass_active']
        else:
            bg_color = cls.COLORS['primary']
            hover_color = cls.COLORS['primary_light']
            active_color = cls.COLORS['primary_dark']

        # إنشاء إطار للزر مع تأثيرات
        button_frame = tk.Frame(parent, bg=parent.cget('bg'))

        # إنشاء canvas للتأثيرات المتقدمة
        canvas = tk.Canvas(
            button_frame,
            width=width*10,
            height=height*25,
            highlightthickness=0,
            relief='flat',
            bd=0
        )
        canvas.pack()

        # رسم الزر مع تدرج وظلال
        def draw_button(state='normal'):
            canvas.delete("all")
            w = canvas.winfo_width()
            h = canvas.winfo_height()

            if w <= 1 or h <= 1:
                return

            # اختيار اللون حسب الحالة
            if state == 'hover':
                current_color = hover_color
                glow_intensity = 0.8
            elif state == 'active':
                current_color = active_color
                glow_intensity = 1.0
            else:
                current_color = bg_color
                glow_intensity = 0.4

            # رسم الظل الخارجي (توهج)
            shadow_offset = 3
            for i in range(5):
                alpha = (5-i) * 0.1 * glow_intensity
                shadow_color = cls._add_alpha_effect(current_color, alpha)
                canvas.create_rounded_rectangle(
                    shadow_offset+i, shadow_offset+i,
                    w-shadow_offset-i, h-shadow_offset-i,
                    radius=15, fill=shadow_color, outline=""
                )

            # رسم الخلفية الرئيسية مع تدرج
            gradient_colors = [current_color, cls._blend_colors(current_color, '#ffffff', 0.1)]
            cls._draw_button_gradient(canvas, 5, 5, w-10, h-10, gradient_colors)

            # رسم الحدود الزجاجية
            canvas.create_rounded_rectangle(
                5, 5, w-5, h-5,
                radius=12, outline=cls.COLORS['border_glass'], width=1
            )

            # رسم النص مع تأثير
            display_text = f"{icon} {text}" if icon else text
            canvas.create_text(
                w//2, h//2,
                text=display_text,
                fill=cls.COLORS['text_primary'],
                font=('Segoe UI', 10, 'bold'),
                anchor='center'
            )

            # إضافة نقطة ضوء (highlight)
            highlight_color = cls._add_alpha_effect('#ffffff', 0.3)
            canvas.create_oval(
                w//4, h//4, w//4+10, h//4+10,
                fill=highlight_color, outline=""
            )

        # ربط الأحداث
        def on_enter(event):
            draw_button('hover')
            canvas.configure(cursor='hand2')

        def on_leave(event):
            draw_button('normal')
            canvas.configure(cursor='')

        def on_click(event):
            draw_button('active')
            if command:
                canvas.after(100, command)
            canvas.after(150, lambda: draw_button('hover'))

        canvas.bind("<Enter>", on_enter)
        canvas.bind("<Leave>", on_leave)
        canvas.bind("<Button-1>", on_click)
        canvas.bind("<Configure>", lambda e: draw_button('normal'))

        # رسم أولي
        canvas.after(10, lambda: draw_button('normal'))

        return button_frame

    @classmethod
    def _draw_button_gradient(cls, canvas, x1, y1, x2, y2, colors):
        """رسم تدرج للزر"""
        height = y2 - y1
        for i in range(int(height)):
            ratio = i / height
            color = cls._interpolate_color_advanced(colors[0], colors[1], ratio)
            canvas.create_line(x1, y1+i, x2, y1+i, fill=color)

    # إضافة دالة لرسم مستطيل مدور (محاكاة)
    def create_rounded_rectangle(self, x1, y1, x2, y2, radius=10, **kwargs):
        """رسم مستطيل مدور الزوايا"""
        points = []

        # الزاوية العلوية اليسرى
        points.extend([x1, y1 + radius])
        points.extend([x1, y1])
        points.extend([x1 + radius, y1])

        # الجانب العلوي
        points.extend([x2 - radius, y1])

        # الزاوية العلوية اليمنى
        points.extend([x2, y1])
        points.extend([x2, y1 + radius])

        # الجانب الأيمن
        points.extend([x2, y2 - radius])

        # الزاوية السفلية اليمنى
        points.extend([x2, y2])
        points.extend([x2 - radius, y2])

        # الجانب السفلي
        points.extend([x1 + radius, y2])

        # الزاوية السفلية اليسرى
        points.extend([x1, y2])
        points.extend([x1, y2 - radius])

        return self.create_polygon(points, smooth=True, **kwargs)

    # ربط الدالة بـ Canvas
    tk.Canvas.create_rounded_rectangle = create_rounded_rectangle

    @classmethod
    def create_modern_glass_card(cls, parent, title="", content="", width=300, height=200, style='glass'):
        """إنشاء بطاقة زجاجية عصرية مع تأثيرات متقدمة"""

        # إطار البطاقة
        card_frame = tk.Frame(parent, bg=parent.cget('bg'))

        # Canvas للتأثيرات
        canvas = tk.Canvas(
            card_frame,
            width=width,
            height=height,
            highlightthickness=0,
            relief='flat',
            bd=0,
            bg=cls.COLORS['background_primary']
        )
        canvas.pack(padx=10, pady=10)

        def draw_card():
            canvas.delete("all")
            w = canvas.winfo_width()
            h = canvas.winfo_height()

            if w <= 1 or h <= 1:
                return

            # رسم الظل الخارجي
            for i in range(8):
                alpha = (8-i) * 0.05
                shadow_color = cls._add_alpha_effect('#000000', alpha)
                canvas.create_rounded_rectangle(
                    i, i, w-i, h-i,
                    radius=20, fill=shadow_color, outline=""
                )

            # رسم الخلفية الزجاجية
            if style == 'glass':
                bg_color = cls.COLORS['surface_glass']
            elif style == 'gradient':
                # رسم تدرج ديناميكي
                gradient = cls.get_random_gradient()
                cls._draw_card_gradient(canvas, 8, 8, w-16, h-16, gradient)
                bg_color = None
            else:
                bg_color = cls.COLORS['surface_glass']

            if bg_color:
                canvas.create_rounded_rectangle(
                    8, 8, w-8, h-8,
                    radius=16, fill=bg_color, outline=""
                )

            # رسم الحدود المتوهجة
            border_color = cls.COLORS['border_glow']
            canvas.create_rounded_rectangle(
                8, 8, w-8, h-8,
                radius=16, outline=border_color, width=2
            )

            # رسم العنوان
            if title:
                canvas.create_text(
                    w//2, 30,
                    text=title,
                    fill=cls.COLORS['text_primary'],
                    font=('Segoe UI', 14, 'bold'),
                    anchor='center'
                )

            # رسم المحتوى
            if content:
                # تقسيم النص لعدة أسطر
                lines = content.split('\n')
                start_y = 60 if title else 30
                for i, line in enumerate(lines):
                    canvas.create_text(
                        w//2, start_y + i*20,
                        text=line,
                        fill=cls.COLORS['text_secondary'],
                        font=('Segoe UI', 10),
                        anchor='center'
                    )

            # إضافة تأثيرات ضوئية
            cls._add_light_effects(canvas, w, h)

        # تأثيرات التفاعل
        def on_enter(event):
            # تأثير توهج عند التمرير
            canvas.configure(cursor='hand2')
            cls._animate_card_glow(canvas, True)

        def on_leave(event):
            canvas.configure(cursor='')
            cls._animate_card_glow(canvas, False)

        canvas.bind("<Enter>", on_enter)
        canvas.bind("<Leave>", on_leave)
        canvas.bind("<Configure>", lambda e: draw_card())

        canvas.after(10, draw_card)
        return card_frame

    @classmethod
    def _draw_card_gradient(cls, canvas, x, y, w, h, colors):
        """رسم تدرج للبطاقة"""
        steps = 50
        for i in range(steps):
            ratio = i / steps
            color = cls._interpolate_color_advanced(colors[0], colors[1], ratio)
            y_pos = y + (h * i // steps)
            y_next = y + (h * (i+1) // steps)
            canvas.create_rectangle(x, y_pos, x+w, y_next, fill=color, outline="")

    @classmethod
    def _add_light_effects(cls, canvas, w, h):
        """إضافة تأثيرات ضوئية للبطاقة"""
        # نقاط ضوء متحركة
        for i in range(3):
            x = random.randint(20, w-20)
            y = random.randint(20, h-20)
            size = random.randint(2, 5)

            light_color = cls._add_alpha_effect(cls.COLORS['accent'], 0.6)
            canvas.create_oval(
                x-size, y-size, x+size, y+size,
                fill=light_color, outline=""
            )

    @classmethod
    def _animate_card_glow(cls, canvas, glow_on):
        """تحريك توهج البطاقة"""
        # هذا مجرد محاكاة - في التطبيق الحقيقي يمكن استخدام threading
        if glow_on:
            canvas.configure(highlightbackground=cls.COLORS['border_glow'], highlightthickness=2)
        else:
            canvas.configure(highlightthickness=0)

    @classmethod
    def create_modern_input_field(cls, parent, placeholder="", width=300, height=40, style='glass'):
        """إنشاء حقل إدخال عصري مع تأثيرات"""

        input_frame = tk.Frame(parent, bg=parent.cget('bg'))

        # Canvas للتأثيرات
        canvas = tk.Canvas(
            input_frame,
            width=width,
            height=height,
            highlightthickness=0,
            relief='flat',
            bd=0
        )
        canvas.pack(pady=5)

        # Entry widget مخفي
        entry_var = tk.StringVar()
        entry = tk.Entry(
            canvas,
            textvariable=entry_var,
            font=('Segoe UI', 11),
            fg=cls.COLORS['text_primary'],
            bg=cls.COLORS['background_secondary'],
            relief='flat',
            bd=0,
            insertbackground=cls.COLORS['accent']
        )

        def draw_input_field(focused=False):
            canvas.delete("all")
            w = canvas.winfo_width()
            h = canvas.winfo_height()

            if w <= 1 or h <= 1:
                return

            # اختيار الألوان حسب الحالة
            if focused:
                border_color = cls.COLORS['accent']
                bg_color = cls.COLORS['surface_glass_hover']
                glow_intensity = 0.8
            else:
                border_color = cls.COLORS['border_glass']
                bg_color = cls.COLORS['surface_glass']
                glow_intensity = 0.3

            # رسم الظل
            for i in range(3):
                alpha = (3-i) * 0.1 * glow_intensity
                shadow_color = cls._add_alpha_effect(border_color, alpha)
                canvas.create_rounded_rectangle(
                    i, i, w-i, h-i,
                    radius=8, fill=shadow_color, outline=""
                )

            # رسم الخلفية
            canvas.create_rounded_rectangle(
                3, 3, w-3, h-3,
                radius=6, fill=bg_color, outline=""
            )

            # رسم الحدود
            canvas.create_rounded_rectangle(
                3, 3, w-3, h-3,
                radius=6, outline=border_color, width=1
            )

            # وضع Entry widget
            entry.place(x=10, y=h//2-10, width=w-20, height=20)

            # رسم placeholder إذا كان الحقل فارغ
            if not entry_var.get() and not focused and placeholder:
                canvas.create_text(
                    15, h//2,
                    text=placeholder,
                    fill=cls.COLORS['text_muted'],
                    font=('Segoe UI', 10),
                    anchor='w'
                )

        def on_focus_in(event):
            draw_input_field(True)

        def on_focus_out(event):
            draw_input_field(False)

        def on_change(*args):
            draw_input_field(entry == canvas.focus_get())

        entry.bind("<FocusIn>", on_focus_in)
        entry.bind("<FocusOut>", on_focus_out)
        entry_var.trace('w', on_change)
        canvas.bind("<Configure>", lambda e: draw_input_field())

        canvas.after(10, lambda: draw_input_field())

        # إرجاع الإطار والمتغير للوصول للقيمة
        input_frame.get_value = lambda: entry_var.get()
        input_frame.set_value = lambda val: entry_var.set(val)
        input_frame.entry_var = entry_var

        return input_frame

    @classmethod
    def create_modern_progress_bar(cls, parent, width=400, height=30, style='neon'):
        """إنشاء شريط تقدم عصري مع تأثيرات نيون"""

        progress_frame = tk.Frame(parent, bg=parent.cget('bg'))

        canvas = tk.Canvas(
            progress_frame,
            width=width,
            height=height,
            highlightthickness=0,
            relief='flat',
            bd=0
        )
        canvas.pack(pady=10)

        progress_value = {'current': 0, 'target': 0, 'animating': False}

        def draw_progress_bar():
            canvas.delete("all")
            w = canvas.winfo_width()
            h = canvas.winfo_height()

            if w <= 1 or h <= 1:
                return

            # رسم الخلفية
            bg_color = cls.COLORS['surface_glass']
            canvas.create_rounded_rectangle(
                0, 0, w, h,
                radius=h//2, fill=bg_color, outline=""
            )

            # رسم الحدود
            canvas.create_rounded_rectangle(
                0, 0, w, h,
                radius=h//2, outline=cls.COLORS['border_glass'], width=1
            )

            # حساب عرض التقدم
            progress_width = int((w-4) * progress_value['current'] / 100)

            if progress_width > 0:
                if style == 'neon':
                    # تأثير نيون متوهج
                    cls._draw_neon_progress(canvas, 2, 2, progress_width, h-4)
                elif style == 'gradient':
                    # تدرج ملون
                    cls._draw_gradient_progress(canvas, 2, 2, progress_width, h-4)
                else:
                    # تقدم بسيط
                    canvas.create_rounded_rectangle(
                        2, 2, progress_width+2, h-2,
                        radius=(h-4)//2, fill=cls.COLORS['accent'], outline=""
                    )

            # رسم النص
            percentage_text = f"{progress_value['current']:.1f}%"
            canvas.create_text(
                w//2, h//2,
                text=percentage_text,
                fill=cls.COLORS['text_primary'],
                font=('Segoe UI', 10, 'bold'),
                anchor='center'
            )

        def animate_progress():
            """تحريك التقدم بسلاسة"""
            if progress_value['animating']:
                diff = progress_value['target'] - progress_value['current']
                if abs(diff) > 0.1:
                    progress_value['current'] += diff * 0.1
                    draw_progress_bar()
                    canvas.after(16, animate_progress)  # ~60 FPS
                else:
                    progress_value['current'] = progress_value['target']
                    progress_value['animating'] = False
                    draw_progress_bar()

        def update_progress(value):
            """تحديث قيمة التقدم مع انيميشن"""
            progress_value['target'] = max(0, min(100, value))
            if not progress_value['animating']:
                progress_value['animating'] = True
                animate_progress()

        canvas.bind("<Configure>", lambda e: draw_progress_bar())
        canvas.after(10, draw_progress_bar)

        # إرجاع الإطار مع دالة التحديث
        progress_frame.update_progress = update_progress
        return progress_frame

    @classmethod
    def _draw_neon_progress(cls, canvas, x, y, w, h):
        """رسم تقدم نيون متوهج"""
        # طبقات التوهج
        glow_colors = [
            cls.COLORS['accent'],
            cls.COLORS['accent_light'],
            '#ffffff'
        ]

        for i, color in enumerate(glow_colors):
            alpha = 0.8 - i * 0.2
            glow_color = cls._add_alpha_effect(color, alpha)
            offset = i * 2

            canvas.create_rounded_rectangle(
                x-offset, y-offset, x+w+offset, y+h+offset,
                radius=(h+offset*2)//2, fill=glow_color, outline=""
            )

    @classmethod
    def _draw_gradient_progress(cls, canvas, x, y, w, h):
        """رسم تقدم متدرج"""
        gradient = cls.get_random_gradient()
        steps = max(1, w//2)

        for i in range(steps):
            ratio = i / steps
            color = cls._interpolate_color_advanced(gradient[0], gradient[1], ratio)
            x_pos = x + (w * i // steps)
            x_next = x + (w * (i+1) // steps)

            canvas.create_rectangle(x_pos, y, x_next, y+h, fill=color, outline="")

    @classmethod
    def create_modern_notification(cls, parent, message, type='info', duration=3000):
        """إنشاء إشعار عصري منبثق"""

        # ألوان الإشعارات
        colors = {
            'info': cls.COLORS['accent'],
            'success': cls.COLORS['success'],
            'warning': cls.COLORS['warning'],
            'error': cls.COLORS['error']
        }

        # أيقونات الإشعارات
        icons = {
            'info': 'ℹ️',
            'success': '✅',
            'warning': '⚠️',
            'error': '❌'
        }

        notification_color = colors.get(type, colors['info'])
        notification_icon = icons.get(type, icons['info'])

        # إطار الإشعار
        notification_frame = tk.Toplevel(parent)
        notification_frame.overrideredirect(True)
        notification_frame.configure(bg=cls.COLORS['background_primary'])

        # موقع الإشعار (أعلى يمين الشاشة)
        screen_width = notification_frame.winfo_screenwidth()
        notification_frame.geometry(f"350x80+{screen_width-370}+20")

        # Canvas للتأثيرات
        canvas = tk.Canvas(
            notification_frame,
            width=350,
            height=80,
            highlightthickness=0,
            relief='flat',
            bd=0,
            bg=cls.COLORS['background_primary']
        )
        canvas.pack(fill='both', expand=True)

        def draw_notification():
            w, h = 350, 80

            # رسم الظل
            for i in range(5):
                alpha = (5-i) * 0.1
                shadow_color = cls._add_alpha_effect('#000000', alpha)
                canvas.create_rounded_rectangle(
                    i, i, w-i, h-i,
                    radius=10, fill=shadow_color, outline=""
                )

            # رسم الخلفية
            canvas.create_rounded_rectangle(
                5, 5, w-5, h-5,
                radius=8, fill=cls.COLORS['surface_glass'], outline=""
            )

            # رسم الحدود الملونة
            canvas.create_rounded_rectangle(
                5, 5, w-5, h-5,
                radius=8, outline=notification_color, width=2
            )

            # رسم شريط جانبي ملون
            canvas.create_rectangle(
                5, 5, 15, h-5,
                fill=notification_color, outline=""
            )

            # رسم الأيقونة
            canvas.create_text(
                35, h//2,
                text=notification_icon,
                fill=notification_color,
                font=('Segoe UI', 16),
                anchor='center'
            )

            # رسم الرسالة
            canvas.create_text(
                60, h//2,
                text=message,
                fill=cls.COLORS['text_primary'],
                font=('Segoe UI', 11),
                anchor='w',
                width=280
            )

        def close_notification():
            notification_frame.destroy()

        # رسم الإشعار
        draw_notification()

        # إغلاق تلقائي
        notification_frame.after(duration, close_notification)

        # إغلاق عند النقر
        canvas.bind("<Button-1>", lambda e: close_notification())

        return notification_frame

    @classmethod
    def create_modern_toggle_switch(cls, parent, text="", initial_state=False, callback=None):
        """إنشاء مفتاح تبديل عصري"""

        toggle_frame = tk.Frame(parent, bg=parent.cget('bg'))

        # متغير الحالة
        switch_state = {'on': initial_state, 'animating': False, 'position': 1.0 if initial_state else 0.0}

        # Canvas للمفتاح
        canvas = tk.Canvas(
            toggle_frame,
            width=60,
            height=30,
            highlightthickness=0,
            relief='flat',
            bd=0
        )
        canvas.pack(side='left', padx=(0, 10))

        # النص
        if text:
            label = tk.Label(
                toggle_frame,
                text=text,
                font=('Segoe UI', 10),
                fg=cls.COLORS['text_primary'],
                bg=parent.cget('bg')
            )
            label.pack(side='left')

        def draw_switch():
            canvas.delete("all")
            w, h = 60, 30

            # اختيار الألوان
            if switch_state['on']:
                bg_color = cls.COLORS['success']
                knob_color = cls.COLORS['text_primary']
            else:
                bg_color = cls.COLORS['surface_glass']
                knob_color = cls.COLORS['text_muted']

            # رسم الخلفية
            canvas.create_rounded_rectangle(
                0, 0, w, h,
                radius=h//2, fill=bg_color, outline=""
            )

            # رسم الحدود
            border_color = cls.COLORS['border_glass'] if not switch_state['on'] else cls.COLORS['success_dark']
            canvas.create_rounded_rectangle(
                0, 0, w, h,
                radius=h//2, outline=border_color, width=1
            )

            # حساب موقع المقبض
            knob_x = 5 + (w-h) * switch_state['position']

            # رسم ظل المقبض
            canvas.create_oval(
                knob_x+1, 6, knob_x+h-5, h-5,
                fill=cls._add_alpha_effect('#000000', 0.2), outline=""
            )

            # رسم المقبض
            canvas.create_oval(
                knob_x, 5, knob_x+h-10, h-5,
                fill=knob_color, outline=""
            )

        def animate_switch():
            """تحريك المفتاح"""
            if switch_state['animating']:
                target = 1.0 if switch_state['on'] else 0.0
                diff = target - switch_state['position']

                if abs(diff) > 0.01:
                    switch_state['position'] += diff * 0.2
                    draw_switch()
                    canvas.after(16, animate_switch)
                else:
                    switch_state['position'] = target
                    switch_state['animating'] = False
                    draw_switch()

        def toggle_switch(event=None):
            """تبديل حالة المفتاح"""
            switch_state['on'] = not switch_state['on']
            switch_state['animating'] = True
            animate_switch()

            if callback:
                callback(switch_state['on'])

        canvas.bind("<Button-1>", toggle_switch)
        canvas.bind("<Configure>", lambda e: draw_switch())
        canvas.configure(cursor='hand2')

        canvas.after(10, draw_switch)

        # إرجاع الإطار مع دوال التحكم
        toggle_frame.get_state = lambda: switch_state['on']
        toggle_frame.set_state = lambda state: toggle_switch() if state != switch_state['on'] else None

        return toggle_frame
    
    @staticmethod
    def animate_button_hover(button, normal_color=None, hover_color=None):
        """إضافة تأثير الحركة المتقدم للأزرار"""
        normal_color = normal_color or GlassTheme.COLORS['accent']
        hover_color = hover_color or GlassTheme.COLORS['accent_hover']
        
        def on_enter(event):
            button.configure(bg=hover_color)
            # تأثير رفع الزر
            button.configure(relief='raised', bd=2)
        
        def on_leave(event):
            button.configure(bg=normal_color)
            button.configure(relief='flat', bd=0)
        
        def on_click(event):
            button.configure(relief='sunken', bd=1)
            button.after(100, lambda: button.configure(relief='flat', bd=0))
        
        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)
        button.bind("<Button-1>", on_click)
    
    @staticmethod
    def create_glass_entry(parent, width=30, placeholder=""):
        """إنشاء حقل إدخال بتأثير زجاجي"""
        entry = tk.Entry(
            parent,
            font=('Segoe UI', 10),
            bg=GlassTheme.COLORS['surface'],
            fg=GlassTheme.COLORS['text_primary'],
            insertbackground=GlassTheme.COLORS['accent'],
            relief='flat',
            bd=1,
            width=width,
            highlightbackground=GlassTheme.COLORS['secondary'],
            highlightthickness=1
        )
        
        # إضافة placeholder
        if placeholder:
            GlassTheme._add_placeholder(entry, placeholder)
        
        return entry
    
    @staticmethod
    def _add_placeholder(entry, placeholder_text):
        """إضافة نص placeholder للحقل"""
        entry.insert(0, placeholder_text)
        entry.configure(fg=GlassTheme.COLORS['text_secondary'])
        
        def on_focus_in(event):
            if entry.get() == placeholder_text:
                entry.delete(0, tk.END)
                entry.configure(fg=GlassTheme.COLORS['text_primary'])
        
        def on_focus_out(event):
            if not entry.get():
                entry.insert(0, placeholder_text)
                entry.configure(fg=GlassTheme.COLORS['text_secondary'])
        
        entry.bind('<FocusIn>', on_focus_in)
        entry.bind('<FocusOut>', on_focus_out)
    
    @staticmethod
    def create_glass_text(parent, width=50, height=10):
        """إنشاء منطقة نص بتأثير زجاجي"""
        text_widget = tk.Text(
            parent,
            font=('Consolas', 9),
            bg=GlassTheme.COLORS['surface'],
            fg=GlassTheme.COLORS['text_primary'],
            insertbackground=GlassTheme.COLORS['accent'],
            relief='flat',
            bd=1,
            width=width,
            height=height,
            wrap=tk.WORD,
            highlightbackground=GlassTheme.COLORS['secondary'],
            highlightthickness=1
        )
        
        # إضافة شريط التمرير
        scrollbar = tk.Scrollbar(parent, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        return text_widget, scrollbar
    
    @staticmethod
    def create_glass_frame(parent, padding=10):
        """إنشاء إطار زجاجي متقدم"""
        frame = tk.Frame(
            parent,
            bg=GlassTheme.COLORS['surface'],
            relief='flat',
            bd=1,
            highlightbackground=GlassTheme.COLORS['secondary'],
            highlightthickness=1
        )
        
        # إضافة padding داخلي
        inner_frame = tk.Frame(frame, bg=GlassTheme.COLORS['surface'])
        inner_frame.pack(fill='both', expand=True, padx=padding, pady=padding)
        
        return frame, inner_frame
    
    @staticmethod
    def create_glass_label(parent, text, font_size=10, font_weight='normal'):
        """إنشاء تسمية بتأثير زجاجي"""
        label = tk.Label(
            parent,
            text=text,
            font=('Segoe UI', font_size, font_weight),
            fg=GlassTheme.COLORS['text_primary'],
            bg=GlassTheme.COLORS['surface'],
            anchor='w'
        )
        return label
    
    @staticmethod
    def create_progress_bar(parent, width=300, height=20):
        """إنشاء شريط تقدم بتأثير زجاجي"""
        canvas = tk.Canvas(
            parent,
            width=width,
            height=height,
            bg=GlassTheme.COLORS['surface'],
            highlightthickness=1,
            highlightbackground=GlassTheme.COLORS['secondary']
        )
        
        def update_progress(value):
            """تحديث قيمة التقدم (0-100)"""
            canvas.delete("progress")
            if value > 0:
                progress_width = int((width - 4) * value / 100)
                # رسم التدرج للتقدم
                for i in range(progress_width):
                    ratio = i / progress_width if progress_width > 0 else 0
                    r1, g1, b1 = GlassTheme._hex_to_rgb(GlassTheme.COLORS['accent'])
                    r2, g2, b2 = GlassTheme._hex_to_rgb(GlassTheme.COLORS['accent_hover'])
                    color = GlassTheme._interpolate_color(r1, g1, b1, r2, g2, b2, ratio)
                    canvas.create_line(
                        2 + i, 2, 2 + i, height - 2,
                        fill=color, tags="progress"
                    )
        
        canvas.update_progress = update_progress
        return canvas
