#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python to EXE Converter Pro v3.0 - Advanced Runner
مشغل متقدم للتطبيق

مشغل ذكي يدعم:
- اختيار النظام المناسب تلقائياً
- فحص المتطلبات
- تثبيت التبعيات المفقودة
- تشغيل النظام الأمثل
"""

import sys
import os
import subprocess
import importlib.util
from pathlib import Path
import tkinter as tk
from tkinter import messagebox

class AdvancedRunner:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.new_system_path = self.project_root / "Python_to_EXE_Converter_Pro_v3"
        self.old_system_file = self.project_root / "modern_pyinstaller_gui.py"
        
    def check_requirements(self):
        """فحص المتطلبات الأساسية"""
        missing = []
        
        # فحص Python version
        if sys.version_info < (3, 7):
            missing.append("Python 3.7+ مطلوب")
        
        # فحص المكتبات الأساسية
        required_packages = [
            'tkinter',
            'PIL',
            'PyInstaller'
        ]
        
        for package in required_packages:
            if not self.is_package_available(package):
                missing.append(package)
        
        return missing
    
    def is_package_available(self, package_name):
        """فحص توفر مكتبة"""
        try:
            if package_name == 'tkinter':
                import tkinter
                return True
            elif package_name == 'PIL':
                import PIL
                return True
            elif package_name == 'PyInstaller':
                import PyInstaller
                return True
            else:
                spec = importlib.util.find_spec(package_name)
                return spec is not None
        except ImportError:
            return False
    
    def install_missing_packages(self, missing_packages):
        """تثبيت المكتبات المفقودة"""
        if not missing_packages:
            return True
        
        print("🔄 تثبيت المكتبات المفقودة...")
        
        for package in missing_packages:
            if package == 'tkinter':
                print("⚠️ tkinter مدمج مع Python - تأكد من تثبيت Python بشكل صحيح")
                continue
            
            try:
                print(f"📦 تثبيت {package}...")
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✅ تم تثبيت {package}")
            except subprocess.CalledProcessError:
                print(f"❌ فشل في تثبيت {package}")
                return False
        
        return True
    
    def run_new_system(self):
        """تشغيل النظام الجديد v3.0"""
        try:
            sys.path.insert(0, str(self.new_system_path))
            from main import main
            print("🚀 تشغيل النظام الجديد v3.0")
            main()
            return True
        except ImportError as e:
            print(f"⚠️ النظام الجديد غير متوفر: {e}")
            return False
        except Exception as e:
            print(f"❌ خطأ في النظام الجديد: {e}")
            return False
    
    def run_old_system(self):
        """تشغيل النظام القديم"""
        try:
            if self.old_system_file.exists():
                print("🔄 تشغيل النظام التقليدي")
                from modern_pyinstaller_gui import ModernPyInstallerGUI
                app = ModernPyInstallerGUI()
                app.run()
                return True
            else:
                print("❌ النظام التقليدي غير موجود")
                return False
        except Exception as e:
            print(f"❌ خطأ في النظام التقليدي: {e}")
            return False
    
    def show_error_dialog(self, message):
        """عرض رسالة خطأ"""
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("خطأ", message)
            root.destroy()
        except:
            print(f"❌ {message}")
    
    def run(self):
        """تشغيل التطبيق"""
        print("🚀 Python to EXE Converter Pro - Advanced Runner")
        print("=" * 50)
        
        # فحص المتطلبات
        print("🔍 فحص المتطلبات...")
        missing = self.check_requirements()
        
        if missing:
            print("⚠️ متطلبات مفقودة:")
            for item in missing:
                print(f"  • {item}")
            
            # محاولة التثبيت التلقائي
            if self.install_missing_packages([pkg for pkg in missing if pkg not in ["Python 3.7+ مطلوب"]]):
                print("✅ تم تثبيت المتطلبات")
            else:
                error_msg = "فشل في تثبيت المتطلبات. يرجى تثبيتها يدوياً:\n"
                error_msg += "\n".join(f"pip install {pkg}" for pkg in missing if "Python" not in pkg)
                self.show_error_dialog(error_msg)
                return False
        
        # محاولة تشغيل النظام الجديد أولاً
        if self.new_system_path.exists():
            if self.run_new_system():
                return True
        
        # العودة للنظام القديم
        print("🔄 التبديل للنظام التقليدي...")
        if self.run_old_system():
            return True
        
        # فشل في تشغيل أي نظام
        error_msg = "فشل في تشغيل التطبيق. تأكد من وجود الملفات المطلوبة."
        self.show_error_dialog(error_msg)
        return False

def main():
    """نقطة الدخول الرئيسية"""
    runner = AdvancedRunner()
    try:
        success = runner.run()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"❌ خطأ حرج: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
