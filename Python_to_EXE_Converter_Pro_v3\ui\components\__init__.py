#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python to EXE Converter Pro v3.0 - UI Components
مكونات واجهة المستخدم

مكونات واجهة مستخدم متقدمة وقابلة لإعادة الاستخدام:
- أزرار ذكية
- مربعات حوار متقدمة
- عناصر تحكم مخصصة
- مكونات تفاعلية
"""

import tkinter as tk
from tkinter import ttk
from typing import Callable, Optional, Dict, Any
from abc import ABC, abstractmethod

class SmartComponent(ABC):
    """مكون ذكي أساسي"""
    
    def __init__(self, parent, name: str):
        self.parent = parent
        self.name = name
        self.widget = None
        self.callbacks = {}
        self.properties = {}
        
    @abstractmethod
    def create(self):
        """إنشاء المكون"""
        pass
    
    @abstractmethod
    def update(self, **kwargs):
        """تحديث المكون"""
        pass
    
    def bind_callback(self, event: str, callback: Callable):
        """ربط callback بحدث"""
        self.callbacks[event] = callback
    
    def trigger_callback(self, event: str, *args, **kwargs):
        """تشغيل callback"""
        if event in self.callbacks:
            self.callbacks[event](*args, **kwargs)
    
    def set_property(self, key: str, value: Any):
        """تعيين خاصية"""
        self.properties[key] = value
    
    def get_property(self, key: str, default: Any = None):
        """الحصول على خاصية"""
        return self.properties.get(key, default)

class AnimatedButton(SmartComponent):
    """زر متحرك ذكي"""
    
    def __init__(self, parent, text: str, command: Optional[Callable] = None, 
                 style: str = "primary", size: str = "medium"):
        super().__init__(parent, f"animated_button_{text}")
        self.text = text
        self.command = command
        self.style = style
        self.size = size
        self.is_animating = False
        
        # ألوان الأنماط
        self.styles = {
            "primary": {"bg": "#3b82f6", "fg": "#ffffff", "hover": "#2563eb"},
            "success": {"bg": "#10b981", "fg": "#ffffff", "hover": "#059669"},
            "warning": {"bg": "#f59e0b", "fg": "#ffffff", "hover": "#d97706"},
            "danger": {"bg": "#ef4444", "fg": "#ffffff", "hover": "#dc2626"},
            "secondary": {"bg": "#6b7280", "fg": "#ffffff", "hover": "#4b5563"}
        }
        
        # أحجام الأزرار
        self.sizes = {
            "small": {"width": 8, "height": 1, "font_size": 9},
            "medium": {"width": 12, "height": 1, "font_size": 10},
            "large": {"width": 16, "height": 2, "font_size": 12}
        }
    
    def create(self):
        """إنشاء الزر المتحرك"""
        style_config = self.styles.get(self.style, self.styles["primary"])
        size_config = self.sizes.get(self.size, self.sizes["medium"])
        
        self.widget = tk.Button(
            self.parent,
            text=self.text,
            command=self._on_click,
            bg=style_config["bg"],
            fg=style_config["fg"],
            font=("Segoe UI", size_config["font_size"], "bold"),
            width=size_config["width"],
            height=size_config["height"],
            relief="flat",
            bd=0,
            cursor="hand2"
        )
        
        # ربط أحداث الماوس للتأثيرات
        self.widget.bind("<Enter>", self._on_enter)
        self.widget.bind("<Leave>", self._on_leave)
        self.widget.bind("<Button-1>", self._on_press)
        self.widget.bind("<ButtonRelease-1>", self._on_release)
        
        return self.widget
    
    def _on_click(self):
        """معالج النقر"""
        if not self.is_animating and self.command:
            self.animate_click()
            self.command()
    
    def _on_enter(self, event):
        """معالج دخول الماوس"""
        if not self.is_animating:
            style_config = self.styles.get(self.style, self.styles["primary"])
            self.widget.configure(bg=style_config["hover"])
    
    def _on_leave(self, event):
        """معالج خروج الماوس"""
        if not self.is_animating:
            style_config = self.styles.get(self.style, self.styles["primary"])
            self.widget.configure(bg=style_config["bg"])
    
    def _on_press(self, event):
        """معالج الضغط"""
        self.widget.configure(relief="sunken")
    
    def _on_release(self, event):
        """معالج الإفلات"""
        self.widget.configure(relief="flat")
    
    def animate_click(self):
        """تحريك النقر"""
        if self.is_animating:
            return
        
        self.is_animating = True
        original_text = self.widget.cget("text")
        
        # تأثير النبض
        def pulse(step=0):
            if step < 6:
                if step % 2 == 0:
                    self.widget.configure(relief="raised", bd=2)
                else:
                    self.widget.configure(relief="flat", bd=0)
                self.parent.after(50, lambda: pulse(step + 1))
            else:
                self.widget.configure(relief="flat", bd=0, text=original_text)
                self.is_animating = False
        
        pulse()
    
    def update(self, **kwargs):
        """تحديث الزر"""
        if "text" in kwargs:
            self.text = kwargs["text"]
            if self.widget:
                self.widget.configure(text=self.text)
        
        if "command" in kwargs:
            self.command = kwargs["command"]
        
        if "enabled" in kwargs:
            state = "normal" if kwargs["enabled"] else "disabled"
            if self.widget:
                self.widget.configure(state=state)

class ProgressIndicator(SmartComponent):
    """مؤشر تقدم ذكي"""
    
    def __init__(self, parent, mode: str = "determinate"):
        super().__init__(parent, "progress_indicator")
        self.mode = mode
        self.value = 0
        self.maximum = 100
        self.is_animated = False
    
    def create(self):
        """إنشاء مؤشر التقدم"""
        style = ttk.Style()
        style.configure("Custom.Horizontal.TProgressbar",
                       background="#3b82f6",
                       troughcolor="#e5e7eb",
                       borderwidth=0,
                       lightcolor="#3b82f6",
                       darkcolor="#3b82f6")
        
        self.widget = ttk.Progressbar(
            self.parent,
            mode=self.mode,
            maximum=self.maximum,
            style="Custom.Horizontal.TProgressbar"
        )
        
        return self.widget
    
    def update(self, **kwargs):
        """تحديث مؤشر التقدم"""
        if "value" in kwargs:
            self.value = kwargs["value"]
            if self.widget:
                self.widget["value"] = self.value
        
        if "maximum" in kwargs:
            self.maximum = kwargs["maximum"]
            if self.widget:
                self.widget["maximum"] = self.maximum
    
    def start_animation(self):
        """بدء الحركة"""
        if self.mode == "indeterminate" and not self.is_animated:
            self.is_animated = True
            self.widget.start()
    
    def stop_animation(self):
        """إيقاف الحركة"""
        if self.is_animated:
            self.is_animated = False
            self.widget.stop()

class SmartTextArea(SmartComponent):
    """منطقة نص ذكية"""
    
    def __init__(self, parent, width: int = 80, height: int = 20, 
                 syntax_highlight: bool = False):
        super().__init__(parent, "smart_text_area")
        self.width = width
        self.height = height
        self.syntax_highlight = syntax_highlight
        self.scrollbar = None
    
    def create(self):
        """إنشاء منطقة النص"""
        # إطار للنص والتمرير
        frame = tk.Frame(self.parent)
        
        # منطقة النص
        self.widget = tk.Text(
            frame,
            width=self.width,
            height=self.height,
            wrap=tk.WORD,
            font=("Consolas", 10),
            bg="#1e1e1e",
            fg="#ffffff",
            insertbackground="#ffffff",
            selectbackground="#404040",
            relief="flat",
            bd=1
        )
        
        # شريط التمرير
        self.scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, 
                                      command=self.widget.yview)
        self.widget.configure(yscrollcommand=self.scrollbar.set)
        
        # ترتيب العناصر
        self.widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # إعداد تمييز الصيغة إذا طُلب
        if self.syntax_highlight:
            self._setup_syntax_highlighting()
        
        return frame
    
    def _setup_syntax_highlighting(self):
        """إعداد تمييز الصيغة"""
        # تمييز الكلمات المفتاحية
        self.widget.tag_configure("keyword", foreground="#569cd6")
        self.widget.tag_configure("string", foreground="#ce9178")
        self.widget.tag_configure("comment", foreground="#6a9955")
        self.widget.tag_configure("number", foreground="#b5cea8")
        
        # ربط حدث تغيير النص
        self.widget.bind("<KeyRelease>", self._highlight_syntax)
    
    def _highlight_syntax(self, event=None):
        """تمييز الصيغة"""
        content = self.widget.get("1.0", tk.END)
        
        # مسح التمييز السابق
        for tag in ["keyword", "string", "comment", "number"]:
            self.widget.tag_remove(tag, "1.0", tk.END)
        
        # تمييز الكلمات المفتاحية
        keywords = ["def", "class", "if", "else", "elif", "for", "while", 
                   "try", "except", "import", "from", "return", "break", "continue"]
        
        for keyword in keywords:
            start = "1.0"
            while True:
                pos = self.widget.search(f"\\b{keyword}\\b", start, tk.END, regexp=True)
                if not pos:
                    break
                end = f"{pos}+{len(keyword)}c"
                self.widget.tag_add("keyword", pos, end)
                start = end
    
    def update(self, **kwargs):
        """تحديث منطقة النص"""
        if "text" in kwargs:
            self.widget.delete("1.0", tk.END)
            self.widget.insert("1.0", kwargs["text"])
        
        if "append" in kwargs:
            self.widget.insert(tk.END, kwargs["append"])
            self.widget.see(tk.END)
    
    def get_text(self) -> str:
        """الحصول على النص"""
        return self.widget.get("1.0", tk.END)
    
    def clear(self):
        """مسح النص"""
        self.widget.delete("1.0", tk.END)

class NotificationToast(SmartComponent):
    """إشعار منبثق"""
    
    def __init__(self, parent, message: str, type: str = "info", duration: int = 3000):
        super().__init__(parent, "notification_toast")
        self.message = message
        self.type = type
        self.duration = duration
        self.toast_window = None
        
        # ألوان الأنواع
        self.type_colors = {
            "info": {"bg": "#3b82f6", "fg": "#ffffff"},
            "success": {"bg": "#10b981", "fg": "#ffffff"},
            "warning": {"bg": "#f59e0b", "fg": "#000000"},
            "error": {"bg": "#ef4444", "fg": "#ffffff"}
        }
    
    def create(self):
        """إنشاء الإشعار"""
        # إنشاء نافذة منبثقة
        self.toast_window = tk.Toplevel(self.parent)
        self.toast_window.withdraw()  # إخفاء مؤقت
        
        # إعداد النافذة
        self.toast_window.overrideredirect(True)  # بدون إطار
        self.toast_window.attributes("-topmost", True)  # في المقدمة
        
        # ألوان النوع
        colors = self.type_colors.get(self.type, self.type_colors["info"])
        
        # إطار الإشعار
        frame = tk.Frame(
            self.toast_window,
            bg=colors["bg"],
            relief="raised",
            bd=1
        )
        frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # نص الإشعار
        label = tk.Label(
            frame,
            text=self.message,
            bg=colors["bg"],
            fg=colors["fg"],
            font=("Segoe UI", 10),
            padx=20,
            pady=10
        )
        label.pack()
        
        # تحديد الموضع
        self._position_toast()
        
        # إظهار الإشعار
        self.show()
        
        return self.toast_window
    
    def _position_toast(self):
        """تحديد موضع الإشعار"""
        # تحديث النافذة للحصول على الحجم الصحيح
        self.toast_window.update_idletasks()
        
        # حساب الموضع (أعلى يمين الشاشة)
        screen_width = self.toast_window.winfo_screenwidth()
        screen_height = self.toast_window.winfo_screenheight()
        
        toast_width = self.toast_window.winfo_reqwidth()
        toast_height = self.toast_window.winfo_reqheight()
        
        x = screen_width - toast_width - 20
        y = 50
        
        self.toast_window.geometry(f"{toast_width}x{toast_height}+{x}+{y}")
    
    def show(self):
        """إظهار الإشعار"""
        self.toast_window.deiconify()
        
        # تأثير الظهور
        self.toast_window.attributes("-alpha", 0.0)
        self._fade_in()
        
        # جدولة الإخفاء
        self.parent.after(self.duration, self.hide)
    
    def _fade_in(self, alpha=0.0):
        """تأثير الظهور التدريجي"""
        if alpha < 1.0:
            alpha += 0.1
            self.toast_window.attributes("-alpha", alpha)
            self.parent.after(50, lambda: self._fade_in(alpha))
    
    def hide(self):
        """إخفاء الإشعار"""
        self._fade_out()
    
    def _fade_out(self, alpha=1.0):
        """تأثير الاختفاء التدريجي"""
        if alpha > 0.0:
            alpha -= 0.1
            self.toast_window.attributes("-alpha", alpha)
            self.parent.after(50, lambda: self._fade_out(alpha))
        else:
            self.toast_window.destroy()
    
    def update(self, **kwargs):
        """تحديث الإشعار"""
        if "message" in kwargs:
            self.message = kwargs["message"]

# دوال مساعدة لإنشاء المكونات
def create_animated_button(parent, text: str, command: Callable = None, 
                          style: str = "primary", size: str = "medium"):
    """إنشاء زر متحرك"""
    button = AnimatedButton(parent, text, command, style, size)
    return button.create()

def create_progress_indicator(parent, mode: str = "determinate"):
    """إنشاء مؤشر تقدم"""
    progress = ProgressIndicator(parent, mode)
    return progress.create()

def create_smart_text_area(parent, width: int = 80, height: int = 20, 
                          syntax_highlight: bool = False):
    """إنشاء منطقة نص ذكية"""
    text_area = SmartTextArea(parent, width, height, syntax_highlight)
    return text_area.create()

def show_notification(parent, message: str, type: str = "info", duration: int = 3000):
    """إظهار إشعار"""
    toast = NotificationToast(parent, message, type, duration)
    toast.create()

# تصدير المكونات
__all__ = [
    'SmartComponent',
    'AnimatedButton', 
    'ProgressIndicator',
    'SmartTextArea',
    'NotificationToast',
    'create_animated_button',
    'create_progress_indicator', 
    'create_smart_text_area',
    'show_notification'
]
