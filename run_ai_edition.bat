@echo off
chcp 65001 >nul
title Python to EXE Converter Pro v2.0 - AI Icon Designer Edition

echo.
echo ================================================
echo   🚀 Python to EXE Converter Pro v2.0
echo   🎨 AI Icon Designer Edition - تصميم ذكي للأيقونات
echo ================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت أو غير موجود في PATH
    echo 💡 يرجى تثبيت Python من: https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: pip غير متوفر
    echo 💡 يرجى إعادة تثبيت Python مع pip
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على pip

REM تثبيت المكتبات المطلوبة للنظام الذكي
echo.
echo 🎨 تحضير نظام تصميم الأيقونات الذكي...

REM PyInstaller
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت PyInstaller...
    pip install PyInstaller
)

REM Pillow لمعالجة الصور
python -c "import PIL" >nul 2>&1
if errorlevel 1 (
    echo 🖼️ تثبيت Pillow...
    pip install Pillow
)

REM requests للبحث في APIs
python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo 🌐 تثبيت requests...
    pip install requests
)

REM packaging لمقارنة الإصدارات
python -c "import packaging" >nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت packaging...
    pip install packaging
)

REM toml لتحليل pyproject.toml (اختياري)
python -c "import toml" >nul 2>&1
if errorlevel 1 (
    echo 📄 تثبيت toml (اختياري)...
    pip install toml
)

REM التحقق من وجود الملفات المطلوبة
if not exist "modern_pyinstaller_gui.py" (
    echo ❌ خطأ: ملف modern_pyinstaller_gui.py غير موجود
    echo 💡 تأكد من وجود جميع ملفات التطبيق في نفس المجلد
    echo.
    pause
    exit /b 1
)

if not exist "smart_dependency_manager.py" (
    echo ❌ خطأ: ملف smart_dependency_manager.py غير موجود
    echo 💡 تأكد من وجود جميع ملفات التطبيق في نفس المجلد
    echo.
    pause
    exit /b 1
)

if not exist "ai_icon_generator.py" (
    echo ❌ خطأ: ملف ai_icon_generator.py غير موجود
    echo 💡 تأكد من وجود جميع ملفات التطبيق في نفس المجلد
    echo.
    pause
    exit /b 1
)

if not exist "icon_editor.py" (
    echo ❌ خطأ: ملف icon_editor.py غير موجود
    echo 💡 تأكد من وجود جميع ملفات التطبيق في نفس المجلد
    echo.
    pause
    exit /b 1
)

echo.
echo 🎨 بدء تشغيل نظام تصميم الأيقونات الذكي...
echo ✨ مع ذكاء اصطناعي متقدم ومحرر تفاعلي
echo.
echo 🎯 المميزات الجديدة:
echo   • تحليل ذكي للكود لفهم نوع التطبيق
echo   • توليد اقتراحات أيقونات مخصصة
echo   • محرر أيقونات تفاعلي متقدم
echo   • معاينة مباشرة بأحجام متعددة
echo   • تطبيق مباشر على التطبيق
echo   • دعم تنسيقات متعددة (ICO, PNG, JPG)
echo   • حفظ وتحميل المشاريع
echo.

REM تشغيل التطبيق الذكي
python modern_pyinstaller_gui.py

REM في حالة حدوث خطأ
if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل التطبيق
    echo 💡 تحقق من رسائل الخطأ أعلاه
    echo.
    pause
)

echo.
echo 👋 شكراً لاستخدام Python to EXE Converter Pro - AI Icon Designer Edition
pause
