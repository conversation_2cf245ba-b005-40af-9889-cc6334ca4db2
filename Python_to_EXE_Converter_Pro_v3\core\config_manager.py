#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python to EXE Converter Pro v3.0 - Configuration Manager
مدير الإعدادات المتقدم

نظام إدارة إعدادات شامل يدعم:
- إعدادات متعددة المستويات (نظام، مستخدم، مشروع)
- تشفير الإعدادات الحساسة
- التحقق من صحة الإعدادات
- النسخ الاحتياطي والاستعادة
- مراقبة التغييرات
"""

import json
import os
import shutil
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import threading
from datetime import datetime
import hashlib
import base64

from .logger import Logger
from .exceptions import ConfigError, ValidationError

class ConfigLevel(Enum):
    """مستويات الإعدادات"""
    SYSTEM = "system"      # إعدادات النظام
    USER = "user"          # إعدادات المستخدم
    PROJECT = "project"    # إعدادات المشروع
    RUNTIME = "runtime"    # إعدادات وقت التشغيل

@dataclass
class ConfigSchema:
    """مخطط الإعدادات للتحقق من الصحة"""
    key: str
    type: type
    default: Any
    required: bool = False
    choices: Optional[List[Any]] = None
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    description: str = ""
    sensitive: bool = False  # للإعدادات الحساسة التي تحتاج تشفير

class ConfigManager:
    """مدير الإعدادات المتقدم"""
    
    # مخطط الإعدادات الافتراضي
    DEFAULT_SCHEMA = [
        # إعدادات التطبيق العامة
        ConfigSchema("app.language", str, "ar", choices=["ar", "en"]),
        ConfigSchema("app.theme", str, "dark", choices=["light", "dark", "auto"]),
        ConfigSchema("app.auto_save", bool, True),
        ConfigSchema("app.check_updates", bool, True),
        
        # إعدادات التحويل الافتراضية
        ConfigSchema("conversion.default_mode", str, "onefile", 
                    choices=["onefile", "onedir"]),
        ConfigSchema("conversion.default_optimization", int, 1, 
                    min_value=0, max_value=3),
        ConfigSchema("conversion.clean_build", bool, True),
        ConfigSchema("conversion.upx_compress", bool, False),
        
        # إعدادات الواجهة
        ConfigSchema("ui.window_width", int, 1400, min_value=800, max_value=3840),
        ConfigSchema("ui.window_height", int, 900, min_value=600, max_value=2160),
        ConfigSchema("ui.remember_window_state", bool, True),
        ConfigSchema("ui.show_advanced_options", bool, False),
        
        # إعدادات السجلات
        ConfigSchema("logging.level", str, "INFO", 
                    choices=["DEBUG", "INFO", "WARNING", "ERROR"]),
        ConfigSchema("logging.max_file_size", int, 10, min_value=1, max_value=100),
        ConfigSchema("logging.backup_count", int, 5, min_value=1, max_value=20),
        
        # إعدادات الأمان
        ConfigSchema("security.encrypt_settings", bool, False),
        ConfigSchema("security.api_key", str, "", sensitive=True),
        ConfigSchema("security.check_file_integrity", bool, True),
        
        # إعدادات الأداء
        ConfigSchema("performance.max_threads", int, 4, min_value=1, max_value=16),
        ConfigSchema("performance.cache_size", int, 100, min_value=10, max_value=1000),
        ConfigSchema("performance.enable_caching", bool, True),
    ]
    
    def __init__(self, app_name: str = "PyToExeConverter", logger: Optional[Logger] = None):
        self.app_name = app_name
        self.logger = logger or Logger("ConfigManager")
        
        # مسارات الإعدادات
        self.config_dir = self._get_config_directory()
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # ملفات الإعدادات لكل مستوى
        self.config_files = {
            ConfigLevel.SYSTEM: self.config_dir / "system.json",
            ConfigLevel.USER: self.config_dir / "user.json",
            ConfigLevel.PROJECT: Path.cwd() / f".{app_name.lower()}.json",
            ConfigLevel.RUNTIME: None  # في الذاكرة فقط
        }
        
        # تخزين الإعدادات
        self.configs = {level: {} for level in ConfigLevel}
        self.schema = {item.key: item for item in self.DEFAULT_SCHEMA}
        
        # مراقبة التغييرات
        self.change_callbacks = []
        self.lock = threading.Lock()
        
        # تحميل الإعدادات
        self._load_all_configs()
    
    def _get_config_directory(self) -> Path:
        """الحصول على مجلد الإعدادات"""
        if os.name == 'nt':  # Windows
            config_dir = Path(os.environ.get('APPDATA', '')) / self.app_name
        else:  # Linux/Mac
            config_dir = Path.home() / f'.config/{self.app_name}'
        
        return config_dir
    
    def _load_all_configs(self):
        """تحميل جميع الإعدادات"""
        for level in ConfigLevel:
            if level != ConfigLevel.RUNTIME:
                self._load_config(level)
    
    def _load_config(self, level: ConfigLevel):
        """تحميل إعدادات مستوى معين"""
        config_file = self.config_files[level]
        
        if not config_file or not config_file.exists():
            self.configs[level] = {}
            return
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # فك تشفير الإعدادات الحساسة
            if self.get("security.encrypt_settings", False):
                data = self._decrypt_sensitive_data(data)
            
            self.configs[level] = data
            self.logger.debug(f"تم تحميل إعدادات {level.value}")
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات {level.value}: {e}")
            self.configs[level] = {}
    
    def _save_config(self, level: ConfigLevel):
        """حفظ إعدادات مستوى معين"""
        if level == ConfigLevel.RUNTIME:
            return  # لا يتم حفظ إعدادات وقت التشغيل
        
        config_file = self.config_files[level]
        if not config_file:
            return
        
        try:
            # إنشاء نسخة احتياطية
            if config_file.exists():
                backup_file = config_file.with_suffix('.json.backup')
                shutil.copy2(config_file, backup_file)
            
            data = self.configs[level].copy()
            
            # تشفير الإعدادات الحساسة
            if self.get("security.encrypt_settings", False):
                data = self._encrypt_sensitive_data(data)
            
            # حفظ الملف
            config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.logger.debug(f"تم حفظ إعدادات {level.value}")
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ إعدادات {level.value}: {e}")
            raise ConfigError(f"فشل في حفظ الإعدادات: {e}")
    
    def get(self, key: str, default: Any = None, level: Optional[ConfigLevel] = None) -> Any:
        """الحصول على قيمة إعداد"""
        with self.lock:
            # البحث في مستوى محدد
            if level:
                return self.configs[level].get(key, default)
            
            # البحث بالأولوية: runtime -> project -> user -> system -> default
            search_order = [
                ConfigLevel.RUNTIME,
                ConfigLevel.PROJECT,
                ConfigLevel.USER,
                ConfigLevel.SYSTEM
            ]
            
            for search_level in search_order:
                if key in self.configs[search_level]:
                    return self.configs[search_level][key]
            
            # إرجاع القيمة الافتراضية من المخطط
            if key in self.schema:
                return self.schema[key].default
            
            return default
    
    def set(self, key: str, value: Any, level: ConfigLevel = ConfigLevel.USER, 
            save: bool = True) -> bool:
        """تعيين قيمة إعداد"""
        try:
            # التحقق من صحة القيمة
            self._validate_value(key, value)
            
            with self.lock:
                self.configs[level][key] = value
                
                if save and level != ConfigLevel.RUNTIME:
                    self._save_config(level)
                
                # إشعار المراقبين
                self._notify_change(key, value, level)
            
            self.logger.debug(f"تم تعيين {key} = {value} في {level.value}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في تعيين {key}: {e}")
            return False
    
    def _validate_value(self, key: str, value: Any):
        """التحقق من صحة القيمة"""
        if key not in self.schema:
            return  # مفاتيح غير معرفة مسموحة
        
        schema = self.schema[key]
        
        # فحص النوع
        if not isinstance(value, schema.type):
            raise ValidationError(f"نوع خاطئ لـ {key}: متوقع {schema.type.__name__}")
        
        # فحص الخيارات المحددة
        if schema.choices and value not in schema.choices:
            raise ValidationError(f"قيمة غير صالحة لـ {key}: {value}")
        
        # فحص النطاق للأرقام
        if isinstance(value, (int, float)):
            if schema.min_value is not None and value < schema.min_value:
                raise ValidationError(f"قيمة {key} أقل من الحد الأدنى: {schema.min_value}")
            if schema.max_value is not None and value > schema.max_value:
                raise ValidationError(f"قيمة {key} أكبر من الحد الأقصى: {schema.max_value}")
    
    def delete(self, key: str, level: ConfigLevel = ConfigLevel.USER, 
               save: bool = True) -> bool:
        """حذف إعداد"""
        try:
            with self.lock:
                if key in self.configs[level]:
                    del self.configs[level][key]
                    
                    if save and level != ConfigLevel.RUNTIME:
                        self._save_config(level)
                    
                    self._notify_change(key, None, level, deleted=True)
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"خطأ في حذف {key}: {e}")
            return False
    
    def get_all(self, level: Optional[ConfigLevel] = None) -> Dict[str, Any]:
        """الحصول على جميع الإعدادات"""
        if level:
            return self.configs[level].copy()
        
        # دمج جميع المستويات
        merged = {}
        for search_level in [ConfigLevel.SYSTEM, ConfigLevel.USER, 
                           ConfigLevel.PROJECT, ConfigLevel.RUNTIME]:
            merged.update(self.configs[search_level])
        
        return merged
    
    def reset_to_defaults(self, level: ConfigLevel = ConfigLevel.USER):
        """إعادة تعيين الإعدادات للقيم الافتراضية"""
        try:
            with self.lock:
                # إنشاء إعدادات افتراضية
                defaults = {}
                for schema_item in self.DEFAULT_SCHEMA:
                    defaults[schema_item.key] = schema_item.default
                
                self.configs[level] = defaults
                
                if level != ConfigLevel.RUNTIME:
                    self._save_config(level)
                
                self.logger.info(f"تم إعادة تعيين إعدادات {level.value}")
                return True
                
        except Exception as e:
            self.logger.error(f"خطأ في إعادة التعيين: {e}")
            return False
    
    def backup_config(self, backup_path: Optional[Path] = None) -> Path:
        """إنشاء نسخة احتياطية من الإعدادات"""
        if not backup_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = self.config_dir / f"backup_{timestamp}.json"
        
        try:
            all_configs = {}
            for level in ConfigLevel:
                if level != ConfigLevel.RUNTIME:
                    all_configs[level.value] = self.configs[level]
            
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(all_configs, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"تم إنشاء نسخة احتياطية: {backup_path}")
            return backup_path
            
        except Exception as e:
            self.logger.error(f"خطأ في النسخ الاحتياطي: {e}")
            raise ConfigError(f"فشل في النسخ الاحتياطي: {e}")
    
    def restore_config(self, backup_path: Path):
        """استعادة الإعدادات من نسخة احتياطية"""
        try:
            with open(backup_path, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            with self.lock:
                for level_name, config_data in backup_data.items():
                    level = ConfigLevel(level_name)
                    if level != ConfigLevel.RUNTIME:
                        self.configs[level] = config_data
                        self._save_config(level)
            
            self.logger.info(f"تم استعادة الإعدادات من: {backup_path}")
            
        except Exception as e:
            self.logger.error(f"خطأ في الاستعادة: {e}")
            raise ConfigError(f"فشل في الاستعادة: {e}")
    
    def add_change_callback(self, callback: Callable):
        """إضافة callback لمراقبة التغييرات"""
        self.change_callbacks.append(callback)
    
    def _notify_change(self, key: str, value: Any, level: ConfigLevel, deleted: bool = False):
        """إشعار المراقبين بالتغييرات"""
        for callback in self.change_callbacks:
            try:
                callback(key, value, level, deleted)
            except Exception as e:
                self.logger.error(f"خطأ في callback التغيير: {e}")
    
    def _encrypt_sensitive_data(self, data: Dict) -> Dict:
        """تشفير البيانات الحساسة"""
        # تشفير بسيط - في التطبيق الحقيقي استخدم مكتبة تشفير قوية
        encrypted_data = data.copy()
        
        for key, schema_item in self.schema.items():
            if schema_item.sensitive and key in encrypted_data:
                value = str(encrypted_data[key])
                encoded = base64.b64encode(value.encode()).decode()
                encrypted_data[key] = f"encrypted:{encoded}"
        
        return encrypted_data
    
    def _decrypt_sensitive_data(self, data: Dict) -> Dict:
        """فك تشفير البيانات الحساسة"""
        decrypted_data = data.copy()
        
        for key, value in decrypted_data.items():
            if isinstance(value, str) and value.startswith("encrypted:"):
                try:
                    encoded = value[10:]  # إزالة "encrypted:"
                    decoded = base64.b64decode(encoded).decode()
                    decrypted_data[key] = decoded
                except Exception as e:
                    self.logger.error(f"خطأ في فك تشفير {key}: {e}")
        
        return decrypted_data
    
    def get_schema(self) -> Dict[str, ConfigSchema]:
        """الحصول على مخطط الإعدادات"""
        return self.schema.copy()
    
    def add_schema_item(self, schema_item: ConfigSchema):
        """إضافة عنصر جديد للمخطط"""
        self.schema[schema_item.key] = schema_item
    
    def validate_all_configs(self) -> List[str]:
        """التحقق من صحة جميع الإعدادات"""
        errors = []
        
        for level in ConfigLevel:
            for key, value in self.configs[level].items():
                try:
                    self._validate_value(key, value)
                except ValidationError as e:
                    errors.append(f"{level.value}.{key}: {e.message}")
        
        return errors
