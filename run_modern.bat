@echo off
chcp 65001 >nul
title Python to EXE Converter Pro v2.0 - Modern Glass UI

echo.
echo ========================================
echo   🚀 Python to EXE Converter Pro v2.0
echo   ✨ Modern Glass UI - واجهة زجاجية عصرية
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت أو غير موجود في PATH
    echo 💡 يرجى تثبيت Python من: https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: pip غير متوفر
    echo 💡 يرجى إعادة تثبيت Python مع pip
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على pip

REM التحقق من وجود PyInstaller
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo.
    echo ⚠️  PyInstaller غير مثبت
    echo 🔄 جاري تثبيت PyInstaller...
    echo.
    pip install PyInstaller
    if errorlevel 1 (
        echo ❌ فشل في تثبيت PyInstaller
        echo 💡 جرب تشغيل الأمر يدوياً: pip install PyInstaller
        echo.
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت PyInstaller بنجاح
)

REM التحقق من وجود الملفات المطلوبة
if not exist "modern_pyinstaller_gui.py" (
    echo ❌ خطأ: ملف modern_pyinstaller_gui.py غير موجود
    echo 💡 تأكد من وجود جميع ملفات التطبيق في نفس المجلد
    echo.
    pause
    exit /b 1
)

echo.
echo 🚀 بدء تشغيل التطبيق العصري...
echo ✨ Modern Glass UI مع تأثيرات متقدمة
echo.

REM تشغيل التطبيق العصري
python modern_pyinstaller_gui.py

REM في حالة حدوث خطأ
if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل التطبيق
    echo 💡 تحقق من رسائل الخطأ أعلاه
    echo.
    pause
)

echo.
echo 👋 شكراً لاستخدام Python to EXE Converter Pro - Modern Glass UI
pause
