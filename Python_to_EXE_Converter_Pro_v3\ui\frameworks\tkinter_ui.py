#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python to EXE Converter Pro v3.0 - Tkinter UI Framework
واجهة Tkinter المحسنة

واجهة Tkinter متقدمة مع:
- تصميم عصري وجذاب
- ثيمات متعددة
- مكونات مخصصة
- تأثيرات بصرية
- تجربة مستخدم محسنة
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import time
from typing import Dict, Any, Optional, Callable
from pathlib import Path

from .. import UIManager, UIComponent, Theme
from ...core.logger import Logger
from ...core.converter import PyToExeConverter, ConversionOptions, ConversionMode
from ...core.config_manager import ConfigManager

class ModernButton(UIComponent):
    """زر عصري مخصص"""
    
    def __init__(self, name: str, parent, text: str, command: Optional[Callable] = None):
        super().__init__(name, parent)
        self.text = text
        self.command = command
        self.widget = None
        self.style_name = f"{name}.TButton"
    
    def create(self):
        """إنشاء الزر"""
        self.widget = ttk.Button(
            self.parent,
            text=self.text,
            command=self.command,
            style=self.style_name
        )
        return self.widget
    
    def update(self, **kwargs):
        """تحديث الزر"""
        if 'text' in kwargs:
            self.text = kwargs['text']
            self.widget.configure(text=self.text)
        if 'command' in kwargs:
            self.command = kwargs['command']
            self.widget.configure(command=self.command)
        if 'state' in kwargs:
            self.widget.configure(state=kwargs['state'])
    
    def destroy(self):
        """تدمير الزر"""
        if self.widget:
            self.widget.destroy()
    
    def apply_theme(self, theme: Theme):
        """تطبيق الثيم"""
        # سيتم تطبيق الثيم من خلال ttk.Style
        pass

class ModernProgressBar(UIComponent):
    """شريط تقدم عصري"""
    
    def __init__(self, name: str, parent):
        super().__init__(name, parent)
        self.widget = None
        self.value = 0
        self.maximum = 100
    
    def create(self):
        """إنشاء شريط التقدم"""
        self.widget = ttk.Progressbar(
            self.parent,
            mode='determinate',
            maximum=self.maximum
        )
        return self.widget
    
    def update(self, **kwargs):
        """تحديث شريط التقدم"""
        if 'value' in kwargs:
            self.value = kwargs['value']
            self.widget['value'] = self.value
        if 'maximum' in kwargs:
            self.maximum = kwargs['maximum']
            self.widget['maximum'] = self.maximum
    
    def destroy(self):
        """تدمير شريط التقدم"""
        if self.widget:
            self.widget.destroy()

class ModernTextArea(UIComponent):
    """منطقة نص عصرية"""
    
    def __init__(self, name: str, parent, width: int = 80, height: int = 20):
        super().__init__(name, parent)
        self.width = width
        self.height = height
        self.widget = None
        self.scrollbar = None
    
    def create(self):
        """إنشاء منطقة النص"""
        # إطار للنص والتمرير
        frame = tk.Frame(self.parent)
        
        # منطقة النص
        self.widget = tk.Text(
            frame,
            width=self.width,
            height=self.height,
            wrap=tk.WORD,
            font=('Consolas', 10),
            bg='#1e1e1e',
            fg='#ffffff',
            insertbackground='#ffffff',
            selectbackground='#404040'
        )
        
        # شريط التمرير
        self.scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.widget.yview)
        self.widget.configure(yscrollcommand=self.scrollbar.set)
        
        # ترتيب العناصر
        self.widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        return frame
    
    def update(self, **kwargs):
        """تحديث منطقة النص"""
        if 'text' in kwargs:
            self.widget.delete('1.0', tk.END)
            self.widget.insert('1.0', kwargs['text'])
        if 'append' in kwargs:
            self.widget.insert(tk.END, kwargs['append'])
            self.widget.see(tk.END)
    
    def destroy(self):
        """تدمير منطقة النص"""
        if self.widget:
            self.widget.destroy()
        if self.scrollbar:
            self.scrollbar.destroy()
    
    def get_text(self) -> str:
        """الحصول على النص"""
        return self.widget.get('1.0', tk.END)
    
    def clear(self):
        """مسح النص"""
        self.widget.delete('1.0', tk.END)

class TkinterUIManager(UIManager):
    """مدير واجهة Tkinter"""
    
    def __init__(self, theme: Theme = Theme.DARK):
        super().__init__(framework='tkinter', theme=theme)
        self.root = None
        self.style = None
        self.logger = Logger("TkinterUI")
        self.config_manager = ConfigManager()
        self.converter = PyToExeConverter(self.logger)
        
        # متغيرات الواجهة
        self.source_file = tk.StringVar()
        self.output_dir = tk.StringVar()
        self.icon_file = tk.StringVar()
        self.conversion_mode = tk.StringVar(value="onefile")
        self.windowed = tk.BooleanVar()
        self.debug_mode = tk.BooleanVar()
        
        # مكونات الواجهة
        self.progress_bar = None
        self.log_area = None
        self.status_label = None
        
        # حالة التحويل
        self.is_converting = False
    
    def initialize(self):
        """تهيئة النظام"""
        if self.is_initialized:
            return
        
        self.root = tk.Tk()
        self.style = ttk.Style()
        
        # تطبيق الثيم
        self._apply_theme()
        
        # تحميل الإعدادات
        self._load_settings()
        
        self.is_initialized = True
        self.logger.info("تم تهيئة واجهة Tkinter")
    
    def create_main_window(self, title: str, width: int, height: int):
        """إنشاء النافذة الرئيسية"""
        if not self.is_initialized:
            self.initialize()
        
        self.root.title(title)
        self.root.geometry(f"{width}x{height}")
        self.root.minsize(800, 600)
        
        # توسيط النافذة
        self._center_window(width, height)
        
        # إنشاء الواجهة
        self._create_interface()
        
        self.main_window = self.root
        self.logger.info(f"تم إنشاء النافذة الرئيسية: {title}")
    
    def _center_window(self, width: int, height: int):
        """توسيط النافذة"""
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def _apply_theme(self):
        """تطبيق الثيم"""
        if self.theme == Theme.DARK:
            self._apply_dark_theme()
        elif self.theme == Theme.LIGHT:
            self._apply_light_theme()
        elif self.theme == Theme.GLASS:
            self._apply_glass_theme()
        else:
            self._apply_dark_theme()  # افتراضي
    
    def _apply_dark_theme(self):
        """تطبيق الثيم الداكن"""
        colors = {
            'bg': '#1e1e1e',
            'fg': '#ffffff',
            'select_bg': '#404040',
            'select_fg': '#ffffff',
            'button_bg': '#2d2d2d',
            'button_fg': '#ffffff',
            'entry_bg': '#2d2d2d',
            'entry_fg': '#ffffff'
        }
        
        self.root.configure(bg=colors['bg'])
        
        # تكوين الأنماط
        self.style.theme_use('clam')
        
        # أزرار
        self.style.configure('TButton',
                           background=colors['button_bg'],
                           foreground=colors['button_fg'],
                           borderwidth=1,
                           focuscolor='none')
        
        self.style.map('TButton',
                      background=[('active', '#404040'),
                                ('pressed', '#505050')])
        
        # حقول الإدخال
        self.style.configure('TEntry',
                           fieldbackground=colors['entry_bg'],
                           foreground=colors['entry_fg'],
                           borderwidth=1)
        
        # التسميات
        self.style.configure('TLabel',
                           background=colors['bg'],
                           foreground=colors['fg'])
        
        # الإطارات
        self.style.configure('TFrame',
                           background=colors['bg'])
    
    def _apply_light_theme(self):
        """تطبيق الثيم الفاتح"""
        colors = {
            'bg': '#ffffff',
            'fg': '#000000',
            'button_bg': '#f0f0f0',
            'button_fg': '#000000',
            'entry_bg': '#ffffff',
            'entry_fg': '#000000'
        }
        
        self.root.configure(bg=colors['bg'])
        self.style.theme_use('default')
    
    def _apply_glass_theme(self):
        """تطبيق الثيم الزجاجي"""
        # ثيم زجاجي متقدم
        self._apply_dark_theme()  # أساس داكن
        
        # إضافة تأثيرات زجاجية
        self.root.attributes('-alpha', 0.95)
    
    def _load_settings(self):
        """تحميل الإعدادات"""
        # تحميل إعدادات النافذة
        width = self.config_manager.get('ui.window_width', 1400)
        height = self.config_manager.get('ui.window_height', 900)
        
        # تحميل إعدادات التحويل
        default_mode = self.config_manager.get('conversion.default_mode', 'onefile')
        self.conversion_mode.set(default_mode)
        
        # تحميل مسار الإخراج الافتراضي
        default_output = self.config_manager.get('conversion.default_output_dir', 'dist')
        self.output_dir.set(default_output)
    
    def _create_interface(self):
        """إنشاء واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title_label = ttk.Label(
            main_frame,
            text="🚀 Python to EXE Converter Pro v3.0",
            font=('Arial', 16, 'bold')
        )
        title_label.pack(pady=(0, 20))
        
        # قسم الملفات
        self._create_file_section(main_frame)
        
        # قسم الخيارات
        self._create_options_section(main_frame)
        
        # قسم التحويل
        self._create_conversion_section(main_frame)
        
        # قسم السجل
        self._create_log_section(main_frame)
        
        # شريط الحالة
        self._create_status_bar(main_frame)
    
    def _create_file_section(self, parent):
        """إنشاء قسم الملفات"""
        files_frame = ttk.LabelFrame(parent, text="📁 الملفات", padding="10")
        files_frame.pack(fill=tk.X, pady=(0, 10))
        
        # ملف المصدر
        ttk.Label(files_frame, text="ملف Python:").grid(row=0, column=0, sticky=tk.W, pady=5)
        source_entry = ttk.Entry(files_frame, textvariable=self.source_file, width=50)
        source_entry.grid(row=0, column=1, padx=(10, 5), pady=5, sticky=tk.EW)
        
        browse_btn = ModernButton("browse_source", files_frame, "تصفح", self._browse_source_file)
        browse_btn.create().grid(row=0, column=2, padx=5, pady=5)
        
        # مجلد الإخراج
        ttk.Label(files_frame, text="مجلد الإخراج:").grid(row=1, column=0, sticky=tk.W, pady=5)
        output_entry = ttk.Entry(files_frame, textvariable=self.output_dir, width=50)
        output_entry.grid(row=1, column=1, padx=(10, 5), pady=5, sticky=tk.EW)
        
        output_btn = ModernButton("browse_output", files_frame, "تصفح", self._browse_output_dir)
        output_btn.create().grid(row=1, column=2, padx=5, pady=5)
        
        # ملف الأيقونة
        ttk.Label(files_frame, text="أيقونة (اختياري):").grid(row=2, column=0, sticky=tk.W, pady=5)
        icon_entry = ttk.Entry(files_frame, textvariable=self.icon_file, width=50)
        icon_entry.grid(row=2, column=1, padx=(10, 5), pady=5, sticky=tk.EW)
        
        icon_btn = ModernButton("browse_icon", files_frame, "تصفح", self._browse_icon_file)
        icon_btn.create().grid(row=2, column=2, padx=5, pady=5)
        
        # تكوين الأعمدة
        files_frame.columnconfigure(1, weight=1)
    
    def _create_options_section(self, parent):
        """إنشاء قسم الخيارات"""
        options_frame = ttk.LabelFrame(parent, text="⚙️ خيارات التحويل", padding="10")
        options_frame.pack(fill=tk.X, pady=(0, 10))
        
        # نمط التحويل
        ttk.Label(options_frame, text="نمط التحويل:").grid(row=0, column=0, sticky=tk.W, pady=5)
        
        mode_frame = ttk.Frame(options_frame)
        mode_frame.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        ttk.Radiobutton(mode_frame, text="ملف واحد", variable=self.conversion_mode, 
                       value="onefile").pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(mode_frame, text="مجلد", variable=self.conversion_mode, 
                       value="onedir").pack(side=tk.LEFT)
        
        # خيارات إضافية
        ttk.Checkbutton(options_frame, text="نافذة بدون وحدة تحكم", 
                       variable=self.windowed).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=5)
        ttk.Checkbutton(options_frame, text="وضع التصحيح", 
                       variable=self.debug_mode).grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=5)
    
    def _create_conversion_section(self, parent):
        """إنشاء قسم التحويل"""
        conversion_frame = ttk.LabelFrame(parent, text="🔄 التحويل", padding="10")
        conversion_frame.pack(fill=tk.X, pady=(0, 10))
        
        # زر التحويل
        self.convert_btn = ModernButton("convert", conversion_frame, "🚀 بدء التحويل", self._start_conversion)
        self.convert_btn.create().pack(side=tk.LEFT, padx=(0, 10))
        
        # زر الإلغاء
        self.cancel_btn = ModernButton("cancel", conversion_frame, "❌ إلغاء", self._cancel_conversion)
        cancel_widget = self.cancel_btn.create()
        cancel_widget.pack(side=tk.LEFT, padx=(0, 10))
        cancel_widget.configure(state=tk.DISABLED)
        
        # شريط التقدم
        self.progress_bar = ModernProgressBar("progress", conversion_frame)
        progress_widget = self.progress_bar.create()
        progress_widget.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
    
    def _create_log_section(self, parent):
        """إنشاء قسم السجل"""
        log_frame = ttk.LabelFrame(parent, text="📝 سجل العمليات", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # منطقة السجل
        self.log_area = ModernTextArea("log", log_frame, width=80, height=15)
        log_widget = self.log_area.create()
        log_widget.pack(fill=tk.BOTH, expand=True)
        
        # أزرار السجل
        log_buttons_frame = ttk.Frame(log_frame)
        log_buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        clear_btn = ModernButton("clear_log", log_buttons_frame, "مسح السجل", self._clear_log)
        clear_btn.create().pack(side=tk.LEFT)
        
        save_btn = ModernButton("save_log", log_buttons_frame, "حفظ السجل", self._save_log)
        save_btn.create().pack(side=tk.LEFT, padx=(10, 0))
    
    def _create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X)
        
        self.status_label = ttk.Label(status_frame, text="جاهز للتحويل")
        self.status_label.pack(side=tk.LEFT)
        
        # معلومات الإصدار
        version_label = ttk.Label(status_frame, text="v3.0.0")
        version_label.pack(side=tk.RIGHT)
    
    def _browse_source_file(self):
        """تصفح ملف المصدر"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف Python",
            filetypes=[("Python files", "*.py"), ("All files", "*.*")]
        )
        if file_path:
            self.source_file.set(file_path)
            self._log_message(f"📄 تم اختيار الملف: {Path(file_path).name}")
    
    def _browse_output_dir(self):
        """تصفح مجلد الإخراج"""
        dir_path = filedialog.askdirectory(title="اختر مجلد الإخراج")
        if dir_path:
            self.output_dir.set(dir_path)
            self._log_message(f"📁 تم اختيار مجلد الإخراج: {dir_path}")
    
    def _browse_icon_file(self):
        """تصفح ملف الأيقونة"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف الأيقونة",
            filetypes=[("Icon files", "*.ico"), ("Image files", "*.png;*.jpg;*.jpeg"), ("All files", "*.*")]
        )
        if file_path:
            self.icon_file.set(file_path)
            self._log_message(f"🎨 تم اختيار الأيقونة: {Path(file_path).name}")
    
    def _start_conversion(self):
        """بدء عملية التحويل"""
        if self.is_converting:
            return
        
        # التحقق من الملف المصدر
        if not self.source_file.get():
            messagebox.showerror("خطأ", "يرجى اختيار ملف Python")
            return
        
        if not Path(self.source_file.get()).exists():
            messagebox.showerror("خطأ", "الملف المصدر غير موجود")
            return
        
        # إعداد خيارات التحويل
        options = ConversionOptions(
            source_file=self.source_file.get(),
            output_dir=self.output_dir.get() or "dist",
            icon_file=self.icon_file.get() if self.icon_file.get() else None,
            mode=ConversionMode.ONEFILE if self.conversion_mode.get() == "onefile" else ConversionMode.ONEDIR,
            windowed=self.windowed.get(),
            debug=self.debug_mode.get()
        )
        
        # بدء التحويل في thread منفصل
        self.is_converting = True
        self._update_ui_state(converting=True)
        
        def conversion_thread():
            try:
                result = self.converter.convert(options, self._on_progress_update)
                self.root.after(0, lambda: self._on_conversion_complete(result))
            except Exception as e:
                self.root.after(0, lambda: self._on_conversion_error(str(e)))
        
        threading.Thread(target=conversion_thread, daemon=True).start()
    
    def _cancel_conversion(self):
        """إلغاء التحويل"""
        if self.is_converting:
            self.converter.cancel_conversion()
            self.is_converting = False
            self._update_ui_state(converting=False)
            self._log_message("❌ تم إلغاء التحويل")
    
    def _on_progress_update(self, stage: str, progress: int):
        """تحديث التقدم"""
        self.root.after(0, lambda: self._update_progress(stage, progress))
    
    def _update_progress(self, stage: str, progress: int):
        """تحديث شريط التقدم"""
        self.progress_bar.update(value=progress)
        self.status_label.configure(text=f"{stage} - {progress}%")
        self._log_message(f"⏳ {stage}: {progress}%")
    
    def _on_conversion_complete(self, result: Dict[str, Any]):
        """اكتمال التحويل"""
        self.is_converting = False
        self._update_ui_state(converting=False)
        
        output_file = result.get('output_file', '')
        self._log_message(f"✅ تم التحويل بنجاح: {output_file}")
        
        messagebox.showinfo("نجح التحويل", f"تم إنشاء الملف:\n{output_file}")
    
    def _on_conversion_error(self, error: str):
        """خطأ في التحويل"""
        self.is_converting = False
        self._update_ui_state(converting=False)
        
        self._log_message(f"❌ خطأ في التحويل: {error}")
        messagebox.showerror("خطأ في التحويل", error)
    
    def _update_ui_state(self, converting: bool):
        """تحديث حالة الواجهة"""
        state = tk.DISABLED if converting else tk.NORMAL
        cancel_state = tk.NORMAL if converting else tk.DISABLED
        
        self.convert_btn.update(state=state)
        self.cancel_btn.update(state=cancel_state)
        
        if not converting:
            self.progress_bar.update(value=0)
            self.status_label.configure(text="جاهز للتحويل")
    
    def _log_message(self, message: str):
        """إضافة رسالة للسجل"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        if self.log_area:
            self.log_area.update(append=formatted_message)
    
    def _clear_log(self):
        """مسح السجل"""
        if self.log_area:
            self.log_area.clear()
    
    def _save_log(self):
        """حفظ السجل"""
        if not self.log_area:
            return
        
        file_path = filedialog.asksaveasfilename(
            title="حفظ السجل",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_area.get_text())
                self._log_message(f"💾 تم حفظ السجل: {file_path}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ السجل: {e}")
    
    def run(self):
        """تشغيل حلقة الأحداث"""
        if not self.is_initialized:
            raise RuntimeError("يجب تهيئة النظام أولاً")
        
        self.logger.info("بدء تشغيل واجهة Tkinter")
        self.root.mainloop()
    
    def shutdown(self):
        """إغلاق النظام"""
        if self.root:
            # حفظ الإعدادات
            self._save_settings()
            
            # إغلاق النافذة
            self.root.quit()
            self.root.destroy()
            
            self.logger.info("تم إغلاق واجهة Tkinter")
    
    def _save_settings(self):
        """حفظ الإعدادات"""
        # حفظ حجم النافذة
        geometry = self.root.geometry()
        if 'x' in geometry:
            size_part = geometry.split('+')[0]
            if 'x' in size_part:
                width, height = map(int, size_part.split('x'))
                self.config_manager.set('ui.window_width', width)
                self.config_manager.set('ui.window_height', height)
        
        # حفظ إعدادات التحويل
        self.config_manager.set('conversion.default_mode', self.conversion_mode.get())
        self.config_manager.set('conversion.default_output_dir', self.output_dir.get())
